# 依赖目录
node_modules/
.next/cache/

# 构建输出
.next/
out/
dist/
build/

# 环境文件（在构建时通过 --build-arg 或 docker-compose 传入）
.env*
!.env.example

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 运行时数据
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# 测试相关
coverage/
.nyc_output/

# 调试
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 系统文件
.DS_Store
Thumbs.db

# 本地开发文件
.turbo/
.vercel/

# 文档
README.md
CHANGELOG.md

# 编辑器目录和文件
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 其他
.cache/
.temp/
.tmp/

# 本地数据库文件
*.sqlite
*.sqlite3

# 但需要保留必要的配置文件
!next.config.*
!tailwind.config.*
!postcss.config.*
!tsconfig.json
!jsconfig.json
!package.json
!package-lock.json
!yarn.lock
!pnpm-lock.yaml

# 保留必要的根目录文件
!public/
!prisma/
