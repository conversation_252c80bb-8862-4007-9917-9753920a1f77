1. 使用 SSR 提升 SEO。
2. 使用别名 `@/*` 替代相对路径。
3. 单个文件不超过 500 行，函数/组件不超过 50 行；超限时拆分模块。
4. 删除无用代码并复用组件、函数和常量。
5. 优化响应速度与数据库查询。
6. 使用 `const` 声明常量，避免 `enum` 和魔法字符串。
7. 使用 JSDoc/TSDoc 进行注释。
8. 实现响应式设计以适配所有设备。
9. 避免使用 `any`。
10. 使用异步 I/O 以避免阻塞。
11. 使用 pnpm 管理包。
12. UI 库优先使用 `shadcn/ui` 和 `tailwindcss`。
13. 数据请求优先使用 `fetch` 或 `swr`。
14. 语言使用 TypeScript 和 React Hooks。
15. `/admin` 后台管理系统页面设计和功能逻辑应独立。
16. `/admin` 数据请求需使用接口并进行安全验证。
17. `/admin` 可以使用 CSR。
