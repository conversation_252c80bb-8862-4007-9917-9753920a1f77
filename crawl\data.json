[{"category": "AI智能体", "title": "扣子空间", "imgUrl": "d08976d8a68059d4.png", "desc": "全面免费开放，提供专业AI Agent服务", "sourceLink": "https://ai-bot.cn/coze-space?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "星辰Agent", "imgUrl": "f685ce82900f53cf.jpg", "desc": "科大讯飞推出的AI智能体开发平台", "sourceLink": "https://agent.xfyun.cn/home?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON><PERSON>", "imgUrl": "6be5a6bd6abda0cd.png", "desc": "Monica团队推出的全球首款通用型 AI Agent", "sourceLink": "https://ai-bot.cn/manus?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "Operator", "imgUrl": "1f1cf461a105ffc7.png", "desc": "OpenAI推出的AI智能体，能推理、联网自主执行任务", "sourceLink": "https://ai-bot.cn/operator?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON><PERSON>", "imgUrl": "56c01744c25ef1b4.png", "desc": "全球首款通用型 AI Agent 开源项目", "sourceLink": "https://ai-bot.cn/suna?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON><PERSON>", "imgUrl": "1a4effc75bc80adc.png", "desc": "画布式AI智能体工具", "sourceLink": "https://flowith.net/invitation?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON>a", "imgUrl": "b47a5034c6692dc3.png", "desc": "Arc 团队推出的 AI 原生浏览器", "sourceLink": "https://ai-bot.cn/dia?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "CoCo", "imgUrl": "112ddde51dba5d83.png", "desc": "智谱推出的首个企业级超级助手Agent", "sourceLink": "https://ai-bot.cn/aiworker-coco?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "Skywork", "imgUrl": "2f1248639adccfcf.png", "desc": "昆仑万维面向全球推出的天工超级智能体", "sourceLink": "https://ai-bot.cn/skywork?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "0aabcca20c196cdf.png", "desc": "首个专业AI设计Agent", "sourceLink": "https://ai-bot.cn/lovart?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "Jaa<PERSON>", "imgUrl": "f42389b1b1f868a5.png", "desc": "开源的AI设计Agent，本地免费Lovart平替项目", "sourceLink": "https://ai-bot.cn/jaaz?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "Fairies", "imgUrl": "e35c901a8b84e763.png", "desc": "通用型AI Agent，强大的多任务执行能力", "sourceLink": "https://ai-bot.cn/fairies?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "AutoGLM沉思", "imgUrl": "dec5b0801c521873.png", "desc": "首个免费、具备深度研究和操作能力的AI Agent", "sourceLink": "https://ai-bot.cn/autoglm-research?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "Genspark", "imgUrl": "930712f0dc14795d.png", "desc": "基于智能体的AI搜索引擎", "sourceLink": "https://ai-bot.cn/genspark?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "<PERSON><PERSON>", "imgUrl": "8551269fa65cae2b.png", "desc": "Fellou AI 推出的首个Agentic浏览器", "sourceLink": "https://ai-bot.cn/fellou?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "CRIC深度智联", "imgUrl": "589f905d6f51d233.jpg", "desc": "房地产行业首个AI Agent", "sourceLink": "https://ai-bot.cn/dichanai?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "MGX", "imgUrl": "774d6d782193e13d.png", "desc": "基于MetaGPT框架的AI编程智能体", "sourceLink": "https://ai-bot.cn/mgx?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "rabbitOS intern", "imgUrl": "ab419322891d24c6.jpg", "desc": "rabbit 推出的通用型AI智能体", "sourceLink": "https://ai-bot.cn/rabbitos-intern?_channel=ai-nav-hnch"}, {"category": "AI智能体", "title": "MiniMax Agent", "imgUrl": "f71641802dbfb21a.png", "desc": "MiniMax推出的通用型AI Agent", "sourceLink": "https://ai-bot.cn/minimax-agent?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "朱雀AI检测", "imgUrl": "54732f415272eb02.png", "desc": "腾讯推出的AI图像和文本鉴别工具", "sourceLink": "https://ai-bot.cn/tencent-ai-detect?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "GPTZero", "imgUrl": "e25952f0662f2e18.png", "desc": "超过百万人都在用的免费AI内容检测工具", "sourceLink": "https://gptzero.me/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "StudyCorgi ChatGPT Detector", "imgUrl": "7162a88f0034c547.png", "desc": "免费的检测论文是否由ChatGPT生成的工具", "sourceLink": "https://studycorgi.com/free-writing-tools/chat-gpt-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "AISEO AI Content Detector", "imgUrl": "c3c585162d6ef75f.png", "desc": "AISEO推出的AI内容检测器", "sourceLink": "https://aiseo.ai/tools/ai-content-detector.html?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "06e11e30acef454f.png", "desc": "AI检测科研图像是否造假抄袭", "sourceLink": "https://www.proofig.com/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Writecream AI Content Detector", "imgUrl": "5bed6e6b7b05c674.png", "desc": "Writecream推出的AI内容检测工具", "sourceLink": "https://www.writecream.com/ai-content-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Smodin AI Content Detector", "imgUrl": "c2e706281fd024ab.png", "desc": "多语种AI内容检测工具", "sourceLink": "https://smodin.io/ai-content-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Sapling AI Content Detector", "imgUrl": "60b11b793ca6f141.png", "desc": "Sapling.ai推出的免费在线AI内容检测工具", "sourceLink": "https://sapling.ai/utilities/ai-content-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "GPT Detector", "imgUrl": "8393049ca52f5c90.png", "desc": "在线检查文本是否由GPT-3或ChatGPT生成", "sourceLink": "https://x.writefull.com/gpt-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "挖错网", "imgUrl": "3b75d2ccb011f370.png", "desc": "AI内容审核校对平台，一键检测内容自动纠错", "sourceLink": "https://wacuowang.com/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Mitata AI", "imgUrl": "8ab7018a21d9743d.png", "desc": "专业的AI文章检测工具，能识别文章是否由AI生成", "sourceLink": "https://www.copyleaks.top/login?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "AI Content Detector", "imgUrl": "301bd558d398af60.png", "desc": "Writer推出的AI内容检测工具", "sourceLink": "https://writer.com/ai-content-detector?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Originality.AI", "imgUrl": "c40c25a2208146f3.png", "desc": "原创度和AI内容检测", "sourceLink": "https://originality.ai/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "CopyLeaks", "imgUrl": "31bffab9fb0cc4dc.png", "desc": "AI内容检测和分级", "sourceLink": "https://copyleaks.com/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "Winston AI", "imgUrl": "0797775b022f5b00.png", "desc": "强大的AI内容检测解决方案", "sourceLink": "https://gowinston.ai/?_channel=ai-nav-hnch"}, {"category": "AI内容检测", "title": "CheckforAI", "imgUrl": "00aaed4beb2225cb.png", "desc": "免费在线检测AI内容", "sourceLink": "https://checkforai.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "沉浸式翻译", "imgUrl": "adfcb34735ba843a.jpg", "desc": "双语对照的网页翻译插件，完全免费 口碑炸裂！", "sourceLink": "https://immersivetranslate.com/zh-<PERSON>/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "会译", "imgUrl": "a5f5cef97f554ac3.png", "desc": "沉浸对照式AI翻译，支持100+语言", "sourceLink": "https://huiyiai.net/hy_api/activity/invite?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "腾讯交互翻译", "imgUrl": "4be60c29491487d5.png", "desc": "腾讯推出的多语言AI翻译工具", "sourceLink": "https://transmart.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "DeepTranslate", "imgUrl": "cfde18a75fe74a5c.png", "desc": "免费的AI双语翻译器，支持142+种语言", "sourceLink": "https://deeptranslate.ai/zh-CN?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "Google翻译", "imgUrl": "43983684e458bc52.png", "desc": "Google免费提供的上百种语言智能翻译工具", "sourceLink": "https://translate.google.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "必应翻译", "imgUrl": "8deb72edc088ed11.png", "desc": "微软必应推出的翻译工具", "sourceLink": "https://www.bing.com/translator?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "DeepL翻译", "imgUrl": "8ae10c7a71d440e5.png", "desc": "准确的人工智能翻译工具", "sourceLink": "https://www.deepl.com/translator?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "<PERSON>", "imgUrl": "815619cf7906b400.png", "desc": "AI实时语音翻译工具，专为视频通话设计", "sourceLink": "https://www.trytoby.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "火山翻译", "imgUrl": "bd1eb8ee5c0f1b64.png", "desc": "字节跳动推出的智能翻译工具", "sourceLink": "https://translate.volcengine.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "百度翻译", "imgUrl": "5214a080f45bfec1.png", "desc": "200种语言互译、沟通全世界！", "sourceLink": "https://fanyi.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "阿里翻译", "imgUrl": "7d5af980a70406a7.png", "desc": "阿里巴巴达摩院推出的多领域多语种的在线机器翻译", "sourceLink": "https://translate.alibaba.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "搜狗翻译", "imgUrl": "b5a2a1c8d086241e.png", "desc": "你的贴身智能翻译专家", "sourceLink": "https://fanyi.sogou.com/text?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "腾讯翻译君", "imgUrl": "11db7a4fc4d325b6.png", "desc": "你免费的随身翻译", "sourceLink": "https://fanyi.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "象寄翻译", "imgUrl": "adbbdf97abbed204.png", "desc": "AI图片和视频翻译神器，支持多种语言的翻译", "sourceLink": "https://www.xiangjifanyi.com/console/register-invite/741be04feb30bf16?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "网易见外", "imgUrl": "051f25c4f7a01bbf.png", "desc": "网易推出的AI智能翻译平台，支持音视频、文档、图片、字幕等翻译", "sourceLink": "https://sight.youdao.com/?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "讯飞智能翻译", "imgUrl": "6bd73b7520ca19f1.png", "desc": "科大讯飞推出的人工智能翻译平台", "sourceLink": "https://fanyi.xfyun.cn/console/trans/text?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "彩云小译", "imgUrl": "a957cc7788b9ccf2.png", "desc": "兼具中日英同声传译、文档翻译和网页翻译的智能翻译工具", "sourceLink": "https://fanyi.caiyunapp.com/?_channel=ai-nav-hnch#"}, {"category": "AI语言翻译", "title": "百度AI同传助手", "imgUrl": "b49ede3c27217cc7.png", "desc": "中英文音视频同传字幕工具", "sourceLink": "https://fanyi.baidu.com/appdownload/download.html?_channel=ai-nav-hnch"}, {"category": "AI语言翻译", "title": "金山快译", "imgUrl": "5d6d5325022f58fa.png", "desc": "金山WPS推出的在线翻译平台", "sourceLink": "https://kuaiyi.wps.cn/txt-translate?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "元典智库", "imgUrl": "fa65df7d09bf6986.png", "desc": "智能法律知识服务平台和搜索引擎", "sourceLink": "https://www.chineselaw.com/tyjs/index?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "通义法睿", "imgUrl": "dd9af078c949637c.png", "desc": "阿里推出的免费AI法律顾问助手", "sourceLink": "https://tongyi.aliyun.com/farui?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "法行宝", "imgUrl": "f0ad682ecf9d34ef.png", "desc": "百度推出的免费AI法律助手", "sourceLink": "https://ailegal.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "ChatLaw", "imgUrl": "1afb47d1fde42f63.png", "desc": "北大开源的法律大模型和助手", "sourceLink": "https://chatlaw.cloud/lawchat/?_channel=ai-nav-hnch#/?utm_source=ai-bot.cn"}, {"category": "AI法律助手", "title": "得理法搜", "imgUrl": "306a260a7ca8c1ed.png", "desc": "AI法律智能检索系统", "sourceLink": "https://www.delilegal.com/search?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "法智", "imgUrl": "64828dbb809faa40.png", "desc": "同花顺推出的AI法律助手", "sourceLink": "https://www.fazhi.law/?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "海瑞智法", "imgUrl": "e82a967f255f2715.png", "desc": "一站式AI法律咨询助手", "sourceLink": "https://www.hairuilegal.com/?_channel=ai-nav-hnch"}, {"category": "AI法律助手", "title": "合同嗖嗖", "imgUrl": "a82a734a2a5c2836.png", "desc": "专业的AI法律合同生成工具", "sourceLink": "https://contract.yoo-ai.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "提示工程指南", "imgUrl": "42a7ce93ec10728f.png", "desc": "提示工程指南（Prompt Engine...", "sourceLink": "https://www.promptingguide.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "PromptPerfect", "imgUrl": "73c1d7d0f9b33a58.png", "desc": "PromptPerfect 是一款专业好...", "sourceLink": "https://promptperfect.jinaai.cn/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "FlowGPT", "imgUrl": "c0a846582d15a600.png", "desc": "FlowGPT", "sourceLink": "https://flowgpt.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Stable Diffusion Prompt Book", "imgUrl": "70e2c9defba216da.png", "desc": "Stable Diffusion Prompt Book", "sourceLink": "https://openart.ai/promptbook?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "PromptHero", "imgUrl": "5f0bf30745209ca5.png", "desc": "PromptHero", "sourceLink": "https://prompthero.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "ClickPrompt", "imgUrl": "46320321c6ebe71d.png", "desc": "ClickPrompt", "sourceLink": "https://www.clickprompt.org/zh-CN?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "AI Prompt Generator", "imgUrl": "04f11ff0afd4184c.png", "desc": "AI Prompt Generator是什么 A...", "sourceLink": "https://www.aipromptgenerator.net/zh?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "AI Short", "imgUrl": "aadb1cd1c30f21ce.png", "desc": "AI Short是什么 AI Short是一...", "sourceLink": "https://www.aishort.top/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "LangGPT", "imgUrl": "409593fd8a5be7f1.png", "desc": "LangGPT是什么 LangGPT是一种...", "sourceLink": "https://github.com/langgptai/LangGPT?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Generrated", "imgUrl": "fa83b6dae40fbfc7.png", "desc": "当你第一次开始使用DALL•E 2...", "sourceLink": "https://generrated.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "PublicPrompts", "imgUrl": "326c130f059223dc.png", "desc": "PublicPrompts是什么 PublicP...", "sourceLink": "https://publicprompts.art/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Snack Prompt", "imgUrl": "47adbf3dc4cfa7f5.png", "desc": "输入正确的提示词，可以让Cha...", "sourceLink": "https://snackprompt.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "AIPRM", "imgUrl": "70a3c2955861d796.png", "desc": "AIPRM", "sourceLink": "https://www.aiprm.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "绘AI", "imgUrl": "7fe5429b39b18fd1.png", "desc": "绘AI致力于探索和创造一种全...", "sourceLink": "https://www.ai016.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "MJ Prompt Tool", "imgUrl": "f7a4272b03dec088.png", "desc": "MJ Prompt Tool", "sourceLink": "https://prompt.noonshot.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Visual Prompt Builder", "imgUrl": "13c9cf78b4a217d6.png", "desc": "Visual Prompt Builder", "sourceLink": "https://tools.saxifrage.xyz/prompt?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "ChatGPT Prompt Genius", "imgUrl": "06f47050ebcedaf7.png", "desc": "ChatGPT Prompt Genius", "sourceLink": "https://github.com/benf2004/ChatGPT-Prompt-Genius?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "PromptBase", "imgUrl": "28f02f2220069993.png", "desc": "PromptBase", "sourceLink": "https://promptbase.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "PromptVine", "imgUrl": "08ba8fc5df43f233.png", "desc": "PromptVine", "sourceLink": "https://promptvine.com/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Awesome ChatGPT Prompts", "imgUrl": "a3a84215cf9e7c78.png", "desc": "Awesome ChatGPT Prompts", "sourceLink": "https://prompts.chat/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "Learning Prompt", "imgUrl": "ea0fe9c99c82d806.png", "desc": "Learning Prompt", "sourceLink": "https://learningprompt.wiki/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "ChatGPT Shortcut", "imgUrl": "21af90e5470c25ed.png", "desc": "ChatGPT Shortcut是国内开发...", "sourceLink": "https://www.aishort.top/?_channel=ai-nav-hnch"}, {"category": "AI提示指令", "title": "词魂", "imgUrl": "84ff9a3640408181.png", "desc": "词魂是一个AIGC精品提示词库...", "sourceLink": "https://icihun.com/?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "MMLU", "imgUrl": "85cdaf60e18ffbf2.png", "desc": "大规模多任务语言理解基准", "sourceLink": "https://paperswithcode.com/sota/multi-task-language-understanding-on-mmlu?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "Open LLM Leaderboard", "imgUrl": "29cdb0a5d8cc3469.png", "desc": "Hugging Face推出的开源大模型排行榜单", "sourceLink": "https://huggingface.co/spaces/HuggingFaceH4/open_llm_leaderboard?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "C-Eval", "imgUrl": "b300eaa0e293fb22.png", "desc": "一个全面的中文基础模型评估套件", "sourceLink": "https://cevalbenchmark.com/static/leaderboard_zh.html?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "FlagEval", "imgUrl": "4acb7efff355c353.png", "desc": "智源研究院推出的FlagEval（天秤）大模型评测平台", "sourceLink": "https://flageval.baai.ac.cn/?_channel=ai-nav-hnch#/trending"}, {"category": "AI模型评测", "title": "SuperCLUE", "imgUrl": "6c47eb07f939e27d.png", "desc": "中文通用大模型综合性测评基准", "sourceLink": "https://www.cluebenchmarks.com/static/superclue.html?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "AGI-Eval", "imgUrl": "e122cc64a97b0f9e.png", "desc": "AI大模型评测社区", "sourceLink": "https://agi-eval.cn/mvp/home?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "OpenCompass", "imgUrl": "dbff5e157139249a.png", "desc": "上海人工智能实验室推出的大模型开放评测体系", "sourceLink": "https://opencompass.org.cn/leaderboard-llm?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "CMMLU", "imgUrl": "ca151e0857795fba.png", "desc": "一个综合性的大模型中文评估基准", "sourceLink": "https://github.com/haonan-li/CMMLU?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "3bb242d01ebfb1b8.png", "desc": "全方位的多模态大模型能力评测体系", "sourceLink": "https://mmbench.opencompass.org.cn/leaderboard?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "HELM", "imgUrl": "ff0e6f1b8eef86a8.png", "desc": "斯坦福大学推出的大模型评测体系", "sourceLink": "https://crfm.stanford.edu/helm/latest?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "Chatbot Arena", "imgUrl": "66f6e628aae4c5f8.png", "desc": "以众包方式进行匿名随机对战的LLM基准平台", "sourceLink": "https://chat.lmsys.org/?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "LLMEval3", "imgUrl": "fa81a85c96adbffd.png", "desc": "由复旦大学NLP实验室推出的大模型评测基准", "sourceLink": "http://llmeval.com/index?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "H2O EvalGPT", "imgUrl": "6c024edb5c135a90.png", "desc": "H2O.ai推出的基于Elo评级方法的大模型评估系统", "sourceLink": "https://evalgpt.ai/?_channel=ai-nav-hnch"}, {"category": "AI模型评测", "title": "PubMedQA", "imgUrl": "0b8c30d23347a7dd.png", "desc": "生物医学研究问答数据集和模型得分排行榜", "sourceLink": "https://pubmedqa.github.io/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "AI大学堂", "imgUrl": "36bc31f3620d4c35.png", "desc": "科大讯飞推出的在线AI学习平台", "sourceLink": "https://www.aidaxue.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "OpenAI Academy", "imgUrl": "2e05a5f9939768a7.png", "desc": "OpenAI 推出的免费 AI 学习平台", "sourceLink": "https://ai-bot.cn/openai-academy?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Day of AI", "imgUrl": "2d480b516c0e2811.png", "desc": "麻省理工学院（MIT）推出的免费AI学习平台", "sourceLink": "https://dayofai.org/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "fast.ai", "imgUrl": "ae449ba51e5ff05b.png", "desc": "免费开源的深度学习和AI学习网站，让每个人都参与到AI！", "sourceLink": "https://www.fast.ai/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Coursera", "imgUrl": "c5535ec0dab75e31.png", "desc": "知名MOOC平台，众多人工智能和机器学习课程", "sourceLink": "https://www.coursera.org/collections/best-machine-learning-ai?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Elements of AI", "imgUrl": "d894d35d34264471.png", "desc": "免费在线AI通识学习", "sourceLink": "https://www.elementsofai.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "DeepLearning.AI", "imgUrl": "8eb659125a961593.png", "desc": "深度学习和人工智能学习平台", "sourceLink": "https://www.deeplearning.ai/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "动手学深度学习", "imgUrl": "f771174cdbfc3842.png", "desc": "动手学深度学习的教材和课程", "sourceLink": "https://zh.d2l.ai/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "MachineLearningMastery", "imgUrl": "22ac72101e165df4.png", "desc": "免费在线学习机器学习，从基础到高级", "sourceLink": "https://machinelearningmastery.com/start-here?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Generative AI for Beginners", "imgUrl": "c11f5861eb1cd661.png", "desc": "微软推出的面向初学者的免费生成式人工智能课程", "sourceLink": "https://microsoft.github.io/generative-ai-for-beginners/?_channel=ai-nav-hnch#/translation/cn"}, {"category": "AI学习网站", "title": "ML for Beginners", "imgUrl": "e19c96bc04d61b4c.png", "desc": "微软推出的免费开源的机器学习课程，GitHub标星4万+", "sourceLink": "https://microsoft.github.io/ML-For-Beginners?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "<PERSON><PERSON>", "imgUrl": "ee8e285a52409cab.png", "desc": "机器学习和数据科学社区", "sourceLink": "https://www.kaggle.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "神经网络入门", "imgUrl": "fcfe0a2840a6a3f3.png", "desc": "Brilliant推出的Introduction to Neural Networks课程", "sourceLink": "https://brilliant.org/courses/intro-neural-networks?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Trancy", "imgUrl": "f8fbf2af0bb74550.png", "desc": "AI驱动的语言学习工具", "sourceLink": "https://trancy.org/zh-cn?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Reading Coach", "imgUrl": "58169048b86d4025.png", "desc": "微软推出的免费个性化AI阅读学习教练", "sourceLink": "https://coach.microsoft.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "飞桨AI Studio", "imgUrl": "9cfa018c183f6f47.png", "desc": "百度推出的AI学习与实训社区", "sourceLink": "https://aistudio.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "腾讯扣叮", "imgUrl": "16835072a0c800a3.png", "desc": "腾讯推出的青少年编程教育平台", "sourceLink": "https://coding.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "阿里云AI学习路线", "imgUrl": "578995bee07e737e.png", "desc": "阿里云推出的人工智能学习路线（学+测）", "sourceLink": "https://developer.aliyun.com/learning/roadmap/ai?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Udacity AI学院", "imgUrl": "fbaaa171e0d5a587.png", "desc": "Udacity推出的School of AI，从入门到高级", "sourceLink": "https://www.udacity.com/school/school-of-ai?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "Google AI", "imgUrl": "d2dce3b4906ff7a8.png", "desc": "Google AI学习平台", "sourceLink": "https://ai.google/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "ShowMeAI知识社区", "imgUrl": "7ce8489c152bc15c.png", "desc": "人工智能领域的资料库和学习社区", "sourceLink": "https://www.showmeai.tech/?_channel=ai-nav-hnch"}, {"category": "AI学习网站", "title": "txyz", "imgUrl": "bad81651c21bd4b3.png", "desc": "AI文献阅读和学术研究辅助平台", "sourceLink": "https://www.txyz.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "吐司AI", "imgUrl": "0dd50ffb9e259b62.png", "desc": "AI绘画模型社区和在线生图平台", "sourceLink": "https://ai-bot.cn/tusiart?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "绘蛙", "imgUrl": "4f48c7f48ab06d0d.png", "desc": "AI电商营销工具，免费生成商品图和种草文案", "sourceLink": "https://ihuiwa.paluai.com/aibot?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "稿定AI", "imgUrl": "64711e0ee714e34c.png", "desc": "一站式AI设计工具集，免费AI绘图、图片转AI绘画、AI抠图消除 ", "sourceLink": "https://www.gaoding.com/utms/68f2b6d26cdb4048adf21904798c7229?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "LiblibAI·哩布哩布AI", "imgUrl": "5e7b0db07f6dd663.png", "desc": "国内领先的AI图像创作平台和模型分享社区", "sourceLink": "https://www.liblib.art/viphome?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "阿贝智能", "imgUrl": "4bae1802386a0a5c.jpg", "desc": "一站式AI绘本创作平台，副业变现必备", "sourceLink": "https://abeiai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "即梦", "imgUrl": "39abf49dbfa90a46.png", "desc": "抖音旗下免费AI图片创作工具", "sourceLink": "https://jimeng.paluai.com/ai-tool?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Midjourney", "imgUrl": "60e6a4cd9d9847fe.png", "desc": "AI图像和插画生成工具", "sourceLink": "https://www.midjourney.com/home?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Stable Diffusion", "imgUrl": "0e34a17e4726e143.png", "desc": "StabilityAI推出的文本到图像生成AI", "sourceLink": ""}, {"category": "AI图片无损放大", "title": "Civitai", "imgUrl": "0e80bed698baf437.png", "desc": "免费的AI图像绘画作品和模型分享平台和社区", "sourceLink": "https://civitai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "堆友AI反应堆", "imgUrl": "df580c789fbd639d.png", "desc": "阿里旗下堆友推出的多风格AI绘画生成器", "sourceLink": "https://d.design/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "炉米Lumi", "imgUrl": "499ba5110d54bc5d.png", "desc": "字节跳动推出的AIGC图像创作平台", "sourceLink": "https://ai-bot.cn/lumi?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "可灵AI", "imgUrl": "153544f6f2fa9f7f.png", "desc": "快手推出的AI图像和视频创作平台", "sourceLink": "https://app.klingai.com/cn/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "AI改图神器", "imgUrl": "0c8217967076033c.png", "desc": "AI万能图片在线编辑器", "sourceLink": "https://img.logosc.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "通义万相", "imgUrl": "eea05fc5df3def00.png", "desc": "阿里最新推出的AI绘画创作模型", "sourceLink": "https://tongyi.aliyun.com/wanxiang/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "秒画", "imgUrl": "a81ebeda0dadcfff.png", "desc": "商汤科技推出的免费AI作画和图片生成平台", "sourceLink": "https://miaohua.sensetime.com/inspiration?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "WHEE", "imgUrl": "c65fdf8feff99062.png", "desc": "美图推出的AI图片和绘画创作生成平台", "sourceLink": "https://www.whee.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "奇域AI", "imgUrl": "56a71c73c2a9ca9c.png", "desc": "中式审美国风AI绘画创作平台", "sourceLink": "https://www.qiyuai.net/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "触手AI绘画", "imgUrl": "71ce3025ccdd0438.png", "desc": "免费专业的AI绘画/模型/分享平台", "sourceLink": "https://acgnai.art/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "造梦日记", "imgUrl": "0b95c7b3280a4397.png", "desc": "AI一下，妙笔生画", "sourceLink": "https://zmrj.art/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Canva AI图像生成", "imgUrl": "9a18ac91bbb30ffc.png", "desc": "在线设计工具Canva推出的AI图像生成工具", "sourceLink": ""}, {"category": "AI图片无损放大", "title": "Krea AI", "imgUrl": "63c96b4634288ff6.png", "desc": "实时AI图像、视频生成和编辑平台", "sourceLink": "https://www.krea.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Photoroom", "imgUrl": "3c3437f1530637be.png", "desc": "在线AI图片编辑工具", "sourceLink": "https://www.photoroom.com/zh?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Ribbet.ai", "imgUrl": "aa5b2022c856a3b5.png", "desc": "免费的多功能AI图片处理工具箱", "sourceLink": "https://ribbet.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "万相营造", "imgUrl": "ddac09a59d187b38.png", "desc": "阿里旗下推出的多模态AI创意生成平台", "sourceLink": "https://agi.taobao.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "悟空图像PhotoSir", "imgUrl": "c096bbfdbb7844b0.png", "desc": "新一代专业图像处理软件，更智能、更高效、更好用", "sourceLink": "https://www.photosir.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "360智图", "imgUrl": "f108e45b19bbef0a.png", "desc": "360推出的AI作图平台，支持智能抠图、智能消除、智能放大、智能配图", "sourceLink": "https://chacha.so.com/home?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "像素蛋糕", "imgUrl": "201f99d6a89ab2fe.png", "desc": "像甜科技推出的AI图像后期软件", "sourceLink": "https://www.pixcakeai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "remove.bg", "imgUrl": "277a6c6a34405c35.png", "desc": "强大的AI图片背景移除工具", "sourceLink": "https://www.remove.bg/zh?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "如果相机", "imgUrl": "94a132ad96db93b2.png", "desc": "仅需1张照片，快速生成AI写真照片", "sourceLink": "https://ifshot.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "ARC", "imgUrl": "be1ea9807c8b716c.png", "desc": "腾讯旗下ARC实验室推出的免费AI图片处理工具", "sourceLink": "https://arc.tencent.com/zh/ai-demos?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "吐司AI高清", "imgUrl": "0dd50ffb9e259b62.png", "desc": "吐司AI推出的图片变高清/修复工具", "sourceLink": "https://tusiart.com/template/820721890405622530?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "稿定AI变清晰", "imgUrl": "64711e0ee714e34c.png", "desc": "稿定设计推出的AI变清晰图像处理工具", "sourceLink": "https://www.gaoding.com/utms/2a5760f7f0d74446b2c8ef68a34d5bfc?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "美图无损放大", "imgUrl": "ffc94c71acb684e8.jpg", "desc": "美图设计室推出的AI图片变清晰工具", "sourceLink": "https://www.designkit.com/upscale/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Upscayl", "imgUrl": "6147138d1ff16870.png", "desc": "免费开源的AI图片无损放大工具", "sourceLink": "https://www.upscayl.org/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "BigJPG", "imgUrl": "d9248e5e789821dc.png", "desc": "免费的在线图片无损放大工具", "sourceLink": "https://bigjpg.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "107fffb72a346155.png", "desc": "AI图像放大增强工具，快速放大至10倍或12K分辨率", "sourceLink": "https://mejorarimagen.org/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Magnific AI", "imgUrl": "da30793a3e8d5f99.png", "desc": "强大的AI图像放大工具，最高支持到10K分辨率", "sourceLink": "https://magnific.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Let’s Enhance", "imgUrl": "831814f3cafd8b8e.png", "desc": "AI在线免费放大图片并保持图像质量", "sourceLink": "https://letsenhance.io/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Icons8 Smart Upscaler", "imgUrl": "f09c14a78d9886e5.png", "desc": "Icons8出品的AI图片无损放大工具", "sourceLink": "https://icons8.com/upscaler?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "GoProd", "imgUrl": "7f96b626507ab6f4.png", "desc": "Icons8推出的智能图片背景移除和无损放大二合一Mac应用", "sourceLink": "https://icons8.com/goprod?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "ClipDrop Image Upscaler", "imgUrl": "aacbfaf05bd1a210.png", "desc": "ClipDrop出品的AI图片放大工具", "sourceLink": "https://clipdrop.co/image-upscaler?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "<PERSON>m<PERSON><PERSON>", "imgUrl": "ce5dd85a231b027f.png", "desc": "免费的AI图片放大工具", "sourceLink": "https://imgupscaler.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Fotor AI Image Upscaler", "imgUrl": "090181c1124c0b75.png", "desc": "Fotor推出的AI图片放大工具", "sourceLink": "https://www.fotor.com/image-upscaler?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Zyro AI Image Upscaler", "imgUrl": "f3e938d93c48dcf1.png", "desc": "Zyro出品的人工智能图片放大工具", "sourceLink": "https://zyro.com/tools/image-upscaler?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Media.io AI Image Upscaler", "imgUrl": "43dda7a0e739142c.png", "desc": "Media.io推出的AI图片放大工具", "sourceLink": "https://www.media.io/image-upscaler.html?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Upscale.media", "imgUrl": "f6e772df2ca4ee75.png", "desc": "AI图片放大和分辨率修改", "sourceLink": "https://www.upscale.media/zh?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Nero Image Upscaler", "imgUrl": "2bcade0db6fe4359.png", "desc": "AI免费图片无损放大", "sourceLink": "https://ai.nero.com/image-upscaler?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "VanceAI Image Resizer", "imgUrl": "5f365eec1aed30f1.png", "desc": "VanceAI推出的在线图片尺寸调整工具", "sourceLink": "https://vanceai.com/image-resizer?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "PhotoAid Image Upscaler", "imgUrl": "947510424f37e3cb.png", "desc": "PhotoAid出品的免费在线人工智能图片放大工具", "sourceLink": "https://photoaid.com/en/tools/ai-image-enlarger?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Upscalepics", "imgUrl": "acd4e8d629c2393f.png", "desc": "在线图片放大工具", "sourceLink": "https://upscalepics.com/?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Image Enlarger", "imgUrl": "b8cdad40f56153ad.png", "desc": "AI无损放大图片", "sourceLink": "https://magicstudio.com/zh/enlarger?_channel=ai-nav-hnch"}, {"category": "AI图片无损放大", "title": "Pixelhunter", "imgUrl": "7fdb66b8f3b7750d.png", "desc": "AI智能调整图片尺寸用于社交媒体平台发帖", "sourceLink": "https://pixelhunter.io/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "Facet", "imgUrl": "de28f88d3ce9743d.png", "desc": "AI图片修图和优化工具", "sourceLink": "https://facet.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "Relight", "imgUrl": "d38bb9cdca0a15a2.png", "desc": "ClipDrop推出的AI图像打光工具", "sourceLink": "https://clipdrop.co/relight?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "美图云修", "imgUrl": "b77a1d7abcd31030.png", "desc": "美图秀秀推出的一站式AI智能修图软件", "sourceLink": "https://yunxiu.meitu.com/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "<PERSON><PERSON>", "imgUrl": "4b139c296ecdb19f.png", "desc": "AI智能将模糊照片变高清的图像修复工具", "sourceLink": "https://remini.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "jpgHD", "imgUrl": "6a45c6be1fbbf6db.png", "desc": "人工智能老照片上色和修复", "sourceLink": "https://jpghd.com/zh?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "像素蛋糕PixCake", "imgUrl": "5b661505bcee9220.png", "desc": "简单易用的AI图像精修工具", "sourceLink": "https://www.pixcakeai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "咻图AI", "imgUrl": "69b3ac8c90d81d64.png", "desc": "面向影楼的摄影后期AI修图软件", "sourceLink": "https://www.aixtsy.com/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "restorePhotos.io", "imgUrl": "a767e3b70257fab7.png", "desc": "AI老照片修复", "sourceLink": "https://www.restorephotos.io/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "PicMa Studio", "imgUrl": "8d6a750c3993bcf0.png", "desc": "AI一键批量增强、修复、彩色化你的照片", "sourceLink": "https://picma.magictiger.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "transpic", "imgUrl": "592b7bd63d680712.png", "desc": "AI图像转绘插画创作平台", "sourceLink": "https://transpic.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "Cutout.Pro老照片上色", "imgUrl": "5075e42714be9511.png", "desc": "Cutout.Pro推出的黑白图片上色", "sourceLink": "https://www.cutout.pro/zh-CN/photo-colorizer-black-and-white?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "Palette", "imgUrl": "c3ddf776d43efa8e.png", "desc": "AI图片调色上色", "sourceLink": "https://palette.fm/?_channel=ai-nav-hnch"}, {"category": "AI图片优化修复", "title": "Playground AI", "imgUrl": "0e46465fc59ee8c4.png", "desc": "AI图片生成和修图", "sourceLink": "https://playgroundai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "绘蛙AI消除", "imgUrl": "4f48c7f48ab06d0d.png", "desc": "免费AI修图工具，一键去除图片中多余元素", "sourceLink": "https://www.ihuiwa.com/workspace/ai-image/partial-redraw?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "吐司AI消除", "imgUrl": "0dd50ffb9e259b62.png", "desc": "吐司AI推出的AI智能消除、去杂物工具", "sourceLink": "https://tusiart.com/template/820385276638620489?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "美图AI消除", "imgUrl": "ffc94c71acb684e8.jpg", "desc": "美图设计室推出的AI消除工具", "sourceLink": "https://www.designkit.com/aieraser/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "稿定AI消除", "imgUrl": "64711e0ee714e34c.png", "desc": "稿定设计推出的AI图像杂物消除工具", "sourceLink": "https://www.gaoding.com/utms/d23b495450f54b78a16f2aff38131606?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "<PERSON><PERSON>", "imgUrl": "568816e0fbdfb1eb.png", "desc": "在线抹除图片中不想要的物体", "sourceLink": "https://www.hama.app/zh?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "f05829637eb58a2c.png", "desc": "免费开源的AI图像擦除、修复和处理工具", "sourceLink": "https://www.iopaint.com/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "Bg Eraser", "imgUrl": "bb4c1c0505e75695.png", "desc": "图片物体抹除和清理", "sourceLink": "https://bgeraser.com/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "SnapEdit", "imgUrl": "93d9e238a344a960.png", "desc": "AI移除图片中的任何物体", "sourceLink": "https://snapedit.app/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "Cleanup.pictures", "imgUrl": "4d2ed633a596b418.png", "desc": "智能移除图片中的物体、文本、污迹、人物或任何不想要的东西", "sourceLink": "https://cleanup.pictures/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "美间AI消除", "imgUrl": "84327019c00ee86e.png", "desc": "AI智能消除工具，轻松消除杂物、水印等", "sourceLink": "https://www.meijian.com/mj-box/ai-eliminate-intro?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "Cutout.Pro Retouch", "imgUrl": "5075e42714be9511.png", "desc": "Cutout.Pro推出的AI图片物体涂抹去除工具", "sourceLink": "https://www.cutout.pro/zh-CN/image-retouch-remove-unwanted-objects?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "蜜蜂剪辑", "imgUrl": "0db7e60e1c5fe1a7.png", "desc": "AI去水印工具，支持图片和30+流行短视频平台", "sourceLink": "https://beecut.cn/online-watermark-remover?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "HitPaw Watermark Remover", "imgUrl": "19d0b0d31b31b01a.png", "desc": "AI图片和视频去水印工具", "sourceLink": "https://www.hitpaw.com/remove-watermark.html?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "Magic Eraser", "imgUrl": "b9abd1204346cdbd.png", "desc": "AI移除图片中不想要的物体", "sourceLink": "https://magicstudio.com/zh/magiceraser/?_channel=ai-nav-hnch"}, {"category": "AI图片物体抹除", "title": "WatermarkRemover", "imgUrl": "6bb874a57b292b86.jpg", "desc": "AI智能删除照片中的水印", "sourceLink": "https://www.watermarkremover.io/zh?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "稿定AI商品图", "imgUrl": "64711e0ee714e34c.png", "desc": "稿定设计推出的AI商品图生成工具", "sourceLink": "https://www.gaoding.com/utms/d0bf3af869c245eabb7f52bbcc85aa88?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "美图商拍", "imgUrl": "ffc94c71acb684e8.jpg", "desc": "美图设计室推出的AI商拍工具", "sourceLink": "https://www.designkit.com/product-shoot/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "<PERSON><PERSON>", "imgUrl": "3880324fe1846432.png", "desc": "阿里国际推出的AI电商营销工具", "sourceLink": "https://www.piccopilot.com/create/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "蜂鸟AI", "imgUrl": "53eb24a5b37821b4.png", "desc": "赶海科技推出的跨境电商AI营销工具", "sourceLink": "https://fengniaoai.com/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "PhotoStudio AI", "imgUrl": "5d3767eb03119e99.png", "desc": "虹软旗下推出的AI商拍工具", "sourceLink": "https://psai.cn/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "Pippit", "imgUrl": "1925fcff35a97104.png", "desc": "字节旗下 CapCut 推出的AI营销内容创作平台", "sourceLink": "https://ai-bot.cn/pippit-ai?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "Poify", "imgUrl": "78fe91097a0d603a.png", "desc": "快手推出的AI电商营销工具", "sourceLink": "https://ai-bot.cn/poify?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "妙思", "imgUrl": "014ca12598d5c44b.png", "desc": "腾讯广告推出的一站式AI广告创意平台", "sourceLink": "https://admuse.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "Cliclic AI", "imgUrl": "f0a36175d951d4b0.png", "desc": "创客贴推出的AI商品图生成和编辑工具", "sourceLink": "https://www.cliclic.ai/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "羚珑", "imgUrl": "3445743b941defc9.png", "desc": "京东推出的商品图智能设计小工具", "sourceLink": "https://ling.jd.com/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "LinkFox AI", "imgUrl": "08a1d567e350a750.jpg", "desc": "AI电商设计工具，快速生成专业电商图", "sourceLink": "https://ai-bot.cn/linkfox-ai?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "灵动AI", "imgUrl": "27992ce2e629c25d.png", "desc": "专业的AI商品图生成工具", "sourceLink": "https://www.redoon.cn/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "创自由", "imgUrl": "835738dfd1390390.png", "desc": "快速设计商品图、广告图的AI作图工具", "sourceLink": "https://chuangziyou.com/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "PhotoMagic", "imgUrl": "1e40d41b1c2bc4a8.png", "desc": "AI快速生成商拍图片", "sourceLink": "https://www.photomagic.cn/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "美间AI商拍", "imgUrl": "84327019c00ee86e.png", "desc": "AI电商设计工具，一键生成高质量商品图", "sourceLink": "https://www.meijian.com/e-commerce?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "P<PERSON><PERSON>ly", "imgUrl": "4f78c6c621b4d281.png", "desc": "AI产品图精美背景添加", "sourceLink": "https://pebblely.com/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "Mokker AI", "imgUrl": "11ccd2a52af1036c.png", "desc": "AI产品图添加背景", "sourceLink": "https://mokker.ai/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "花生图像", "imgUrl": "3f44e5ad1665ee28.png", "desc": "AI电商产品图生成和背景抠图工具", "sourceLink": "https://www.hsphoto.cn/?_channel=ai-nav-hnch"}, {"category": "AI商品图生成", "title": "图生生", "imgUrl": "0604df06ba721ed4.png", "desc": "专为电商设计的AI商拍工具", "sourceLink": "https://tushengsheng.com/home/<USER>"}, {"category": "AI商品图生成", "title": "WeShop唯象", "imgUrl": "eb64a831a8160fd1.png", "desc": "蘑菇街推出的AI商拍工具", "sourceLink": "https://www.weshop.com/?_channel=ai-nav-hnch"}, {"category": "AI 3D模型生成", "title": "Tripo AI", "imgUrl": "da8d1623a1064e61.png", "desc": "AI 3D模型生成平台，支持文本、图像一键生成3D模型", "sourceLink": "https://ai-bot.cn/tripo-ai?_channel=ai-nav-hnch"}, {"category": "AI 3D模型生成", "title": "腾讯混元3D", "imgUrl": "3e1e55d4e847abd1.png", "desc": "腾讯推出的一站式3D内容生产AI创作平台", "sourceLink": "https://3d.hunyuan.tencent.com/?_channel=ai-nav-hnch"}, {"category": "AI 3D模型生成", "title": "Hitems", "imgUrl": "fceeaa659e3b6d6c.png", "desc": "数美万物推出的AI创意物品生成社区", "sourceLink": "https://hitems.ai/?_channel=ai-nav-hnch"}, {"category": "AI 3D模型生成", "title": "VoxCraft", "imgUrl": "4b1f2ee936369a93.png", "desc": "AI生成3D模型的工具", "sourceLink": "https://voxcraft.ai/?_channel=ai-nav-hnch"}, {"category": "AI 3D模型生成", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "5e5eb1594208bcd8.png", "desc": "AI快速从文本或图像生成3D模型", "sourceLink": "https://www.meshy.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "AiPPT", "imgUrl": "f62d7ca50f4abd66.png", "desc": "AI快速生成高质量PPT", "sourceLink": "https://www.aippt.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "咔片PPT", "imgUrl": "835e554d782115fd.png", "desc": "AI PPT制作工具，设计美化全流程自动化", "sourceLink": "https://www.cappt.cc/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "讯飞智文", "imgUrl": "da7e55f08ce2b444.png", "desc": "一键生成PPT和Word", "sourceLink": "https://zhiwen.xfyun.cn/home?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "文多多AiPPT", "imgUrl": "0ed1ab9894a0a27b.png", "desc": "AI一键生成PPT，支持AI配图和智能资料整合", "sourceLink": "https://docmee.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "笔格AIPPT", "imgUrl": "b3e8ac190f4545db.png", "desc": "高效的AI PPT生成工具", "sourceLink": "https://www.bigppt.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "iSlide AIPPT", "imgUrl": "f099c8b4afbeaaad.png", "desc": "AI一键设计精美PPT，只需一句标题", "sourceLink": "https://www.islide.cc/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "博思AIPPT", "imgUrl": "57f5fd256345e62d.png", "desc": "博思云创推出的在线AI生成PPT工具", "sourceLink": "https://pptgo.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "稿定PPT", "imgUrl": "64711e0ee714e34c.png", "desc": "稿定推出的PPT模板资源库", "sourceLink": "https://www.gaoding.com/templates-ppt?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "笔灵AIPPT", "imgUrl": "13f4078f66b2e390.png", "desc": "一键生成PPT和千字演讲稿", "sourceLink": "https://ibiling.cn/ppt-zone?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Gamma", "imgUrl": "4bfa1abcf3a3c48e.png", "desc": "AI幻灯片演示生成工具", "sourceLink": "https://gamma.app/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "清言PPT", "imgUrl": "262bb4e72f3d2e27.jpg", "desc": "智谱清言联合AiPPT推出的PPT生成智能体", "sourceLink": "https://ai-bot.cn/qingyan-ppt?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "百度文库AI助手", "imgUrl": "536884a8ec875c97.png", "desc": "基于文心一言的一站式智能文档助手", "sourceLink": "https://wenku.baidu.com/ndlaunch/browse/chat?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "539f3f96bf5f8cbe.png", "desc": "将文本内容快速转换成演示图像的AI办公工具", "sourceLink": "https://www.napkin.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "PicDoc", "imgUrl": "464f9d4ebe379656.png", "desc": "AI文本转图表工具，一键生成多种视觉图表", "sourceLink": "https://www.picdoc.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "VisDoc", "imgUrl": "001ed5493a82c23c.png", "desc": "AI文生图表工具，支持生成柱状图、折线图、饼图等", "sourceLink": "https://www.visdoc.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Pi", "imgUrl": "6551df3e8138ef2d.png", "desc": "AI演示文档创作与共享平台", "sourceLink": "https://ai-bot.cn/pi?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "万知", "imgUrl": "68a640121a987634.png", "desc": "零一万物推出的一站式AI文档阅读和PPT创作工作台", "sourceLink": "https://www.wanzhi01.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "美图AI PPT", "imgUrl": "ffc94c71acb684e8.jpg", "desc": "美图秀秀推出的免费在线AI生成PPT设计工具", "sourceLink": "https://www.designkit.com/ppt/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "万兴智演", "imgUrl": "0293b27b9d51508e.png", "desc": "万兴科技推出的AI PPT和演示制作软件", "sourceLink": "https://zhiyan.wondershare.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "歌者AI", "imgUrl": "e7fe69cae4861816.png", "desc": "彩漩PPT推出的AI PPT生成工具", "sourceLink": "https://gezhe.caixuan.cc/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "ChatBA", "imgUrl": "b1cf1ecb6db31735.png", "desc": "AI幻灯片生成工具", "sourceLink": "https://www.chatba.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Decktopus AI", "imgUrl": "54d491e8bf5a7f15.png", "desc": "AI驱动的的在线演示文稿生成器", "sourceLink": "https://www.decktopus.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Powerpresent AI", "imgUrl": "1e073697bdde75f2.png", "desc": "AI创建精美的演示稿", "sourceLink": "https://powerpresent.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "希沃白板", "imgUrl": "4e610a325b733eaa.png", "desc": "专为互动教学设计的AI课件生成器", "sourceLink": "https://easinote.seewo.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "秒出PPT", "imgUrl": "5037e9c09b5315f5.png", "desc": "一键生成PPT，智能辅助编辑", "sourceLink": "https://10sppt.com/pptx/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "GAIPPT", "imgUrl": "b7488153561a2854.png", "desc": "AI智能美化PPT工具，上传PPT一键美化", "sourceLink": "https://www.gaippt.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "beautiful.ai", "imgUrl": "61bee2e9a22035e3.png", "desc": "AI创建展示幻灯片", "sourceLink": "https://www.beautiful.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "麦当秀MindShow", "imgUrl": "82e08365714f5a7b.png", "desc": "AI在线PPT制作工具，支持Markdown等多种格式", "sourceLink": "https://www.mindshow.vip/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "ChatPPT", "imgUrl": "62a603ce1a9de601.png", "desc": "AI一键对话生成PPT，智能排版美化", "sourceLink": "https://www.chat-ppt.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "轻竹办公", "imgUrl": "5e701d49810f6160.png", "desc": "在线智能生成和设计PPT的AI工具", "sourceLink": "https://qzoffice.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "办公小浣熊", "imgUrl": "55485b5e42ce64ae.png", "desc": "最强AI数据分析助手", "sourceLink": "https://www.xiaohuanxiong.com/login?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "酷表ChatExcel", "imgUrl": "512c573af4cc2d7b.png", "desc": "AI Excel 数据分析辅助工具", "sourceLink": "https://www.chatexcel.com/?_channel=ai-nav-hnch#/home?partner_uuid=FDB2E7F66AAB36C88AE9ECEC03EBB43A"}, {"category": "AI表格数据处理", "title": "vika维格云", "imgUrl": "917f86a19db784cf.png", "desc": "智能多维表格和数据生产力平台", "sourceLink": "https://vika.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "百度GBI", "imgUrl": "4e342bebc2d73eba.png", "desc": "百度推出的全球商业智能平台", "sourceLink": "https://gbi.cloud.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "82a7a8f88adf6b65.png", "desc": "处理Excel和Google Sheets表格的AI工具", "sourceLink": "https://ajelix.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Sheet+", "imgUrl": "386112e60ddb74d9.png", "desc": "Excel和Google Sheets表格AI处理工具", "sourceLink": "https://sheetplus.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "轻云图", "imgUrl": "a20ad5752d63d99e.png", "desc": "必优科技推出的AI一键生成可视化云图工具", "sourceLink": "https://cloud.yoo-ai.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "北极九章", "imgUrl": "3f04327d2362cd0f.png", "desc": "北极数据推出的AI数据分析平台", "sourceLink": "https://datarc.cn/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Formula bot", "imgUrl": "27fb27a4207d0b7b.png", "desc": "AI将指令转换成Excel的函数公式", "sourceLink": "https://excelformulabot.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "FormX.ai", "imgUrl": "132f431d7d3f0736.png", "desc": "AI自动从表格和文档中提取数据", "sourceLink": "https://www.formx.ai/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Rows", "imgUrl": "4ca8b4b2a9414ce8.png", "desc": "集成了AI功能的在线表格处理工具", "sourceLink": "https://rows.com/?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Excelly-AI", "imgUrl": "a3bf7bb6954d8b59.png", "desc": "将文本转换成Excel或Google Sheets公式", "sourceLink": "https://excelly-ai.io/index.html?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "SheetGod", "imgUrl": "009735bfa0dc9bf9.png", "desc": "BoloForms推出的AI Excel公式生成工具", "sourceLink": "https://www.boloforms.com/sheetgod?_channel=ai-nav-hnch"}, {"category": "AI表格数据处理", "title": "Excel Formularizer", "imgUrl": "f9e821c4b7ae3e5d.png", "desc": "AI将文本输入转换为Excel公式处理", "sourceLink": "https://excelformularizer.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "包阅AI", "imgUrl": "82ea62f0abeecf6c.png", "desc": "高效的AI智能阅读助手", "sourceLink": "https://baoyueai.com/channel?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Doc2X", "imgUrl": "0852fd8043b770c1.png", "desc": "AI文档识别、转换与翻译工具", "sourceLink": "https://noedgeai.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Acrobat AI Assistant", "imgUrl": "71a53752041971ff.png", "desc": "Adobe推出的Acrobat PDF文档AI助手", "sourceLink": "https://www.adobe.com/acrobat/generative-ai-pdf.html?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "WPS AI", "imgUrl": "ccb7d656db1a8dd9.png", "desc": "WPS推出的AI办公助手，已免费开放", "sourceLink": "https://ai.wps.cn/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "腾讯文档智能助手", "imgUrl": "8a08fd56b6df8095.png", "desc": "腾讯推出的AI文档生成和辅助工具", "sourceLink": "https://docs.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Cubox", "imgUrl": "5210522bf08f203f.png", "desc": "高效的AI阅读学习助手和信息收集管理工具", "sourceLink": "https://cubox.pro/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "5ae8cfe2b913c697.png", "desc": "开源的知识库搭建工具，构建你的第二大脑", "sourceLink": "https://www.quivr.app/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Coda AI", "imgUrl": "1b521b48f15d404a.png", "desc": "在线协作平台Coda推出的AI写作和文档助手，类似于Notion AI", "sourceLink": "https://coda.io/product/ai?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "有道速读", "imgUrl": "3b17a683172ab198.png", "desc": "网易有道推出的AI论文和文档阅读助手", "sourceLink": "https://read.youdao.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "腾讯问卷", "imgUrl": "95e858bcda0c25cb.png", "desc": "腾讯推出的AI生成调查问卷的免费工具", "sourceLink": "https://wj.qq.com/ai/generate.html?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "匡优AI", "imgUrl": "0a96d425de50f109.png", "desc": "AI出题工具，快速生成各类考试题目", "sourceLink": "https://ai.kyou.ltd/pc?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "通义智文", "imgUrl": "eea05fc5df3def00.png", "desc": "基于通义大模型的AI阅读助手，可智能阅读网页、论文、图书和文档", "sourceLink": "https://tongyi.aliyun.com/zhiwen?_channel=ai-nav-hnch#/?ref=ai-bot.cn"}, {"category": "AI文档工具", "title": "字语智能", "imgUrl": "9b58d8e71c98236d.png", "desc": "一站式智能Office内容创作平台", "sourceLink": "https://getgetai.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "星火文档问答", "imgUrl": "4e5b11a0a9e72581.png", "desc": "基于讯飞星火大模型的AI文档和知识库问答助手", "sourceLink": "https://chatdoc.xfyun.cn/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "PMAI", "imgUrl": "a8096b55499ca50d.png", "desc": "面向产品经理的AI助手", "sourceLink": "https://www.pm-ai.cn/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "PDF.ai", "imgUrl": "abe54cdd14634a61.png", "desc": "AI PDF文档阅读工具，智能文档总结摘要", "sourceLink": "https://pdf.ai/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "司马阅", "imgUrl": "6b24a7bd62d44e2d.png", "desc": "AI文档阅读分析工具", "sourceLink": "https://smartread.cc/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "知我AI", "imgUrl": "597f97eb39c1e9d7.png", "desc": "智能阅读机器人，AI总结文档、网页、视频、播客等", "sourceLink": "https://knowme.xiaoduoai.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "星火科研助手", "imgUrl": "ce0cb83a727d7a41.png", "desc": "科大讯飞联合中科院推出的AI科研文献助手", "sourceLink": "https://paper.iflytek.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "印象AI", "imgUrl": "4eb0e1ac7be9648a.png", "desc": "印象笔记推出的AI知识和信息管理功能", "sourceLink": "https://www.yinxiang.com/about/yxai-yxbj?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Craft AI Assistant", "imgUrl": "ecf93dd165cbe86e.png", "desc": "在线文档工具Craft推出的AI文档和创作助手", "sourceLink": "https://www.craft.do/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "<PERSON><PERSON>", "imgUrl": "e77435a11d3874d9.png", "desc": "基于GPT的AI文档分析、阅读和问答工具", "sourceLink": "https://www.humata.ai/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "ChatDOC", "imgUrl": "fe5eb3eeed554da0.png", "desc": "基于ChatGPT的文档阅读、提取、总结、摘要的工具", "sourceLink": "http://chatdoc.com/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "PandaGPT", "imgUrl": "0286fa7e03b1a4ce.png", "desc": "AI文档要点总结工具", "sourceLink": "https://www.pandagpt.io/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Rossum.ai", "imgUrl": "9ecd095bcea6b7d1.png", "desc": "现代化的AI文档处理工具", "sourceLink": "https://rossum.ai/?_channel=ai-nav-hnch"}, {"category": "AI文档工具", "title": "Super AI", "imgUrl": "309c861314666010.png", "desc": "AI复杂文档自动识别处理转换", "sourceLink": "https://super.ai/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "TreeMind树图", "imgUrl": "aa9704c67ccb2720.png", "desc": "新一代AI智能思维导图，一句话生成思维导图", "sourceLink": "https://shutu.cn/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "博思白板", "imgUrl": "8b01bbce4d0fed1e.png", "desc": "博思云创推出的AI多功能白板工具", "sourceLink": "https://boardmix.cn/ai-whiteboard/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "ProcessOn", "imgUrl": "ada7b1a28a485736.png", "desc": "在线AI流程图和思维导图制作工具", "sourceLink": "https://www.processon.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "自由画布", "imgUrl": "50821ff0e2612f0c.png", "desc": "百度文库和百度网盘联合推出的AI万能白板", "sourceLink": "https://wenku.baidu.com/pcactivity/freeBoard?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "亿图脑图", "imgUrl": "8eab94375fc8f9d9.png", "desc": "亿图脑图AI思维导图助手", "sourceLink": "https://www.edrawsoft.cn/mindmaster/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "妙办画板", "imgUrl": "6c5ef5a407171de4.png", "desc": "在线实时协作的画图工具，AI一键生成流程图", "sourceLink": "https://imiaoban.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "Mapify", "imgUrl": "c4a4b196b9748c87.png", "desc": "Xmind推出的AI思维导图生成工具", "sourceLink": "https://mapify.so/cn/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "小画桌", "imgUrl": "91f5315feb81d4a2.png", "desc": "在线协作白板工具，内置AIGC功能", "sourceLink": "https://www.xiaohuazhuo.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "印象图记", "imgUrl": "14e52f88788659df.png", "desc": "印象AI加持的在线思维导图工具", "sourceLink": "https://www.yinxiang.com/product/evermind?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "知犀AI", "imgUrl": "393a59ea9ae7be88.png", "desc": "知犀推出的AI思维导图生成工具", "sourceLink": "https://www.swdt.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "<PERSON><PERSON> Copilot", "imgUrl": "cb6bf97017be73ea.png", "desc": "Xmind AI思维导图助手", "sourceLink": "https://xmind.ai/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "妙办AI画图工具", "imgUrl": "3fa57a82553d7fc3.png", "desc": "AI免费一键生成流程图、思维导图", "sourceLink": "https://imiaoban.com/work/AIht?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "GitMind思乎", "imgUrl": "4e44b98cda6b7335.png", "desc": "AI驱动的免费思维导图工具", "sourceLink": "https://gitmind.cn/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "亿图图示AI", "imgUrl": "8a3180e038b85ac7.png", "desc": "专业的办公绘图软件，轻松绘制图表和图形", "sourceLink": "https://www.edrawsoft.cn/edrawmax/ai/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "Whimsical", "imgUrl": "32006849b4f94f71.gif", "desc": "Whimsical推出的AI思维导图工具", "sourceLink": "https://whimsical.com/ai-mind-maps?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "4603a6b68cb057ea.png", "desc": "开箱即用的在线AI思维导图工具", "sourceLink": "https://amymind.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "Taskade", "imgUrl": "53559cd917700263.png", "desc": "高颜值AI大纲和思维导图生成", "sourceLink": "https://www.taskade.com/?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "Miro AI", "imgUrl": "85f8fffa15b9a8a8.png", "desc": "在线白板协作工具推出的AI功能，Beta测试中", "sourceLink": "https://miro.com/mind-map?_channel=ai-nav-hnch"}, {"category": "AI思维导图", "title": "Ayoa Ultimate", "imgUrl": "ee6fe563de1c82cc.png", "desc": "AI思维导图和头脑风暴工具", "sourceLink": "https://www.ayoa.com/ultimate?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "听脑AI", "imgUrl": "f42dea3c3193d928.png", "desc": "人工智能语音录音记录助手", "sourceLink": "https://itingnao.com/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "简单听记", "imgUrl": "9382ff0fd5faf154.png", "desc": "百度网盘推出的AI语音转文字工具", "sourceLink": "https://pan.baidu.com/embed/listennote?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "通义听悟", "imgUrl": "2d7c134dd6aff874.png", "desc": "阿里推出的AI会议转录工具，万语千言，心领神悟", "sourceLink": "https://tingwu.aliyun.com/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "讯飞会议", "imgUrl": "987f907f0d11961f.png", "desc": "AI智能会议系统，实时字幕、实时翻译、自动生成会议记录", "sourceLink": "https://meeting.iflyrec.com/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "飞书妙记", "imgUrl": "7466af071384545c.png", "desc": "飞书智能会议纪要和快捷语音识别转文字", "sourceLink": "https://www.feishu.cn/product/minutes?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "Otter.ai", "imgUrl": "a1a486aa710c6be2.png", "desc": "AI会议内容生成和实时转录", "sourceLink": "https://otter.ai/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "腾讯会议AI小助手", "imgUrl": "23d099614a9c658d.png", "desc": "腾讯会议推出的AI会议内容助理", "sourceLink": "https://meeting.tencent.com/ai/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "Zoom Workplace", "imgUrl": "1e64b4b810706f87.png", "desc": "Zoom推出的AI办公协作和交流沟通平台", "sourceLink": "https://www.zoom.com/zh-cn/products/collaboration-tools?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "麦耳会记", "imgUrl": "014ec124a91b9419.png", "desc": "思必驰推出的AI会议助手，语音转文字、字幕同传、AI摘要", "sourceLink": "https://work.duiopen.com/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "Fireflies.ai", "imgUrl": "ec6788177229ee93.png", "desc": "AI会议转录和会议纪要生成工具", "sourceLink": "https://fireflies.ai/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "Noty.ai", "imgUrl": "32827b788feccc2d.png", "desc": "ChatGPT驱动的AI会议转录工具", "sourceLink": "https://noty.ai/?_channel=ai-nav-hnch"}, {"category": "AI会议工具", "title": "Airgram", "imgUrl": "35cb167f95b1aeb9.png", "desc": "自动会议笔记和总结的AI助手", "sourceLink": "https://www.airgram.io/?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "755ae1620403809d.png", "desc": "AI招聘求职平台", "sourceLink": "https://ai-bot.cn/mercor?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "Offerin", "imgUrl": "d480dd9b0798723a.png", "desc": "AI面试笔试助手，精准识别面试问题秒级生成答案", "sourceLink": "https://ai-bot.cn/offerin-ai?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "智面星", "imgUrl": "adbf187be5e9555f.png", "desc": "AI面试辅助工具，提供全流程的面试辅助", "sourceLink": "https://ai-bot.cn/aiqtools?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "多面鹅", "imgUrl": "57193a74c68ba615.png", "desc": "免费大厂面试模拟器，线上面试答案提词器", "sourceLink": "https://ai-bot.cn/offergoose?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "白瓜面试", "imgUrl": "229b58972937cfed.png", "desc": "在线AI面试助手，快速生成面试问题的答案", "sourceLink": "https://ai-bot.cn/m-baigua?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "职徒简历", "imgUrl": "d3f46a4a869f5d18.png", "desc": "智能简历制作软件，基于GPT的简历优化和简历代写", "sourceLink": "https://www.52cv.com/?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "职得简历", "imgUrl": "ea0c15d480808ec5.png", "desc": "在线AI简历生成工具", "sourceLink": "https://www.zdjianli.cn/?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "蓝字典AI求职", "imgUrl": "7c638e99cd3681f0.png", "desc": "AI求职工具，提供AI简历生成、AI模拟面试服务", "sourceLink": "https://www.lanzidian.com/?_channel=ai-nav-hnch"}, {"category": "AI招聘求职", "title": "神笔简历", "imgUrl": "c4d258cec43bd1ca.png", "desc": "AI简历云平台，专为求职者提供一站式求职服务", "sourceLink": "https://jianli.jiuyeqiao.cn/?_channel=ai-nav-hnch#/index/index?utm_source=ai-bot.cn"}, {"category": "AI招聘求职", "title": "YOO简历", "imgUrl": "e5d41d275ef61e01.png", "desc": "必优科技推出的AI简历生成工具", "sourceLink": "https://www.yoojober.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "飞书多维表格", "imgUrl": "c6715b0fd80e6da1.png", "desc": "表格形态的 AI 工作流搭建工具", "sourceLink": "https://www.feishu.cn/paid/ai-register?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "TinyWow", "imgUrl": "98a69cdca3cb2025.png", "desc": "免费在线AI工具箱", "sourceLink": "https://tinywow.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "ima.copilot", "imgUrl": "a375d2971bdc2d04.png", "desc": "腾讯推出的AI智能工作台产品，基于混元大模型", "sourceLink": "https://ima.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "飞书知识问答", "imgUrl": "277d695937824194.png", "desc": "飞书智能办公推出的AI知识库工具", "sourceLink": "https://ai-bot.cn/ask-feishu?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "<PERSON>", "imgUrl": "800f3024541d8c63.png", "desc": "全能AI助手，提供聊天、搜索、写作、翻译等多功能服务", "sourceLink": "https://monica.cn/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "WPS灵犀", "imgUrl": "7b1822077147db63.jpg", "desc": "WPS推出的AI办公助手，支持PPT生成、AI写作", "sourceLink": "https://lingxi.wps.cn/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "c83bd1359f03de19.png", "desc": "无代码的AI小工具构建平台", "sourceLink": "https://glif.app/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "奇觅", "imgUrl": "39fe33bf8f331db0.png", "desc": "美图推出的游戏广告AI制作与投放平台", "sourceLink": "https://qimi.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "云一朵", "imgUrl": "0f030f0cc68f7eff.png", "desc": "百度网盘最新推出的智能助理", "sourceLink": "https://pan.baidu.com/aipan/welcome?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "苏打办公", "imgUrl": "2765e1b5adb15f16.png", "desc": "360公司推出的一站式AI办公工具", "sourceLink": "https://bangong.360.cn/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "<PERSON><PERSON>", "imgUrl": "e69a8226d253576e.png", "desc": "AI销售助手", "sourceLink": "https://tome.app/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "Hoarder", "imgUrl": "59febadc37189df3.png", "desc": "开源的基于AI的书签和个人知识库管理工具", "sourceLink": "https://hoarder.app/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "通义晓蜜", "imgUrl": "dd9af078c949637c.png", "desc": "阿里推出的企业智能服务解决方案", "sourceLink": "https://tongyi.aliyun.com/xiaomi?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "奇妙问", "imgUrl": "e6abcbe4a02c9d2a.png", "desc": "企业AI数字员工生成平台", "sourceLink": "https://aiask365.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "NotebookLM", "imgUrl": "abefe288b7d52966.png", "desc": "谷歌推出的AI笔记应用，5分钟生成一段对话播客", "sourceLink": "https://notebooklm.google/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "影刀AI Power", "imgUrl": "682e22ce8dce87d2.png", "desc": "面向企业的无代码AI开发和集成平台", "sourceLink": "https://www.yingdao.com/ai-power/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "通答AI", "imgUrl": "cfc0ad7d49dec98e.png", "desc": "企业AI数字员工生成平台", "sourceLink": "https://www.tongdaai.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "司马诸葛", "imgUrl": "e342caee7f645cc4.png", "desc": "企业级AI数字员工平台", "sourceLink": "https://smartchoose.cn/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "靠谱AI", "imgUrl": "af3a576a27fa7f9d.png", "desc": "无代码AI机器人创建平台", "sourceLink": "https://www.kaopuai.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "<PERSON> Copilot", "imgUrl": "56eb360019964f22.png", "desc": "Salesforce推出的CRM系统AI对话助手", "sourceLink": "https://www.salesforce.com/einsteincopilot?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "Zapier AI", "imgUrl": "f40d3e77cdd22cd7.png", "desc": "Zapier推出的AI自动化集成功能", "sourceLink": "https://zapier.com/ai?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "怪兽AI知识库", "imgUrl": "708aaf91cc229059.png", "desc": "企业知识库大模型 + 智能AI问答机器人", "sourceLink": "https://www.guaishouai.net/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "熊猫办公", "imgUrl": "9ddaa80403e60a2a.png", "desc": "AI办公服务平台，提供PPT模板、Excel模板、Word模板等资源", "sourceLink": "https://www.tukuppt.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "小易AI", "imgUrl": "10541edc002ac35e.png", "desc": "易企秀推出的AI办公工具", "sourceLink": "https://ai.eqxiu.com/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "Merlin", "imgUrl": "9e36fb86533d15aa.png", "desc": "基于ChatGPT的Chrome浏览器扩展，浏览任意网页时利用GPT", "sourceLink": "https://merlin.foyer.work/?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "Raycast AI", "imgUrl": "f8dcc9085fff2fdf.png", "desc": "Raycast推出的Mac AI助手，智能写作、编程、回答问题等", "sourceLink": "https://www.raycast.com/ai?_channel=ai-nav-hnch"}, {"category": "AI效率提升", "title": "Timely", "imgUrl": "8114be11aaca2d4b.png", "desc": "AI时间管理跟踪软件", "sourceLink": "https://timelyapp.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "讯飞绘文", "imgUrl": "98c8a23c7e653dca.png", "desc": "免费AI写作工具，5分钟生成一篇原创稿！", "sourceLink": "https://turbodesk.xfyun.cn/client-pro?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "笔灵AI写作", "imgUrl": "80e06b0767c71950.png", "desc": "面向专业写作领域的AI写作工具", "sourceLink": "https://ibiling.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "火山写作", "imgUrl": "8bb676bf9565abdd.png", "desc": "字节推出的免费AI写作助手（已合并至豆包）", "sourceLink": "https://ai-bot.cn/sites/4189.html?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "新华妙笔", "imgUrl": "d0634cb043202334.png", "desc": "新华社推出的体制内办公学习平台", "sourceLink": "https://miaobi.xinhuaskl.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "笔灵AI小说", "imgUrl": "03058783249e663a.png", "desc": "笔灵推出的AI小说生成和创作工具", "sourceLink": "https://ibiling.cn/novel-workbench/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "讯飞文书", "imgUrl": "129f9ee8d5b425a8.png", "desc": "国产大模型AI公文写作工具", "sourceLink": "https://gw.iflydocs.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "蛙蛙写作", "imgUrl": "abb1551b7aaf086b.png", "desc": "AI小说和内容创作工具", "sourceLink": "https://wawawriter.com/app/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "墨狐AI", "imgUrl": "68f15827a5fe77ab.jpg", "desc": "短篇小说AI写作助手，专为网文小说作者设计", "sourceLink": "https://inkfox-ai.com/?_channel=ai-nav-hnch#/home"}, {"category": "AI写作工具", "title": "Paperpal", "imgUrl": "d45a8bb3f545a5d9.png", "desc": "英文论文写作助手", "sourceLink": "https://www.editage.cn/paperpal?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "稿易AI论文", "imgUrl": "e4bdd8ddffc8f9eb.png", "desc": "AI论文写作助手，免费生成2000字大纲", "sourceLink": "https://gaoyiai.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "千笔AI论文", "imgUrl": "69845ce758654a92.png", "desc": "全网首家论文无限改稿平台", "sourceLink": "https://www.qianbixiezuo.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "66AI论文", "imgUrl": "9ac8439d73970360.png", "desc": "高质量、低查重、低AIGC率的AI论文写作工具", "sourceLink": "https://www.66paper.cn/AI_A2E1C09?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "稿定AI文案", "imgUrl": "64711e0ee714e34c.png", "desc": "小红书、公众号、短视频AI文案生成工具", "sourceLink": "https://www.gaoding.com/utms/cd3cc3a1cb0149d3bfc4a71b5e157098?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "秘塔写作猫", "imgUrl": "b2235a9473e1e49c.png", "desc": "AI写作，文章自成", "sourceLink": "https://xiezuocat.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "光速写作", "imgUrl": "a8723272e5a7f143.png", "desc": "AI写作、PPT生成工具，单篇最长15000字", "sourceLink": "https://www.guangsuxie.com/static/college-write-web/home?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "松果AI写作", "imgUrl": "19962252da4555e5.png", "desc": "AI写作工具，支持批量生成文章", "sourceLink": "https://songguoai.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "QuillBot", "imgUrl": "85f472601ada455b.png", "desc": "AI英/德语写作润色和改进工具", "sourceLink": "https://try.quillbot.com/6eqrqpoysmlh?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "小鱼AI写作", "imgUrl": "a4584a552462753c.png", "desc": "一站式AI写作平台，一键生成高质量原创内容", "sourceLink": "https://www.xiaoyuxiezuo.com/AI_A2E1C09?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "讯飞写作", "imgUrl": "cd10fd65b568e118.png", "desc": "科大讯飞推出的AI智能写作助手", "sourceLink": "https://huixie.iflyrec.com/list?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "橙篇", "imgUrl": "7c45eabab64c96b3.png", "desc": "百度推出的AI长文理解和内容创作工具", "sourceLink": "https://cp.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Notion AI", "imgUrl": "c057e9f95f283ac6.png", "desc": "Notion推出的AI内容创作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "FlowUs AI", "imgUrl": "4f0a5b86b234612f.png", "desc": "在线文档平台息流推出的AI创作助手，类似于Notion AI", "sourceLink": "https://flowus.cn/ai?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "茅茅虫", "imgUrl": "25bac3873441073c.png", "desc": "一站式AI论文写作助手", "sourceLink": "https://mymmc.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "万能小in", "imgUrl": "4a53778a1649ddd6.png", "desc": "AI知识助手，专业知识库的论文专家", "sourceLink": "https://xiaoin.com.cn/home/<USER>"}, {"category": "AI写作工具", "title": "ReadPo", "imgUrl": "e9814961fd268d1c.png", "desc": "AI读写助手，支持内容聚合快速阅读并总结", "sourceLink": "https://ai-bot.cn/readpo?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "迅捷AI写作", "imgUrl": "2db96e715f39e93b.png", "desc": "迅捷办公团队推出的AI写作工具", "sourceLink": "https://www.aichat1234.com/app/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "深言达意", "imgUrl": "5b7071e5a08c0bbb.png", "desc": "免费的词句查询智能写作辅助工具，输入模糊描述即可查找词句", "sourceLink": "https://www.shenyandayi.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "彩云小梦", "imgUrl": "5e648cb6f20bbbde.png", "desc": "彩云科技推出的智能AI故事写作工具", "sourceLink": "https://if.caiyunai.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "MidReal", "imgUrl": "e53de5138239e9a0.png", "desc": "AI互动式小说文本生成工具", "sourceLink": "https://midreal.ai/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "65200c9da0c31e98.png", "desc": "AI内容生成和写作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "Copy.ai", "imgUrl": "fd91bfc3f0fa6ed0.png", "desc": "人工智能营销文案和内容创作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON>", "imgUrl": "22fa5cff2d3a7d0f.png", "desc": "AI文字内容创作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "库宝AI工作助手", "imgUrl": "6f967bdbdee414df.png", "desc": "千库网推出的多功能AI创作工具", "sourceLink": "https://588tool.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Grammarly", "imgUrl": "1c739dc9a8652b7c.png", "desc": "AI英语语法和拼写检查写作助手", "sourceLink": "https://www.grammarly.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "文状元", "imgUrl": "11a05278847db9a3.png", "desc": "AI公文写作助手，提供大量范文库", "sourceLink": "https://www.wenzhuangyuan.cn/workspace/writing-store?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "晓语台", "imgUrl": "9dd8a526aa63e8c1.png", "desc": "智能AI写作工具，内置500+创作模板", "sourceLink": "https://www.xiaoyutai.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Writesonic", "imgUrl": "820a72c61b284896.png", "desc": "AI写作，文案，释义工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "DeepL Write", "imgUrl": "8ae10c7a71d440e5.png", "desc": "DeepL推出的AI驱动的写作助手", "sourceLink": "https://www.deepl.com/write?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "<PERSON><PERSON>", "imgUrl": "350d8a231da5f95f.png", "desc": "AI研究文章和博客写作辅助工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "有道翻译·AI写作", "imgUrl": "3b17a683172ab198.png", "desc": "网易有道推出的智能写作辅助工具，支持100多种语言", "sourceLink": "https://fanyi.youdao.com/aiwrite?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Wordvice AI", "imgUrl": "bd4f940a2b150454.jpg", "desc": "Wordvice推出的免费AI写作助手", "sourceLink": "https://wordvice.ai/cn?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "AI新媒体文章", "imgUrl": "f9c53313faa4bda3.png", "desc": "夸克推出的AI写作工具", "sourceLink": "https://vt.quark.cn/blm/creator-773/index?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "魔撰写作", "imgUrl": "c090dd8ab4d4e422.png", "desc": "出门问问旗下推出的AI智能写作工具", "sourceLink": "https://www.moyin.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "宙语Cosmos", "imgUrl": "006dbace8a9659fa.png", "desc": "专为中文写作设计的AI智能写作助手", "sourceLink": "https://ailjyk.com/pc/creation/model?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "灵构AI笔记", "imgUrl": "cfcf7a8fd3be18eb.png", "desc": "在线安全的灵感收集、思路整理AI笔记工具", "sourceLink": "https://88lingo.com/ai?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "有道写作", "imgUrl": "2f3f3f13ada2cf9a.png", "desc": "网易有道出品的智能英文写作修改和润色工具", "sourceLink": "https://write.youdao.com/?_channel=ai-nav-hnch#/homepage"}, {"category": "AI写作工具", "title": "写作蛙", "imgUrl": "02d8501bafd83fa9.png", "desc": "智谱AI推出的免费智能写作工具", "sourceLink": "https://littlefrog.ai/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "文思助手", "imgUrl": "4ab898c8ab0fa40a.png", "desc": "强大的AI写作智能体，支持生成专业报告和科研论文", "sourceLink": "https://wensi.sodabot.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "WriteWise", "imgUrl": "9705875545d7c270.png", "desc": "喜马拉雅推出的免费网文和小说AI写作工具", "sourceLink": "https://www.ximalaya.com/gatekeeper/write-wise-web?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "百度作家平台", "imgUrl": "e2295528f675bf95.png", "desc": "百度免费AI小说写作工具", "sourceLink": "https://zuojia.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "爱创作", "imgUrl": "41fdab1fb23c4981.png", "desc": "ZAKER新闻推出的AI写作工具", "sourceLink": "https://ai.zaker.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Verse", "imgUrl": "1a92559a52b3bfc4.png", "desc": "印象笔记旗下团队推出的AI写作和文档工具", "sourceLink": "https://verse.app.yinxiang.com/product/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Moonbeam", "imgUrl": "87aa07a2ff731961.png", "desc": "长文章AI内容创作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "Cohesive", "imgUrl": "88a88c9c91225b38.png", "desc": "人工智能文案内容创作和编辑工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "万彩AI", "imgUrl": "d96846ac22ddec13.png", "desc": "全能型AI内容和文案创作助手", "sourceLink": "https://ai.kezhan365.com/inviteCode/FwJFxy?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "WritingPal", "imgUrl": "e47557357a534021.png", "desc": "面向留学生的AI英文写作工具", "sourceLink": "https://writingpal.ai/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Magic Write", "imgUrl": "9a18ac91bbb30ffc.png", "desc": "Canva旗下AI文案生成器", "sourceLink": ""}, {"category": "AI写作工具", "title": "NovelAI", "imgUrl": "65d016799ee7211e.png", "desc": "AI小说故事创作工具", "sourceLink": "https://novelai.net/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "奇妙文", "imgUrl": "49dccca9163f291f.png", "desc": "出门问问推出的AI写作助理", "sourceLink": "https://wen.mobvoi.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Spell.tools", "imgUrl": "850eba4d68943087.png", "desc": "高颜值AI内容营销创作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "HyperWrite", "imgUrl": "6f6ed8b3b6918288.png", "desc": "AI写作助手帮助你创作内容更自信", "sourceLink": ""}, {"category": "AI写作工具", "title": "Typeface AI", "imgUrl": "555a342ef8b1bcaa.png", "desc": "AI创意内容创作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "悉语", "imgUrl": "62949127fb8217b4.png", "desc": "阿里旗下智能文案工具，一键生成电商营销文案", "sourceLink": "https://chuangyi.taobao.com/pages/aiCopy?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "文涌Effidit", "imgUrl": "e959ca1bbe776b55.png", "desc": "腾讯AI Lab开发的智能创作助手", "sourceLink": "https://effidit.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "火龙果写作", "imgUrl": "47ec4293ad2e505f.png", "desc": "AI驱动的文字生产力工具", "sourceLink": "https://www.mypitaya.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "树熊写作", "imgUrl": "ed7a7b4edb3b296c.png", "desc": "树熊AI推出的AI智能写作工具", "sourceLink": "https://ai.koalaoffice.com/ai/homePage?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "爱改写", "imgUrl": "a1233bcbed90d39c.png", "desc": "AI改写、纠错、润色辅助工具", "sourceLink": "https://www.aigaixie.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "HeyFriday", "imgUrl": "83ef42c596872acb.png", "desc": "国内团队推出的智能AI写作工具", "sourceLink": "https://www.heyfriday.cn/home?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "易撰", "imgUrl": "049e68d08d138e39.png", "desc": "新媒体AI内容创作助手", "sourceLink": "https://www.yizhuan5.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "智搜", "imgUrl": "de518e77a6da4dbf.png", "desc": "Giiso写作机器人，内容创作AI辅助工具", "sourceLink": "https://www.giiso.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "创作王", "imgUrl": "79a2d116ec271bd1.png", "desc": "AI一键帮助你创作营销内容", "sourceLink": "https://aiapp.cc/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "字符狂飙", "imgUrl": "128e30b9bd7bad34.png", "desc": "全方位AI文档生成工具，快速生成专业文档", "sourceLink": "https://vgoapp.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "XPaper AI", "imgUrl": "475853db215722df.png", "desc": "晓语台旗下的论文写作辅助指导平台", "sourceLink": "https://www.130ai.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "悟智写作", "imgUrl": "f47d401faee027ad.png", "desc": "人工智能驱动的自动化写作平台", "sourceLink": "https://www.wuz.com.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "讯飞智检", "imgUrl": "d28133c980b7b775.png", "desc": "讯飞推出的智能写作SaaS工具，支持智能写作后的校对与合规审核", "sourceLink": "https://zj.xfyun.cn/exam/text?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "5118 SEO优化精灵", "imgUrl": "d607eceb10dce8cf.png", "desc": "一键式生成高质量SEO文章，提高搜索引擎排名获得更多流量", "sourceLink": "https://www.5118.com/ai/articlegenius?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "ContentBot", "imgUrl": "aedbb4eba299e752.png", "desc": "AI快速写作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON><PERSON>", "imgUrl": "024e10fcaf5fc6ae.png", "desc": "AI阅读总结、写作和内容生成助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "快文CopyDone", "imgUrl": "2d2a790dde6eb2e5.png", "desc": "AIGC原创内容创作和营销文案生成", "sourceLink": "https://copyai.cn/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Peppertype.ai", "imgUrl": "20b8911d57cb8342.png", "desc": "高质量AI内容生成", "sourceLink": ""}, {"category": "AI写作工具", "title": "Compose AI", "imgUrl": "83aa353fba399c8d.png", "desc": "免费的Chrome浏览器自动化写作扩展", "sourceLink": ""}, {"category": "AI写作工具", "title": "Texta", "imgUrl": "707a658fe4095ec4.png", "desc": "AI博客和文章一键生成", "sourceLink": ""}, {"category": "AI写作工具", "title": "Sudowrite", "imgUrl": "2835a073677e84dc.png", "desc": "AI故事写作工具，多种风格选择", "sourceLink": "https://www.sudowrite.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "ClosersCopy", "imgUrl": "8cff002c7bc1125f.png", "desc": "AI文案写作机器人", "sourceLink": ""}, {"category": "AI写作工具", "title": "WPS智能写作", "imgUrl": "332e75f2295361a5.png", "desc": "WPS旗下在线智能写作工具", "sourceLink": "https://ai-bot.cn/sites/33.html?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Anyword", "imgUrl": "778766f0120d5f20.png", "desc": "AI文案写作助手和文本生成器", "sourceLink": ""}, {"category": "AI写作工具", "title": "Hypotenuse AI", "imgUrl": "ab50a6ab40e633e8.png", "desc": "人工智能写作助手和文本生成器", "sourceLink": ""}, {"category": "AI写作工具", "title": "Smodin AI Research Paper", "imgUrl": "c2e706281fd024ab.png", "desc": "Smodin推出的AI研究论文写作工具", "sourceLink": "https://smodin.io/zh-cn/%E4%BD%9C%E5%AE%B6/%E7%A0%94%E7%A9%B6%E8%AE%BA%E6%96%87?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "ParagraphAI", "imgUrl": "63a19143525f1926.png", "desc": "基于ChatGPT的AI写作应用", "sourceLink": ""}, {"category": "AI写作工具", "title": "LongShot", "imgUrl": "9ffdb33192cfbbc3.png", "desc": "AI长文章写作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "3314d9a9b848c124.png", "desc": "无限制免费AI文案写作", "sourceLink": ""}, {"category": "AI写作工具", "title": "Reword", "imgUrl": "1860a7ed0cfe6ff0.png", "desc": "AI文章写作", "sourceLink": ""}, {"category": "AI写作工具", "title": "Elephas", "imgUrl": "2048e500e31dd6a1.png", "desc": "与Mac、iPhone、iPad集成的个人写作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "AISEO", "imgUrl": "c3c585162d6ef75f.png", "desc": "AI创作SEO优化友好的文案和文章", "sourceLink": ""}, {"category": "AI写作工具", "title": "PaperBetter AI", "imgUrl": "9f485983dcd9302c.png", "desc": "AI论文写作工具，一键生成万字初稿", "sourceLink": "http://www.ai.paperbetter.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "Writer", "imgUrl": "301bd558d398af60.png", "desc": "企业级AI内容创作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "范文喵", "imgUrl": "fe7f5a6bb8c7a227.png", "desc": "面向大学生的AI论文写作工具", "sourceLink": "https://ai.wolian.chat/openmao/?_channel=ai-nav-hnch#/?inviteCode=1745323733465174017"}, {"category": "AI写作工具", "title": "小微智能论文", "imgUrl": "3fa92c2c20559359.png", "desc": "人工智能论文创作辅助工具", "sourceLink": "http://t2unr1.checkmore.net/tb/NY6p0S?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "笔杆论文", "imgUrl": "ce621ccacd8cf27c.png", "desc": "简单高效的AI论文写作平台", "sourceLink": "https://write.bigan.net/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "AI论文君", "imgUrl": "6cceb056e30cad2d.png", "desc": "AI大模型驱动的论文写作好帮手", "sourceLink": "https://www.ailunwenjun.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "SurferSEO", "imgUrl": "bc72e10ecc754cfe.png", "desc": "AI SEO大纲和内容优化写作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "ProWritingAid", "imgUrl": "8d189321553b6cd5.png", "desc": "英语写作优化和修改工具", "sourceLink": "https://prowritingaid.com/?_channel=ai-nav-hnch"}, {"category": "AI写作工具", "title": "WordTune", "imgUrl": "0e712b6688f1daf5.png", "desc": "你的个人写作助手和编辑", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON><PERSON>", "imgUrl": "69df86c2ef2bfd03.png", "desc": "释放你的写作潜力", "sourceLink": ""}, {"category": "AI写作工具", "title": "Co<PERSON><PERSON>", "imgUrl": "a3b8e94d4f4ab60b.png", "desc": "企业级和电商文案生成", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON>ase", "imgUrl": "26e08989d1df83d8.png", "desc": "AI SEO内容优化和写作工具", "sourceLink": ""}, {"category": "AI写作工具", "title": "NeuralText", "imgUrl": "1465c12c75170ffd.png", "desc": "人工智能SEO文章写作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "Copymatic", "imgUrl": "b80540ae10b64e4b.png", "desc": "AI文案和内容创作助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "TextCortex", "imgUrl": "c274b0ff5ecca98c.png", "desc": "AI写作能手", "sourceLink": ""}, {"category": "AI写作工具", "title": "星火网文助手", "imgUrl": "9746d2c5aa0bd3c1.png", "desc": "免费AI小说网文写作工具", "sourceLink": "http://writersdesk.net/?_channel=ai-nav-hnch#/dashboard"}, {"category": "AI写作工具", "title": "INK", "imgUrl": "017ffba9091407d3.png", "desc": "AI内容营销和SEO助手", "sourceLink": ""}, {"category": "AI写作工具", "title": "Content at Scale", "imgUrl": "b2697c3f52f6118f.png", "desc": "AI SEO长内容创作", "sourceLink": ""}, {"category": "AI写作工具", "title": "<PERSON>", "imgUrl": "1e6905ba31d32e85.png", "desc": "AI快速创建营销文案", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "Cutout.Pro", "imgUrl": "5075e42714be9511.png", "desc": "AI在线处理图片", "sourceLink": "https://www.cutout.pro/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "MagicStudio", "imgUrl": "2a3c0e07f21ce8f3.png", "desc": "高颜值AI图像处理工具", "sourceLink": "https://magicstudio.com/zh?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Booltool", "imgUrl": "cae77a822788f91f.png", "desc": "在线AI图像工具箱", "sourceLink": "https://booltool.boolv.tech/home?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Faceswapper", "imgUrl": "b24d1dbebb186f30.png", "desc": "AI在线换脸工具", "sourceLink": "https://faceswapper.ai/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "ClipDrop", "imgUrl": "d21b4a5cc884e77d.png", "desc": "Stability.ai推出的AI图片处理系列工具", "sourceLink": "https://clipdrop.co/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Vmake AI", "imgUrl": "60a7b4736df05ab7.png", "desc": "AI在线图像和视频编辑平台，专为电商、设计提供服务", "sourceLink": "https://vmake.ai/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Leonardo.ai", "imgUrl": "872c6937560c94cb.png", "desc": "免费的AI绘画和图像生成工具和社区", "sourceLink": "https://leonardo.ai/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "DeepSwapper", "imgUrl": "31d39b2397cbbb80.png", "desc": "免费的在线AI换脸工具，支持图片、视频多种格式", "sourceLink": "https://www.deepswapper.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "<PERSON><PERSON>", "imgUrl": "6f2d8535d9c7281b.png", "desc": "专业的AI写真工具，媲美专业摄影", "sourceLink": "https://www.kacha.ai/zh?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "PicTech AI", "imgUrl": "7285cdd2401b092d.png", "desc": "免费的在线图片翻译工具，支持一键抠图", "sourceLink": "https://www.pictech.cc/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Hotpot.ai", "imgUrl": "96a68d04b9496a71.png", "desc": "AI图片图像处理和生成工具", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "IconGen", "imgUrl": "b44ee727e42fc751.png", "desc": "免费的icon图标AI生成器", "sourceLink": "https://www.icongen.io/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "言之画", "imgUrl": "a6c23b0fff1bee6c.png", "desc": "AI图像内容创作平台，由出门问问推出", "sourceLink": "https://paint.mobvoi.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "百度智能云一念", "imgUrl": "4e342bebc2d73eba.png", "desc": "基于百度文心大模型的多模态内容创作平台", "sourceLink": "https://yinian.cloud.baidu.com/creativity/main/workbench?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "艾绘", "imgUrl": "a89b70d240a2501b.png", "desc": "一键创作故事、绘画、配音，轻松创建高质量的绘本故事", "sourceLink": "https://www.aiyou.art/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "a718908943b1a08a.png", "desc": "开箱即用的 Stable Diffusion WebUI 在线图像生成服务", "sourceLink": "https://www.diffus.graviti.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "秘塔捉捉猫", "imgUrl": "419314bc339e4aab.png", "desc": "秘塔写作猫推出的AI文字到图像生成工具", "sourceLink": "https://ai-bot.cn/sites/1607.html?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "超能画布", "imgUrl": "87c9c34cfdcec248.png", "desc": "百度网盘推出的AI创意图像写真创作平台", "sourceLink": "https://photo.baidu.com/photasy/home?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Bing Image Creator", "imgUrl": "8deb72edc088ed11.png", "desc": "微软必应推出的基于DALL·E的AI图像生成工具", "sourceLink": "https://www.bing.com/images/create?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Adobe Firefly", "imgUrl": "2d21f08ff53e0a2f.png", "desc": "Adobe最新推出的AI图片生成工具", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "简单AI", "imgUrl": "3d937c40d88a0124.png", "desc": "搜狐推出的AI图片生成平台", "sourceLink": "https://ai.sohu.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "摩笔马良", "imgUrl": "7c2f6e8e2f9f7a08.png", "desc": "摩尔线程推出的AI图像绘画创作平台", "sourceLink": "https://maliang.mthreads.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Exactly.ai", "imgUrl": "b0c7a76b0e7f0295.png", "desc": "专业的AI绘画和艺术创作平台", "sourceLink": "https://exactly.ai/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "画宇宙", "imgUrl": "b43211f85c91d8a9.png", "desc": "人工智能AI作画网站", "sourceLink": "https://creator.nolibox.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "6pen Art", "imgUrl": "c252e6cafca10712.png", "desc": "面包多团队推出的从文本描述生成绘画艺术作品", "sourceLink": "https://6pen.art/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "创客贴AI画匠", "imgUrl": "bb25e89319713286.png", "desc": "创客贴推出的AI艺术画生成工具", "sourceLink": "https://aiart.chuangkit.com/landingpage?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Visual Electric", "imgUrl": "ce0b5fbfac6568f2.png", "desc": "专业的高质量AI图像创作工具", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "360智绘", "imgUrl": "af77128fd40950a6.png", "desc": "360推出的AI图片和绘画生成工具", "sourceLink": "https://aigc.360.com/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "网易AI创意工坊", "imgUrl": "a27987d22181d209.png", "desc": "网易云课堂推出的AI作画平台，在线使用Stable Diffusion出图", "sourceLink": "https://ke.study.163.com/artWorks/painting?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Imagine with <PERSON><PERSON>", "imgUrl": "1977e8a1a2d8589c.png", "desc": "Meta最新推出的在线AI图像生成器", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "Freepik AI Image Generator", "imgUrl": "126e7751541ee5d0.png", "desc": "Freepik最新推出的AI图片生成工具", "sourceLink": "https://www.freepik.com/ai/image-generator?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "Stockimg AI", "imgUrl": "be9fc04c8c1cea30.png", "desc": "AI生成各种类型的图像和插画", "sourceLink": ""}, {"category": "常用AI图像工具", "title": "Stable Doodle", "imgUrl": "bfe9a27d0f1e2986.png", "desc": "StabilityAI最新推出的将手绘草图转换成精美图像的工具", "sourceLink": "https://clipdrop.co/stable-doodle?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "175FUN", "imgUrl": "9eb63a63c71f59c3.png", "desc": "免费AI绘画社区，国货之光", "sourceLink": "https://175.fun/?_channel=ai-nav-hnch"}, {"category": "常用AI图像工具", "title": "行者AI美术", "imgUrl": "4b8fb02433fbf6d0.png", "desc": "AI图片生成和美术创作工具箱", "sourceLink": "https://xingzheai.cn/?_channel=ai-nav-hnch#create?utm_source=ai-bot.cn"}, {"category": "常用AI图像工具", "title": "Skybox AI", "imgUrl": "a250f8b29c687ec1.png", "desc": "AI生成和合成360°全景图像插画", "sourceLink": "https://skybox.blockadelabs.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "白日梦", "imgUrl": "ad8e603d76ed6a6b.png", "desc": "AI视频创作平台，最长可生成六分钟的视频", "sourceLink": "https://aibrm.paluai.com/bairimeng?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "绘蛙AI视频", "imgUrl": "4f48c7f48ab06d0d.png", "desc": "绘蛙推出的AI图生视频工具", "sourceLink": "https://www.ihuiwa.com/workspace/ai-video/custom-action?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "讯飞绘镜", "imgUrl": "5e4afce518605a97.png", "desc": "科大讯飞推出的AI短视频创作平台", "sourceLink": "https://typemovie.art/?_channel=ai-nav-hnch#/dashboard"}, {"category": "AI视频工具", "title": "Vidu", "imgUrl": "8b03e9c5054859d0.jpg", "desc": "生数科技推出的AI视频生成大模型", "sourceLink": "https://www.vidu.cn/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "SoundView", "imgUrl": "84686fc983b95e47.jpg", "desc": "AI视频本地化工具，支持视频配音和翻译", "sourceLink": "https://soundviewai.com/invitation?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "HeyGen", "imgUrl": "901cbc8cd6f8042b.png", "desc": "AI数字人视频创作平台", "sourceLink": "https://ai-bot.cn/heygen?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "有言", "imgUrl": "bbba9481003edf44.png", "desc": "一站式AI视频创作和3D数字人生成平台", "sourceLink": "https://www.youyan3d.com/platform/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "巨日禄", "imgUrl": "3a2cae75cd05c923.png", "desc": "一站式AI动漫视频创作平台", "sourceLink": "https://jurilu.paluai.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "可灵AI", "imgUrl": "153544f6f2fa9f7f.png", "desc": "快手推出的AI视频生成工具", "sourceLink": "https://kling.kuaishou.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "即梦AI", "imgUrl": "39abf49dbfa90a46.png", "desc": "字节跳动推出的一站式AI创作平台", "sourceLink": "http://dis.csqixiang.cn/unpo/jimeng_1.html?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "智谱清影", "imgUrl": "9bfe4fe29530e4b6.png", "desc": "智谱推出的AI视频生成工具", "sourceLink": "https://ai-bot.cn/qingying?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Pollo AI", "imgUrl": "a081562688ade23c.png", "desc": "一站式AI图像和视频创作平台", "sourceLink": "https://ai-bot.cn/pollo-ai?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "创一AI", "imgUrl": "1619471c378eb6dc.png", "desc": "AI音视频创作工具，支持AI短片、AI角色和作图配音等", "sourceLink": "https://www.creatifyone.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "蝉镜", "imgUrl": "7b58b4145385f357.png", "desc": "AI数字人视频生成平台", "sourceLink": "https://www.chanjing.cc/refc/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Runway", "imgUrl": "487da429953ce5d9.png", "desc": "AI视频工具，绿幕抠除、视频生成、动态捕捉等功能", "sourceLink": "https://runwayml.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "海螺AI", "imgUrl": "9f63e622b78bb46d.png", "desc": "MiniMax公司推出的AI视频生成工具", "sourceLink": "https://ai-bot.cn/hailuoai-video?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "腾讯混元AI视频", "imgUrl": "e2f6b80c297dc1f8.png", "desc": "腾讯推出的AI视频生成工具", "sourceLink": "https://video.hunyuan.tencent.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "通义万相AI视频", "imgUrl": "eea05fc5df3def00.png", "desc": "通义万相AI视频是阿里推出的...", "sourceLink": "https://ai-bot.cn/tongyi-wanxvideo?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "JoyPix", "imgUrl": "92c6ab2122676bf5.png", "desc": "AI数字人创作工具，支持声音克隆", "sourceLink": "https://www.joypix.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON>ra", "imgUrl": "4e46d5e690f291b1.png", "desc": "OpenAI推出的AI视频生成模型", "sourceLink": "https://openai.com/sora?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Pika", "imgUrl": "da49bd7b8fa52c69.png", "desc": "Pika Labs推出的AI视频生成和编辑工具", "sourceLink": "https://pika.art/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Medeo", "imgUrl": "24079335e62a7a0c.jpg", "desc": "AI视频创作平台，一句话生成完整视频", "sourceLink": "https://ai-bot.cn/medeo?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Dream Machine", "imgUrl": "44a14cc8f6e63eee.png", "desc": "Luma AI推出的AI视频生成工具", "sourceLink": "https://lumalabs.ai/dream-machine?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "KreadoAI", "imgUrl": "3d5aa3040ce17a1f.png", "desc": "AI数字人视频营销创作平台", "sourceLink": "https://www.kreadoai.com/zh?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "妙播", "imgUrl": "ac5149916fc5fa9b.png", "desc": "腾讯广告推出的AI直播电商解决方案", "sourceLink": "https://admuse.qq.com/intelligent/live/page/index.html?_channel=ai-nav-hnch#/?utm_source=ai-bot.cn"}, {"category": "AI视频工具", "title": "即创", "imgUrl": "39dd16c9d2c50e2e.png", "desc": "抖音推出的一站式AI智能创作平台", "sourceLink": "https://aic.oceanengine.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON>", "imgUrl": "d59bcf686787ba84.png", "desc": "AI对口型视频生成工具", "sourceLink": "https://www.hedra.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON>o", "imgUrl": "bee4c5766b91377a.png", "desc": "多功能AI视频编辑工具", "sourceLink": "https://www.vozo.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Viggle", "imgUrl": "3bb2db0e52005f3d.png", "desc": "AI生成可控的角色动态视频的工具", "sourceLink": "https://viggle.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "88c629df4b7d2d46.png", "desc": "AI数字人克隆和AI视频实时对话工具", "sourceLink": "https://ai-bot.cn/tavus?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "阶跃视频", "imgUrl": "912df2e931015d25.png", "desc": "阶跃星辰推出的AI视频生成工具", "sourceLink": "https://ai-bot.cn/yuewen-videos?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "秒创", "imgUrl": "6f4a3881c633734a.png", "desc": "AIGC内容创作平台", "sourceLink": "https://aigc.yizhentv.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "元镜", "imgUrl": "aa60143f834f000a.png", "desc": "AI视频生成工具，支持多模态创意分镜创作服务", "sourceLink": "https://ai-bot.cn/yuanjing?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "SkyReels", "imgUrl": "2820044a40c80cfd.png", "desc": "昆仑万维推出的AI短剧创作平台", "sourceLink": "https://skyreels.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "MOKI", "imgUrl": "fa40ba69ce67ddd8.png", "desc": "美图推出的AI视频短片创作平台", "sourceLink": "https://www.moki.cn/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "神笔马良", "imgUrl": "c515a9ba3741ee8d.png", "desc": "猫眼娱乐推出的AI影视创作生成工具", "sourceLink": "https://ai-bot.cn/shenbi-maoyan?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Video Ocean", "imgUrl": "3177ca2059eb902c.png", "desc": "潞晨科技推出的多功能AI视频生成平台", "sourceLink": "https://ai-bot.cn/video-ocean?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Flow Studio", "imgUrl": "edc0243499ac0333.png", "desc": "FlowGPT推出的AI长视频生成工具", "sourceLink": "https://flowgpt.com/flow-studio?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "c24ced3fe9a33ef2.png", "desc": "将长视频转为社交短视频的AI工具", "sourceLink": "https://vizard.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "寻光", "imgUrl": "1c5250b3c30fa21e.png", "desc": "阿里达摩院推出的全流程AI视频创作平台", "sourceLink": "https://xunguang.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Hotshot", "imgUrl": "d5de88135ac3f85c.png", "desc": "AI视频生成工具，将文本转为3秒逼真视频", "sourceLink": "https://hotshot.co/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Viva", "imgUrl": "ff3d701df603ddfe.png", "desc": "免费的AI视频生成和图像创作平台", "sourceLink": "https://vivago.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "66c58ae57a9832ac.png", "desc": "AI数字人生成工具，自定义创建专属数字人", "sourceLink": "https://humva.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "D-ID", "imgUrl": "290a3e7e87eab94b.png", "desc": "AI真人口播视频生成工具", "sourceLink": "https://www.d-id.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Stable Video", "imgUrl": "e13fb5df16be430b.png", "desc": "Stability AI推出的AI视频生成工具", "sourceLink": "https://www.stablevideo.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "OneStory", "imgUrl": "5465feb94c76aedd.png", "desc": "专业的AI故事生成助手", "sourceLink": "https://onestory.art/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Noisee AI", "imgUrl": "d6ed09eeb997387a.png", "desc": "月之暗面旗下推出的AI音乐视频MV生成工具", "sourceLink": "https://noisee.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "腾讯智影", "imgUrl": "bc94e3abad200ba2.png", "desc": "腾讯推出的AI智能创作工具", "sourceLink": "https://zenvideo.qq.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "万兴播爆", "imgUrl": "a40552eebb52d19a.png", "desc": "AI数字人口播视频营销工具，海量素材一键套用", "sourceLink": "https://virbo.wondershare.cn/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "b651566530468c0b.png", "desc": "商汤科技推出的可控人物视频生成AI模型", "sourceLink": "https://www.sensetime.com/cn/product-detail?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Etna", "imgUrl": "9249d25626d3f76c.png", "desc": "七火山科技推出的AI文生视频工具\n", "sourceLink": "https://etna.7volcanoes.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "艺映AI", "imgUrl": "6d719f99bf74fb62.png", "desc": "AI视频创作工具，支持文生视频、图生视频及视频转漫画功能", "sourceLink": "https://www.artink.art/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "LensGo", "imgUrl": "f7187ef064705777.png", "desc": "AI视频创作工具，支持视频转动漫，替换3D人物", "sourceLink": "https://lensgo.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "必剪Studio", "imgUrl": "9be766a0cf911774.png", "desc": "B站推出的免费AI数字分身定制和视频创作工具", "sourceLink": "https://member.bilibili.com/york/bilibili-studio/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "度加创作工具", "imgUrl": "6f3ee941fe4dd49a.png", "desc": "百度官方出品的AIGC创作平台", "sourceLink": "https://aigc.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "WinkStudio", "imgUrl": "1a034b5fbc8da41b.png", "desc": "美图推出的桌面端AI视频剪辑工具", "sourceLink": "https://wink.meitu.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "VMagic", "imgUrl": "7d1f3d5d7d7d1a80.png", "desc": "AI视频处理平台，提供视频风格转换、换脸、照片舞蹈等功能", "sourceLink": "https://www.vmagic.app/zh?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "讯飞虚拟人", "imgUrl": "e186991770441ccd.png", "desc": "科大讯飞推出的全栈式AI虚拟人应用服务平台", "sourceLink": "https://virtual-man.xfyun.cn/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "飞影数字人", "imgUrl": "bb82597007af7433.png", "desc": "AI数字人创作平台，支持免费定制数字人", "sourceLink": "https://www.flyworks.live/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Video Studio", "imgUrl": "bbfba71040f05801.png", "desc": "在线AI视频制作工具，零编辑技能制作专业视频内容", "sourceLink": "https://www.vidustudio.net/zh?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Pixfun", "imgUrl": "2cb8c66f4a2db1f5.jpg", "desc": "一站式动画故事AI视频生成平台", "sourceLink": "https://app.pixfun.ai/home?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "1379e87278d72fa0.png", "desc": "AI视频生成平台，支持音频同步功能", "sourceLink": "https://www.decohere.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Yo<PERSON>o", "imgUrl": "86c398bb5c389810.png", "desc": "鹿影科技推出的二次元动漫视频AI创作平台", "sourceLink": "https://avolutionai.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Opus Clip", "imgUrl": "4cb33e0e2af4cd28.png", "desc": "AI视频切片工具，自动从长视频中提取精彩片段", "sourceLink": "https://www.opus.pro/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Filmora", "imgUrl": "64425422467d0c20.png", "desc": "万兴科技推出的AI视频编辑工具", "sourceLink": "https://filmora.wondershare.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Descript", "imgUrl": "bfb592fac1a76e4c.png", "desc": "AI视频编辑工具，支持通过编辑文字来剪辑音视频内容", "sourceLink": "https://www.descript.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "曦灵", "imgUrl": "4e342bebc2d73eba.png", "desc": "百度推出的AI数字人和视频创作平台", "sourceLink": "https://xiling.cloud.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "开拍", "imgUrl": "36940e2a09b80ca1.png", "desc": "美图推出的AI口播视频制作工具", "sourceLink": "https://kaipai.meitu.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Duix", "imgUrl": "0446ce96ee61fffb.png", "desc": "硅基智能推出的AI数字人生成平台", "sourceLink": "https://www.duix.ai/duix-app-landing-page/?_channel=ai-nav-hnch#/home?utm_source=ai-bot.cn"}, {"category": "AI视频工具", "title": "场辞", "imgUrl": "32f7b541e862ae63.png", "desc": "新片场推出的AI视频字幕制作工具", "sourceLink": "https://trans.xinpianchang.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "一起剪", "imgUrl": "517b72811d10f8d3.png", "desc": "AI短视频创作平台，图文一键成片", "sourceLink": "https://www.yiqijian.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Spikes Studio", "imgUrl": "a8e68623cbff2499.png", "desc": "AI自动将长视频切片剪辑为短视频", "sourceLink": "https://spikes.studio/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Google Vids", "imgUrl": "12aa6e9fedc1428b.png", "desc": "谷歌推出的AI视频创作工具", "sourceLink": "https://workspace.google.com/products/vids/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "DomoAI", "imgUrl": "32b6122ce88136de.png", "desc": "一键将照片和视频动漫化的AI工具", "sourceLink": "https://domoai.app/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Gatekeep", "imgUrl": "1cc6658fdcab84ff.png", "desc": "AI教学视频生成工具，可生成数学物理问题解释视频", "sourceLink": "https://gatekeep.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Morph Studio", "imgUrl": "5674a1ef8a207a86.png", "desc": "高质量的AI文本到视频生成工具", "sourceLink": "https://www.morphstudio.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON>", "imgUrl": "4ed57bae6ecc7171.png", "desc": "AI视频生成和重绘工具，支持文本/图像转视频", "sourceLink": "https://haiper.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Showrunner", "imgUrl": "4a9b7e4d5aba51ea.png", "desc": "AI动画视频剧集生成工具", "sourceLink": "https://www.showrunner.xyz/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "即构数智人", "imgUrl": "512097fe03a4e975.png", "desc": "即构科技推出的AI数字人创作平台", "sourceLink": "https://aigc.zego.im/?_channel=ai-nav-hnch#/MyHome?utm_source=ai-bot.cn"}, {"category": "AI视频工具", "title": "快剪辑", "imgUrl": "5efc9be291252acb.png", "desc": "360旗下的AI视频剪辑工具，AI成片、AI数字人、智能添加字幕、去水印等", "sourceLink": "https://www.kuaijianji.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "闪剪", "imgUrl": "c6c4bf30d8cf9ca8.png", "desc": "AI数字人短视频创作工具", "sourceLink": "https://shanjian.tv/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Wonder Studio", "imgUrl": "99cd590a3c17bd11.png", "desc": "AI自动为CG角色制作动画、打光并将其合成到真人场景中", "sourceLink": "https://wonderdynamics.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON>", "imgUrl": "a67279a4325113fc.png", "desc": "实时的AI直播/视频换脸工具", "sourceLink": "https://magicam.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "LTX Studio", "imgUrl": "4bbd810957188323.png", "desc": "AI电影制作和视频短片生成平台", "sourceLink": "https://ltx.studio/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Clipfly", "imgUrl": "b095d2152ca2ceb3.png", "desc": "一站式AI长视频制作和编辑平台", "sourceLink": "https://www.clipfly.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Captions", "imgUrl": "559ce28e39f20062.png", "desc": "AI驱动的视频剪辑和制作平台", "sourceLink": "https://www.captions.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Capsule", "imgUrl": "9961f27369184e9e.png", "desc": "AI驱动的在线视频剪辑工具，个人和小团队免费", "sourceLink": "https://capsule.video/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "GoEnhance", "imgUrl": "987c9486a7c8a235.png", "desc": "AI视频风格转换和画质增强工具", "sourceLink": "https://www.goenhance.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "InVideo AI", "imgUrl": "d7b1463e582c6809.png", "desc": "人工智能视频创作和剪辑工具", "sourceLink": "https://invideo.io/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Unscreen", "imgUrl": "2520db192e3b1eea.png", "desc": "AI智能视频背景移除工具", "sourceLink": "https://www.unscreen.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "EbSynth", "imgUrl": "12ea30533318d4bc.png", "desc": "AI将真人视频转化为油画风动画", "sourceLink": "https://ebsynth.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Artflow", "imgUrl": "4d9fd9367ec32a9c.png", "desc": "AI创建生成视频动画", "sourceLink": ""}, {"category": "AI视频工具", "title": "<PERSON><PERSON>", "imgUrl": "2ecf8d55a7cfd4b1.png", "desc": "图片文字转视频的AI引擎", "sourceLink": ""}, {"category": "AI视频工具", "title": "Typeframes", "imgUrl": "e59fd6ba298abef8.png", "desc": "AI快速生成高质量的产品介绍视频", "sourceLink": "https://www.typeframes.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "DreamFace", "imgUrl": "3d29a41c1f97d9a2.png", "desc": "让图片动起来的AI工具", "sourceLink": "https://dreamfaceapp.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Mootion", "imgUrl": "dd014530062e1f00.png", "desc": "AI原生3D动画创作平台", "sourceLink": "https://mootion.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "PixVerse", "imgUrl": "7117c1cf09af047d.png", "desc": "爱诗科技推出的AI视频生成工具", "sourceLink": "https://ai-bot.cn/pixverse-ai-video-generator?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "来画", "imgUrl": "202a0ed04a3c9ce8.png", "desc": "动画和数字人智能生成平台", "sourceLink": "https://www.laihua.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "奇妙元", "imgUrl": "e58682c333f4a82d.png", "desc": "AI数字人视频生成平台，由出门问问推出", "sourceLink": "https://weta365.com/conduct?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "绘影字幕", "imgUrl": "58a2e57cf39aabe1.png", "desc": "一键智能在线自动为视频加字幕", "sourceLink": "https://huiyingzimu.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Fliki", "imgUrl": "70fb1a489fa0001e.png", "desc": "AI文字转视频并配音", "sourceLink": ""}, {"category": "AI视频工具", "title": "Anylang.ai", "imgUrl": "ec3802ec878b50de.png", "desc": "AI视频翻译并保持音色和口型的同步", "sourceLink": "https://anylang.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "DeepBrain", "imgUrl": "2b12698ea1ac0621.png", "desc": "AI口播视频生成工具", "sourceLink": ""}, {"category": "AI视频工具", "title": "Synthesia", "imgUrl": "a098151df2dcd3d5.png", "desc": "AI视频生成平台", "sourceLink": ""}, {"category": "AI视频工具", "title": "Lumen5", "imgUrl": "1f8cf7ab9052d994.png", "desc": "AI将博客文章转换成视频", "sourceLink": ""}, {"category": "AI视频工具", "title": "Rephrase.ai", "imgUrl": "2907dd0241572b65.png", "desc": "AI文字到视频生成", "sourceLink": ""}, {"category": "AI视频工具", "title": "万彩微影", "imgUrl": "681458aba46901bf.png", "desc": "AI智能自动生成动画短视频", "sourceLink": "https://www.animiz.cn/microvideo/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "录咖", "imgUrl": "aac71b453236a813.png", "desc": "一站式AI音视频总结和转录处理工具", "sourceLink": "https://reccloud.cn/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "怪兽AI数字人", "imgUrl": "9e3dc26fbb551b88.png", "desc": "人工智能数字人短视频创作和直播平台", "sourceLink": "https://www.guaishouai.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "团队快剪", "imgUrl": "33822cb4bdf67c08.png", "desc": "闪剪智能专为团队带货打造的AI视频工具", "sourceLink": "https://teamcut.shanjian.tv/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "鬼手剪辑GhostCut", "imgUrl": "a48db97bb2ed8164.png", "desc": "多功能AI视频二创剪辑和翻译工具", "sourceLink": "https://cn.jollytoday.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "模力视频", "imgUrl": "304843b1b42a2c43.png", "desc": "AI驱动的视频编辑平台", "sourceLink": "https://www.mooliv.com/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Gencraft", "imgUrl": "f4c5925baaf360b0.png", "desc": "AI艺术画视频生成工具", "sourceLink": ""}, {"category": "AI视频工具", "title": "Synthesys", "imgUrl": "9ee0784cda176cc3.png", "desc": "AI虚拟人出镜讲解", "sourceLink": ""}, {"category": "AI视频工具", "title": "Veed Video Background Remover", "imgUrl": "6a2ba0d1d756b268.png", "desc": "Veed推出的AI视频背景移除工具", "sourceLink": "https://www.veed.io/zh-CN/tools/video-background-remover?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Hour One", "imgUrl": "15dd2e77669e3ba8.png", "desc": "人工智能文字到视频生成", "sourceLink": ""}, {"category": "AI视频工具", "title": "BgRem", "imgUrl": "51c40ff7f77886bf.png", "desc": "无水印AI视频背景移除", "sourceLink": "https://bgrem.deelvin.com/zh/remove_video_bg/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Colourlab.ai", "imgUrl": "ad274081e45c9691.png", "desc": "好莱坞也在用的AI视频颜色分级工具", "sourceLink": "https://colourlab.ai/?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "Cutout.Pro", "imgUrl": "5075e42714be9511.png", "desc": "AI一键视频背景移除", "sourceLink": "https://www.cutout.pro/zh-CN/remove-video-background?_channel=ai-nav-hnch"}, {"category": "AI视频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "0802aaf406acca71.png", "desc": "AI虚拟人出镜视频生成", "sourceLink": ""}, {"category": "AI视频工具", "title": "AVCLabs", "imgUrl": "f4ba3cff8cd213db.png", "desc": "AI自动移除视频背景", "sourceLink": "https://app.avclabs.com/?_channel=ai-nav-hnch#"}, {"category": "AI视频工具", "title": "Elai.io", "imgUrl": "b7b7bfd04334038c.png", "desc": "AI文本到视频生成工具", "sourceLink": ""}, {"category": "AI视频工具", "title": "Pictory", "imgUrl": "9a051e3bb2f3ee3b.png", "desc": "AI视频制作工具", "sourceLink": ""}, {"category": "AI视频工具", "title": "SteveAI", "imgUrl": "244f35a091d3597b.png", "desc": "Animaker旗下AI在线视频制作工具", "sourceLink": ""}, {"category": "AI视频工具", "title": "Rask", "imgUrl": "64f118a499d3df67.png", "desc": "AI视频本地化解决方案，支持超过130种语言", "sourceLink": "https://www.rask.ai/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "Chronicle", "imgUrl": "9c4f99d19cdf7e18.png", "desc": "AI高颜值演示文稿创建", "sourceLink": "https://chroniclehq.com/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "Presentations.AI", "imgUrl": "b1494207d55c5c79.png", "desc": "演示文档版的ChatGPT", "sourceLink": "https://www.presentations.ai/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "SlidesAI", "imgUrl": "1bc794202d90e45f.png", "desc": "AI快速创建演示幻灯片", "sourceLink": "https://www.slidesai.io/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "auxi", "imgUrl": "2df2ab0622ffe231.png", "desc": "功能强大的PowerPoint AI插件", "sourceLink": "https://www.auxi.ai/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "AI灵感PPT", "imgUrl": "fba05efde39e8900.png", "desc": "免费高效的AIPPT生成工具", "sourceLink": "https://www.lgppt.cn/?_channel=ai-nav-hnch"}, {"category": "AI幻灯片和演示", "title": "MindShow", "imgUrl": "2291aaf4168d2a18.png", "desc": "国内独立开发者开发的输入内容自动生成演示工具", "sourceLink": "https://www.mindshow.fun/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "问小白", "imgUrl": "2fcd205c0e3cc3b5.png", "desc": "元石科技推出的AI智能助手，支持DeepSeek满血版", "sourceLink": "https://wenxiaobai.paluai.com/aibot?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "豆包", "imgUrl": "3b4058286444ff72.png", "desc": "字节跳动推出的免费AI智能助手", "sourceLink": "http://dis.haodxi.cn/dbff/dbaiboa2.html?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "讯飞星火", "imgUrl": "47681a3a39732d0d.png", "desc": "AI智能助手，支持PPT生成、深度推理", "sourceLink": "https://ai-bot.cn/xinghuo-xfyun?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "逗逗", "imgUrl": "fafa9cd12930ddd2.png", "desc": "AI游戏陪玩，支持原神、黑神话、LOL！", "sourceLink": "https://doudou.paluai.com/web_aitool?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "ChatGPT", "imgUrl": "35d95d86dfb312a6.png", "desc": "OpenAI旗下AI对话工具", "sourceLink": ""}, {"category": "AI对话聊天", "title": "<PERSON>", "imgUrl": "c8d859efe6972da7.png", "desc": "Anthropic公司推出的对话式AI智能助手", "sourceLink": ""}, {"category": "AI对话聊天", "title": "DeepSeek", "imgUrl": "8fb007b8f9701011.png", "desc": "幻方量化旗下深度求索推出的开源大模型和聊天助手", "sourceLink": "https://www.deepseek.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON><PERSON>", "imgUrl": "dd9af078c949637c.png", "desc": "阿里通义推出的 Qwen 最新模型体验平台", "sourceLink": "https://ai-bot.cn/qwen-chat?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON>i智能助手", "imgUrl": "9dcac0fb8809b0d5.png", "desc": "月之暗面推出的AI智能助手", "sourceLink": "https://www.kimi.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "MiniMax", "imgUrl": "f71641802dbfb21a.png", "desc": "MiniMax推出的AI智能问答助手", "sourceLink": "https://chat.minimaxi.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "腾讯元宝", "imgUrl": "4acf5c5a784e2944.png", "desc": "腾讯推出的免费AI智能助手", "sourceLink": "https://yuanbao.tencent.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Gemini", "imgUrl": "790c23e0775ebe08.webp", "desc": "Google推出的AI聊天对话机器人Gemini", "sourceLink": "https://deepmind.google/technologies/gemini?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Z.ai", "imgUrl": "aaad91842c70d92d.png", "desc": "智谱面向全球推出的最新模型体验平台", "sourceLink": "https://ai-bot.cn/z-ai?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Grok", "imgUrl": "fc9ca6172c3c6391.png", "desc": "马斯克旗下xAI推出的人工智能助手", "sourceLink": "https://x.ai/grok?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON>", "imgUrl": "1a756f1326f3e878.png", "desc": "问答社区Quora推出的问答机器人工具", "sourceLink": ""}, {"category": "AI对话聊天", "title": "智谱清言", "imgUrl": "7f90ea2a37c1d48d.png", "desc": "智谱推出的全能AI助手", "sourceLink": "https://chatglm.cn/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "通义", "imgUrl": "eea05fc5df3def00.png", "desc": "阿里推出的自研大模型，通情、达义，你的全能AI助手！", "sourceLink": "https://tongyi.aliyun.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "华为小艺", "imgUrl": "1bba4cb7a66c0480.png", "desc": "华为旗下小艺AI助手网页端，已接入DeepSeek-R1", "sourceLink": "https://ai-bot.cn/huawei-xiaoyi?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "书生大模型", "imgUrl": "0ef705775a62c049.png", "desc": "上海人工智能实验室推出的系列AI模型", "sourceLink": "https://ai-bot.cn/sites/53308.html?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "文心一言", "imgUrl": "94f4c5f628e12808.png", "desc": "百度推出的基于文心大模型的AI对话互动工具", "sourceLink": "https://yiyan.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "阶跃AI", "imgUrl": "912df2e931015d25.png", "desc": "阶跃星辰推出的支持多模态的AI聊天机器人", "sourceLink": "https://yuewen.cn/chats/new?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "百小应", "imgUrl": "11bec1aa671fe5c7.png", "desc": "百川智能推出的免费AI助手", "sourceLink": "https://ying.baichuan-ai.com/chat?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "天工AI", "imgUrl": "551872324234c4ed.png", "desc": "昆仑万维推出的AI智能助手", "sourceLink": "https://www.tiangong.cn/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Copilot", "imgUrl": "f9ce38546cb52a4b.png", "desc": "微软推出的网页版Copilot助手", "sourceLink": "https://copilot.microsoft.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Character.AI", "imgUrl": "1184d88ca88f06a9.png", "desc": "创建虚拟角色并与其对话", "sourceLink": ""}, {"category": "AI对话聊天", "title": "J1 Assistant", "imgUrl": "32eea569d4e8507d.png", "desc": "罗永浩旗下 Jarvis 项目推出的 AI 智能助手", "sourceLink": "https://ai-bot.cn/j1-assistant?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Meta AI助手", "imgUrl": "1977e8a1a2d8589c.png", "desc": "Meta推出的免费AI聊天助手", "sourceLink": "https://www.meta.ai/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON>新必应", "imgUrl": "8deb72edc088ed11.png", "desc": "微软推出的新版结合了ChatGPT功能的必应", "sourceLink": ""}, {"category": "AI对话聊天", "title": "Koko AI", "imgUrl": "b638b9b7d96925c5.png", "desc": "Seele公司推出的「AI+3D」情感陪伴产品", "sourceLink": "https://www.seeles.ai/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Me.bot", "imgUrl": "713389306a4ec82e.webp", "desc": "心识宇宙推出的个性化AI伴侣产品", "sourceLink": "https://www.me.bot/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON><PERSON>", "imgUrl": "a39b03ddb34a042a.png", "desc": "AI驱动的故事角色扮演游戏应用，沉浸式的剧本互动体验", "sourceLink": "https://www.sayloai.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "通义星尘", "imgUrl": "dd9af078c949637c.png", "desc": "用AI定制属于你自己的IP角色", "sourceLink": "https://tongyi.aliyun.com/xingchen/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "CueMe", "imgUrl": "4ec3627fa3fce11a.png", "desc": "夸克推出的AI智能对话助手，支持2万字长文写作", "sourceLink": "https://cueme.cn/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "造梦次元", "imgUrl": "0fd711d705da85e3.png", "desc": "AI互动内容平台，虚拟角色逗你开心", "sourceLink": "https://ciyuan.ideaflow.pro/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Museland", "imgUrl": "5f28f3a9c664f412.png", "desc": "沉浸式AI角色扮演产品", "sourceLink": "https://www.museland.ai/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "百度AI助手", "imgUrl": "354c8153a8d48981.png", "desc": "百度推出的多场景AI智能体助手", "sourceLink": "https://chat.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "商量SenseChat", "imgUrl": "b1d18c84cf95ad83.png", "desc": "商汤科技推出的免费AI聊天助手", "sourceLink": "https://chat.sensetime.com/wb/home?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "小悟空", "imgUrl": "a7cabd1674e2c852.png", "desc": "字节跳动推出的免费AI对话助手和个人助理", "sourceLink": "https://wukong.com/tool?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "紫东太初", "imgUrl": "b781b09356d94ece.png", "desc": "中科院与武智院推出的千亿参数全模态大模型和助手", "sourceLink": "https://taichu-web.ia.ac.cn/?_channel=ai-nav-hnch#/welcome"}, {"category": "AI对话聊天", "title": "小黄蕉", "imgUrl": "2d8013580e08eabd.png", "desc": "字节跳动旗下推出的AI虚拟交友聊天平台", "sourceLink": "https://chatwiz.cn/h5/feely?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "冒泡鸭", "imgUrl": "1d39b510c2a5d0e6.png", "desc": "阶跃星辰推出的AI聊天机器人和智能体平台", "sourceLink": "https://maopaoya.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Cici", "imgUrl": "1d434c4abe4fc24a.png", "desc": "豆包国际版，字节跳动面向海外市场推出的AI助手", "sourceLink": "https://www.ciciai.com/browser-extension/landing/zh/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "百川大模型", "imgUrl": "2e9bb8e9ad2386c8.png", "desc": "百川智能推出的大模型助手，融合了意图理解、信息检索以及强化学习技术", "sourceLink": "https://chat.baichuan-ai.com/home?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON>", "imgUrl": "71c6c0184356366f.png", "desc": "Mistral推出的AI对话聊天助手", "sourceLink": "https://chat.mistral.ai/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "百度AI伙伴", "imgUrl": "235fb24e18c5200e.png", "desc": "百度最新上线的AI搜索对话工具", "sourceLink": "https://chat.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "超级助理", "imgUrl": "9cda824e597fdcc8.png", "desc": "百度智能云发布的基于文心一言的AI原生应用和Copilot“超级助理”", "sourceLink": "https://cloud.baidu.com/product/infoflow.html?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "钉钉·个人版", "imgUrl": "fcb8ade6727172db.png", "desc": "钉钉推出的个人版办公应用程序，内置AI智能助手，可进行AI创作、AI对话、AI绘画", "sourceLink": "https://workspace.dingtalk.com/welcome?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Wanderboat", "imgUrl": "f86764884571e673.png", "desc": "硅谷初创公司UTA AI推出的AI旅行助手", "sourceLink": "https://wanderboat.ai/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON><PERSON>", "imgUrl": "ebcbf0d9ab648538.png", "desc": "基于孟子GPT大模型的AI对话机器人", "sourceLink": "https://www.langboat.com/portal/mengzi-gpt?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Luca面壁露卡", "imgUrl": "3f40233a1fc8e266.png", "desc": "面壁智能推出的千亿多模态大模型免费智能对话助手", "sourceLink": "https://luca-beta.modelbest.cn/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "元象XChat", "imgUrl": "b6e19247632dbedb.png", "desc": "元象XVERSE大模型驱动的AI聊天助手", "sourceLink": "https://chat.xverse.cn/home/<USER>"}, {"category": "AI对话聊天", "title": "ChitChop", "imgUrl": "6cb4a752570cfe72.png", "desc": "字节旗下面向海外用户推出的免费大模型产品和AI助手工具箱", "sourceLink": "https://www.chitchop.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "魔搭GPT（ModelScopeGPT）", "imgUrl": "d9b6127b1d71f340.png", "desc": "阿里达摩院推出的大小模型协同的智能助手，具备作诗、绘画、视频生成、语音播放等多模态能力", "sourceLink": "https://www.modelscope.cn/studios/damo/ModelScopeGPT?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Forefront", "imgUrl": "c5b0f45d3ee41948.png", "desc": "提供GPT-3.5、GPT-4、<PERSON>的AI聊天机器人", "sourceLink": ""}, {"category": "AI对话聊天", "title": "HuggingChat", "imgUrl": "ccbd65125b448e37.png", "desc": "HuggingFace推出的在线聊天机器人，基于Open Assistant模型", "sourceLink": ""}, {"category": "AI对话聊天", "title": "TigerBot", "imgUrl": "8e5539beb6b25f56.png", "desc": "虎博科技推出的AI对话聊天机器人，基于TigerBot开源大模型", "sourceLink": "https://tigerbot.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Stable Chat", "imgUrl": "301cde55dd15b4a1.png", "desc": "Stability AI 最新推出的免费聊天对话网站", "sourceLink": "https://research.stability.ai/chat?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "ColossalChat", "imgUrl": "bc07195e7048013f.png", "desc": "Colossal-AI推出的免费开源版ChatGPT聊天机器人替代品", "sourceLink": ""}, {"category": "AI对话聊天", "title": "<PERSON>", "imgUrl": "22fa5cff2d3a7d0f.png", "desc": "Jasper针对内容创作者出品的AI聊天工具", "sourceLink": ""}, {"category": "AI对话聊天", "title": "YouChat AI", "imgUrl": "04338b94ba72d695.png", "desc": "AI搜索对话工具", "sourceLink": ""}, {"category": "AI对话聊天", "title": "ChatSonic", "imgUrl": "e907694350cf5267.png", "desc": "WriteSonic出品的ChatGPT竞品", "sourceLink": ""}, {"category": "AI对话聊天", "title": "Replika", "imgUrl": "89510324a5ed0cde.png", "desc": "AI对话陪伴工具", "sourceLink": ""}, {"category": "AI对话聊天", "title": "Whispr", "imgUrl": "15e7f20a46a85d5f.png", "desc": "免费AI对话回应", "sourceLink": ""}, {"category": "AI对话聊天", "title": "MOSS", "imgUrl": "dc391eb6eb9ab56f.png", "desc": "复旦大学团队开发的对话式大型语言模型", "sourceLink": "https://moss.fastnlp.top/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "Open Assistant", "imgUrl": "f51fe42e145b89ab.png", "desc": "免费开源的对话式AI，GitHub星标超3万", "sourceLink": ""}, {"category": "AI对话聊天", "title": "Pi", "imgUrl": "ae95390c03e7e476.png", "desc": "DeepMind联创新公司推出的AI聊天机器人", "sourceLink": ""}, {"category": "AI对话聊天", "title": "Inworld", "imgUrl": "008beaefb0cd74e3.png", "desc": "开发和创建AI虚拟角色并与其互动", "sourceLink": ""}, {"category": "AI对话聊天", "title": "商量SenseChat", "imgUrl": "1cec08c080f0501e.png", "desc": "商汤科技推出的类ChatGPT的人工智能大语言模型", "sourceLink": "https://chat.sensetime.com/wb?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "360智脑", "imgUrl": "e6e71d7f737b3815.png", "desc": "360搜索最新推出的AI对话聊天机器人", "sourceLink": "https://ai.360.com/?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "e3cd67c68673c6d2.png", "desc": "集成了AI问答的AI搜索引擎", "sourceLink": "https://ai-bot.cn/sites/1890.html?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "对话写作猫", "imgUrl": "b2235a9473e1e49c.png", "desc": "秘塔写作猫推出的AI对话聊天工具", "sourceLink": "https://ai-bot.cn/sites/13.html?_channel=ai-nav-hnch"}, {"category": "AI对话聊天", "title": "应事AI", "imgUrl": "c65a3d6626231378.png", "desc": "MiniMax推出的AI对话助理，已免费开放", "sourceLink": "https://hailuoai.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "4e8fd178ef2c5489.png", "desc": "字节跳动推出的免费AI编程工具，基于Claude模型", "sourceLink": "https://trae.paluai.com/trae?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "通义灵码", "imgUrl": "ab95d7030f0266b1.png", "desc": "阿里推出的免费AI编程工具，基于通义大模型", "sourceLink": "https://click.aliyun.com/m/1000399943?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "文心快码", "imgUrl": "b930d60e80a3a87d.png", "desc": "百度推出的AI编程助手，基于文心大模型", "sourceLink": "https://comate.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "豆包AI编程", "imgUrl": "3b4058286444ff72.png", "desc": "豆包推出的AI编程新功能", "sourceLink": "https://ai-bot.cn/doubao-aicoding?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "09fc32e39bd1d286.png", "desc": "AI代码编辑器，快速进行编程和软件开发", "sourceLink": "https://www.cursor.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "GitHub Copilot", "imgUrl": "cc428a5381723349.png", "desc": "GitHub推出的AI编程工具", "sourceLink": "https://github.com/features/copilot?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "代码小浣熊", "imgUrl": "55485b5e42ce64ae.png", "desc": "商汤科技推出的免费AI编程助手", "sourceLink": "https://www.xiaohuanxiong.com/login?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Firebase Studio", "imgUrl": "95546dee2b772582.png", "desc": "谷歌推出的AI编程工具，一站式开发全栈应用", "sourceLink": "https://ai-bot.cn/firebase-studio?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Windsurf", "imgUrl": "12b49c5a17d5f48a.png", "desc": "Codeium公司推出的AI编程工具", "sourceLink": "https://ai-bot.cn/windsurf?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Bolt.new", "imgUrl": "7acb011c58a29018.png", "desc": "StackBlitz 推出的全栈AI代码工具，可以看作 Artfacts、V0 和 Replit 的结合体", "sourceLink": "https://ai-bot.cn/bolt%E2%80%A4new?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Replit Agent", "imgUrl": "9af07bf74931b3e4.png", "desc": "AI初创公司Replit推出的AI编程工具", "sourceLink": "https://ai-bot.cn/replit-agent?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Lovable", "imgUrl": "aa2b2fe8da7cbe8d.png", "desc": "AI编程工具，用自然对话快速构建网站和Web应用", "sourceLink": "https://ai-bot.cn/lovable?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeBuddy", "imgUrl": "c692715e9223f6f8.png", "desc": "腾讯推出的AI编程助手", "sourceLink": "https://ai-bot.cn/codebuddy?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imgUrl": "fa476db56a88d289.png", "desc": "亚马逊推出的免费AI编程助手", "sourceLink": "https://aws.amazon.com/codewhisperer?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "30ef6ff2b93d7f1a.png", "desc": "JetBrains 推出的 AI 编程助手", "sourceLink": "https://ai-bot.cn/junie?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodiumAI", "imgUrl": "a51e00515668481c.png", "desc": "免费的AI代码测试和分析工具", "sourceLink": "https://www.codium.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeGeeX", "imgUrl": "9f7f015c1290d7e9.png", "desc": "智谱AI推出的免费AI编程助手", "sourceLink": "https://codegeex.cn/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON>", "imgUrl": "dfbbd15771d5f47f.png", "desc": "Sourcegraph推出的免费AI编程工具", "sourceLink": "https://about.sourcegraph.com/cody?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "DevChat", "imgUrl": "4e3269ed46b65e5a.png", "desc": "开源的支持多款大模型的AI编程助手", "sourceLink": "https://www.devchat.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "b842f1870568c477.png", "desc": "Cosine AI推出的AI编程助手", "sourceLink": "https://ai-bot.cn/genie?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "iFlyCode", "imgUrl": "aee02300053ad34b.png", "desc": "科大讯飞推出的智能编程助手", "sourceLink": "https://iflycode.xfyun.cn/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "f95b8617fa12e199.png", "desc": "专为 VS Code 设计的AI代码补全插件", "sourceLink": "https://twinny.dev/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Project IDX", "imgUrl": "21318e1be5428162.png", "desc": "谷歌推出的AI云端开发和代码编辑器", "sourceLink": "https://idx.dev/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Sketch2Code", "imgUrl": "23bf9982864793da.png", "desc": "微软AI Lab推出的将手绘草图转换成HTML代码工具", "sourceLink": "https://sketch2code.azurewebsites.net/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeFuse", "imgUrl": "5dc19975e34ea129.png", "desc": "蚂蚁集团推出的AI代码编程助手", "sourceLink": "https://codefuse.alipay.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "684fb64ce6b36b1e.png", "desc": "免费开源的自托管AI编程助手", "sourceLink": "https://tabby.tabbyml.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "C知道", "imgUrl": "8cc1dfdd4fa14de2.png", "desc": "CSDN推出的AI技术问答工具", "sourceLink": "https://so.csdn.net/chat?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "驭码CodeRider", "imgUrl": "48773789522d1088.png", "desc": "极狐GitLab推出的AI编程与软件智能研发助手", "sourceLink": "https://coderider.gitlab.cn/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON> Chat", "imgUrl": "9d2d7a48760180fa.png", "desc": "GitLab推出的AI编程助手", "sourceLink": "https://about.gitlab.com/gitlab-duo/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeRabbit", "imgUrl": "fe65fe3c0a2cba37.png", "desc": "AI驱动的代码审查平台", "sourceLink": "https://coderabbit.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Augment Code", "imgUrl": "0d33b008b1675985.png", "desc": "AI编程辅助工具，专为大型代码库设计", "sourceLink": "https://www.augmentcode.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON>", "imgUrl": "e71e7ed3a203d5f6.png", "desc": "首个全自主的AI软件工程师智能体", "sourceLink": "https://preview.devin.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Plandex", "imgUrl": "59f31947c9631937.png", "desc": "免费开源的基于终端的AI编程引擎", "sourceLink": "https://plandex.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Fitten Code", "imgUrl": "7e80d80d14173103.png", "desc": "非十科技推出的免费AI代码助手", "sourceLink": "https://code.fittentech.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "BLACKBOX AI", "imgUrl": "92f6d47b06e02300.png", "desc": "黑箱AI编程助理，快速代码生成", "sourceLink": "https://www.useblackbox.io/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Solo", "imgUrl": "296c3a3736ac156d.png", "desc": "Mozilla推出的零编程无代码AI网站建设工具", "sourceLink": "https://soloist.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "JetBrains AI", "imgUrl": "62744e8c645e396e.png", "desc": "JetBrains推出的AI编程开发助手", "sourceLink": "https://www.jetbrains.com/ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeArts Snap", "imgUrl": "a869079ab7c9ff30.png", "desc": "华为云推出的智能编程助手", "sourceLink": "https://www.huaweicloud.com/product/codeartside/snap.html?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Ask<PERSON><PERSON><PERSON>", "imgUrl": "24a7c5399d6b2513.png", "desc": "你的个人AI编程助手", "sourceLink": "https://www.askcodi.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "v0.dev", "imgUrl": "bcfa5921ec333569.png", "desc": "AI生成前端React/UI组件，由Vercel推出", "sourceLink": "https://v0.dev/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Boxy", "imgUrl": "9c40455c07b89fb8.png", "desc": "CodeSandbox推出的AI编程助手", "sourceLink": "https://codesandbox.io/blog/meet-boxy-ai-coding-assistant?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Quest AI", "imgUrl": "d7d697ca944d5ff3.png", "desc": "AI将设计稿生成React代码，支持JavaScript和TypeScript", "sourceLink": "https://www.quest.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "天工智码Skycode", "imgUrl": "46253b76632229ca.png", "desc": "AI智能编程助手，轻松生成各种代码", "sourceLink": "https://sky-code.singularity-ai.com/index.html?_channel=ai-nav-hnch#"}, {"category": "AI编程工具", "title": "JamGPT", "imgUrl": "76200e6ed65510a1.png", "desc": "AI Debug调试助手", "sourceLink": "https://jam.dev/jamgpt?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "aiXcoder", "imgUrl": "b43953e932bd6c65.png", "desc": "自然语言到代码的方法级代码生成，以及多行智能代码补全", "sourceLink": "https://www.aixcoder.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "AirOps", "imgUrl": "dad1593ced5a2815.png", "desc": "AI SQL语句生成和修改", "sourceLink": "https://www.airops.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Imgcook", "imgUrl": "66cd0dc11c3c1ef9.png", "desc": "阿里推出的免费设计稿智能生成前端代码", "sourceLink": "https://www.imgcook.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Deco", "imgUrl": "130ee772f2966a72.png", "desc": "京东推出的设计稿一键生成多端代码工具", "sourceLink": "https://ling-deco.jd.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Ghostwriter", "imgUrl": "bd042c60d3d8d473.png", "desc": "知名在线编程IDE Replit推出的AI编程助手", "sourceLink": "https://replit.com/site/ghostwriter?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Codiga", "imgUrl": "ab02c3c05d5466bc.png", "desc": "AI代码实时分析", "sourceLink": "https://www.codiga.io/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Locofy", "imgUrl": "36e260044f06e646.png", "desc": "AI无代码工具将Figma、Adobe XD和Sketch设计转换成前端代码", "sourceLink": "https://www.locofy.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "<PERSON><PERSON>", "imgUrl": "ba5048f59e41882d.png", "desc": "AI智能将图片转换成HTML和CSS代码", "sourceLink": "https://fronty.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "MarsX", "imgUrl": "79d1709af9ed3741.png", "desc": "AI无代码软件开发", "sourceLink": "https://www.marsx.dev/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Tabnine", "imgUrl": "7dc7600d69376855.png", "desc": "AI代码自动补全编程助手", "sourceLink": "https://www.tabnine.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Mutable AI", "imgUrl": "b00fd2d102dec0ff.png", "desc": "人工智能加速软件开发", "sourceLink": "https://mutable.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Debuild", "imgUrl": "6082ff8b9c805f9c.png", "desc": "低代码快速开发网页应用", "sourceLink": "https://debuild.app/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Warp", "imgUrl": "112ca4d98fb5233e.png", "desc": "21世纪的终端工具（内置AI命令搜索）", "sourceLink": "https://www.warp.dev/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Fig", "imgUrl": "41fb8e09d996fbcb.png", "desc": "下一代命令行工具（内置AI终端命令自动补全）", "sourceLink": "https://fig.io/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "CodeSnippets", "imgUrl": "7247aa18f1e60d9a.png", "desc": "AI代码生成、补全、分析、重构和调试", "sourceLink": "https://codesnippets.ai/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Hocoos", "imgUrl": "f82fb3a734de276e.png", "desc": "无代码AI智能在线快速创建网站", "sourceLink": "https://hocoos.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "HTTPie AI", "imgUrl": "adafd9ede2412f00.gif", "desc": "AI API开发工具", "sourceLink": "https://httpie.io/ai?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "AI Code Reviewer", "imgUrl": "76a0c6b26a1d2372.png", "desc": "AI代码检查", "sourceLink": "https://ai-code-reviewer.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Visual Studio IntelliCode", "imgUrl": "c5627144bfd5af75.png", "desc": "Visual Studio AI辅助开发", "sourceLink": "https://visualstudio.microsoft.com/zh-hans/services/intellicode?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "HeyCLI", "imgUrl": "d46bfb1e9196ef17.png", "desc": "自然语言转义为CLI命令", "sourceLink": "https://www.heycli.com/?_channel=ai-nav-hnch"}, {"category": "AI编程工具", "title": "Codeium", "imgUrl": "1e6c93c05aae829a.png", "desc": "免费的AI编程工具，智能生成和补全代码", "sourceLink": "https://www.codeium.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "稿定AI", "imgUrl": "64711e0ee714e34c.png", "desc": "一站式AI创作和设计平台", "sourceLink": "https://www.gaoding.com/utms/1591aff1eb344c64a364a5e6dd7fcbdd?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Recraft AI", "imgUrl": "74792f914a25ba30.png", "desc": "免费无限AI画板，生成高质量矢量艺术画、图标、3D图片和插画", "sourceLink": "https://www.recraft.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "<PERSON><PERSON>", "imgUrl": "3880324fe1846432.png", "desc": "阿里国际推出的AI电商设计工具", "sourceLink": "https://www.piccopilot.com/create/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Figma AI", "imgUrl": "6f91c0c29ad191b8.png", "desc": "Figma推出的原生AI设计工具", "sourceLink": "https://www.figma.com/ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "创客贴AI", "imgUrl": "bb25e89319713286.png", "desc": "AI辅助的智能在线设计工具", "sourceLink": "https://aiart.chuangkit.com/matrix?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "美图设计室", "imgUrl": "ffc94c71acb684e8.jpg", "desc": "AI图像创作和设计平台", "sourceLink": "https://www.designkit.com/tools?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "蚂上有创意", "imgUrl": "da2b313afb87866e.png", "desc": "支付宝推出的AI设计工具，面向商家提供电商设计服务", "sourceLink": "https://idesign.alipay.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "爱设计", "imgUrl": "31704bf8077829e6.png", "desc": "AI在线设计平台，提供多端在线拖拽设计工具", "sourceLink": "https://www.isheji.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "美间AI", "imgUrl": "b048e08a3b693fe6.png", "desc": "AI设计工具助手，智能海报、提案和商品图生成", "sourceLink": "https://www.meijian.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "魔力工作室", "imgUrl": "9a18ac91bbb30ffc.png", "desc": "Canva可画推出的一站式AI创作套件", "sourceLink": "https://www.canva.cn/magic/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Microsoft Designer", "imgUrl": "7d64bded97a7e426.png", "desc": "微软推出的在线设计海报和宣传图工具", "sourceLink": "https://designer.microsoft.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Onlook", "imgUrl": "7a0df97ea0fc7505.png", "desc": "开源AI视觉编辑工具，设计修改自动同步代码", "sourceLink": "https://ai-bot.cn/onlook?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Looka", "imgUrl": "7977cb76010260a5.png", "desc": "AI在线设计和生成logo", "sourceLink": "https://looka.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "智绘设计", "imgUrl": "7f70b1cb99d21044.png", "desc": "腾讯推出的智能设计平台，让内容更精彩", "sourceLink": "https://taishan.qq.com/brand/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "MasterGo AI", "imgUrl": "2844f22dfffdde80.png", "desc": "国产产品设计工具MasterGo推出的智能UI设计助手", "sourceLink": "https://mastergo.com/upcoming-ai/apply?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "墨刀AI", "imgUrl": "174e4681c9742d3e.png", "desc": "墨刀推出的AI产品原型设计助手", "sourceLink": "https://modao.cc/feature/ai.html?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "居然设计家", "imgUrl": "b3cb778da667cd4f.png", "desc": "居然之家联合阿里推出的AI家装设计平台", "sourceLink": "https://ai-bot.cn/design-shejijia?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "FigJam AI", "imgUrl": "6f91c0c29ad191b8.png", "desc": "Figma推出的AI白板协作设计工具", "sourceLink": "https://www.figma.com/figjam/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "鹿班", "imgUrl": "86be2cd0be66fc8d.png", "desc": "阿里推出的智能设计商品图和海报的平台", "sourceLink": "https://luban.aliyun.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Magic Design", "imgUrl": "9a18ac91bbb30ffc.png", "desc": "在线设计工具Canva推出的AI设计工具", "sourceLink": "https://www.canva.com/magic-design?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "简单设计", "imgUrl": "4cd91ac415118aef.png", "desc": "免费的在线设计、图片处理工具", "sourceLink": "https://www.jiandan.link/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "135 AI排版", "imgUrl": "8964a8a6dcf8a1c8.png", "desc": "公众号AI图文排版和智能文案生成工具", "sourceLink": "https://www.135editor.com/ai_editor/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "笔格设计", "imgUrl": "f5526ebaa4fbb444.png", "desc": "AI设计工具合集，包括文生图、智能消除等", "sourceLink": "https://bigesj.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Logoai", "imgUrl": "2c8f303b9297ab97.png", "desc": "AI LOGO创建设计平台，一站式品牌打造", "sourceLink": "https://www.logoai.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "豆绘AI", "imgUrl": "f351816bc012bad7.png", "desc": "AI绘图设计平台，一键生成720°VR全景图", "sourceLink": "https://www.douhuiai.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "千图网", "imgUrl": "7033cb9c854b528e.png", "desc": "在线设计图片素材平台", "sourceLink": "https://www.58pic.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Pictographic", "imgUrl": "89313fb797a03f01.png", "desc": "AI插图资源库和生成平台", "sourceLink": "https://pictographic.io/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Fable Prism", "imgUrl": "13b81f51dfa4fcdb.png", "desc": "AI动效设计和动画效果制作工具", "sourceLink": "https://www.fable.app/prism?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Wegic", "imgUrl": "7c56ac6266fdc203.png", "desc": "AI网页设计和建站开发工具", "sourceLink": "https://wegic.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "匠紫", "imgUrl": "a67a269ef6ee0f48.png", "desc": "一站式AI设计工具", "sourceLink": "https://jiangziai.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Collov AI", "imgUrl": "96ffbaf6ddc95f51.png", "desc": "AI室内家居设计生成平台", "sourceLink": "https://collov.cn/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "包图网AI素材库", "imgUrl": "0e8d6233fd66de41.png", "desc": "包图网提供的特色图库服务", "sourceLink": "https://ibaotu.com/tupian/shuziyishu.html?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "易可图", "imgUrl": "877eb29a3633394c.png", "desc": "免费的AI图片编辑和海报设计平台", "sourceLink": "https://www.yiketu.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "笔魂AI", "imgUrl": "7699c7f1319452b6.png", "desc": "AI设计工具，支持AI抠图、消除、无损放大", "sourceLink": "https://ibihun.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "c9a531ee792ae1bf.png", "desc": "AI驱动的UI和UX设计工具", "sourceLink": "https://creatie.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Kittl", "imgUrl": "7a9fe8cb1451e9fa.png", "desc": "AI驱动的平面图形设计工具", "sourceLink": "https://www.kittl.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Dzine", "imgUrl": "aaeabc32564adb89.png", "desc": "一站式AI图像编辑和设计工具", "sourceLink": "https://www.dzine.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Ilus AI", "imgUrl": "bd90e9a7929b92bf.png", "desc": "AI插画插图生成工具", "sourceLink": "https://ilus.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "酷家乐AI", "imgUrl": "c4a4e942558ed170.png", "desc": "功能强大的AI家居设计软件", "sourceLink": "https://www.kujiale.cn/activities/AI-kujiale?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Pixso AI", "imgUrl": "185e3b28e6823146.jpg", "desc": "国产在线设计工具Pixso的内置AI助手，支持AI文生图、AI对话、AI设计等", "sourceLink": "https://pixso.cn/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Framer AI", "imgUrl": "60efca2244bc5bfc.png", "desc": "Framer推出的AI网站自动设计、生成和上线", "sourceLink": "https://www.framer.com/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "LogoliveryAI", "imgUrl": "33b48ee7e4ccc001.png", "desc": "免费的AI Logo生成器，提供SVG矢量格式", "sourceLink": "https://logolivery.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Motiff 妙多", "imgUrl": "859b29e907cb4b2e.png", "desc": "猿辅导旗下推出的AI界面设计工具", "sourceLink": "https://motiff.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Pimento", "imgUrl": "fae86d773e19a6cd.png", "desc": "人工智能驱动的设计创意和视觉参考平台", "sourceLink": "https://www.pimento.design/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Logo Diffusion", "imgUrl": "2550c7f98eaf2da7.png", "desc": "AI驱动的Logo和标志生成工具", "sourceLink": "https://logodiffusion.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Realibox AI", "imgUrl": "f8f7d8957009a8dc.png", "desc": "AI免费将草图/模型生成3D渲染图", "sourceLink": "https://www.realibox.com/product/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Vectorizer.AI", "imgUrl": "991a63fec3edd3cd.png", "desc": "AI一键将位图转换为矢量图片", "sourceLink": "https://vectorizer.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "模袋云AI", "imgUrl": "5fcc034b01d4ebff.png", "desc": "建筑AI创作平台，专注于大型建筑、小型住宅、室内设计、景观的出图和AI模型训练", "sourceLink": "https://www.modaiyun.com/mdy/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Vizcom", "imgUrl": "164315c54959b3f6.png", "desc": "AI渲染转化手绘图为产品设计图", "sourceLink": "https://www.vizcom.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Dora AI", "imgUrl": "e69000184263d5fd.png", "desc": "AI在线生成精美3D动画的网站", "sourceLink": "https://www.dora.run/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Designs.ai", "imgUrl": "84975c82789d0cfd.png", "desc": "AI设计工具", "sourceLink": "https://designs.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Galileo AI", "imgUrl": "281688751cc29753.png", "desc": "AI高保真原型设计", "sourceLink": "https://www.usegalileo.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Spline AI", "imgUrl": "7eb67acf77944f20.png", "desc": "Spline推出的AI生成3D物体、动画、材质", "sourceLink": "https://spline.design/ai?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "千图设计室AI海报", "imgUrl": "d73a259d8383451b.png", "desc": "免费批量生成在线可编辑的AI海报工具", "sourceLink": "https://hihaibao.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "illostrationAI", "imgUrl": "2f1f993c6303ab38.png", "desc": "AI插画生成，low poly、3D、矢量、logo、像素风、皮克斯等风格", "sourceLink": "https://www.illostration.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Uizard", "imgUrl": "8c43f287c77c412a.png", "desc": "AI网页、App和UI设计，快速生成应用和网站原型", "sourceLink": "https://uizard.io/ai-design?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Luma AI", "imgUrl": "2f0b1e9499e3e523.png", "desc": "AI 3D捕捉、建模和渲染", "sourceLink": "https://lumalabs.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "图宇宙", "imgUrl": "1dec862ac828b38d.png", "desc": "高品质AI智能设计平台", "sourceLink": "https://www.nolibox.com/introduction?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "阿里云智能logo设计", "imgUrl": "578995bee07e737e.png", "desc": "阿里云推出的智能Logo设计", "sourceLink": "https://ai-bot.cn/sites/1989.html?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "AIDesign", "imgUrl": "2d4f2b78cd30aecf.png", "desc": "腾讯推出的免费AI Logo在线设计工具", "sourceLink": "https://ai-bot.cn/sites/1957.html?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Fabrie AI", "imgUrl": "6984b979b51c0627.png", "desc": "在线白板协作平台Fabrie推出的AI设计助手，支持多种渲染模式", "sourceLink": "https://kebuxi.datasink.sensorsdata.cn/r/dF?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Poly", "imgUrl": "772df869a3f06ba7.png", "desc": "AI生成3D材质", "sourceLink": "https://withpoly.com/browse/textures?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Illustroke", "imgUrl": "252fc406cf035f24.png", "desc": "AI SVG矢量插画生成工具", "sourceLink": "https://illustroke.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Eva Design System", "imgUrl": "070113b95913c7fd.png", "desc": "基于深度学习的色彩生成工具", "sourceLink": "https://colors.eva.design/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Color Wheel", "imgUrl": "b2e07d3825ff81cb.png", "desc": "AI灰度logo或插画上色工具", "sourceLink": "https://brandmark.io/color-wheel?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "a4fb44881944c73b.png", "desc": "AI调色生成工具", "sourceLink": "https://huemint.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "ColorMagic", "imgUrl": "f4561b9fb4842bfa.png", "desc": "AI调色板生成工具", "sourceLink": "https://www.obviously.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Logomaster.ai", "imgUrl": "14904f0de2d7bf9e.png", "desc": "AI Logo生成工具", "sourceLink": "https://logomaster.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Magician", "imgUrl": "e2d4d749851975d9.png", "desc": "Figma插件，AI生成图标、图片和UX文案", "sourceLink": "https://magician.design/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Appicons AI", "imgUrl": "41c682a98e2cda5f.png", "desc": "AI生成精美App图标", "sourceLink": "https://appicons.ai/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "IconifyAI", "imgUrl": "9a068ad41be95918.png", "desc": "AI App图标生成器", "sourceLink": "https://www.iconifyai.com/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "b35d4d5116d48471.png", "desc": "AI调色盘生成工具", "sourceLink": "https://www.khroma.co/?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "即时AI", "imgUrl": "d3d66b869997955d.png", "desc": "即时设计推出的由文本描述生成可编辑的原型设计稿", "sourceLink": "https://jsai.cc/ai/create?_channel=ai-nav-hnch"}, {"category": "AI设计工具", "title": "Alpaca", "imgUrl": "0ab0097b9b8086e0.png", "desc": "将生成式AI集成到Photoshop图像设计中", "sourceLink": "https://www.getalpaca.io/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "问小白", "imgUrl": "2fcd205c0e3cc3b5.png", "desc": "DeepSeek-R1满血版免费不限次，支持联网搜索", "sourceLink": "https://wenxiaobai.paluai.com/aibot?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "夸克AI", "imgUrl": "e1391faf690e07a2.png", "desc": "集AI搜索、网盘、文档、创作等功能于一体的应用", "sourceLink": "https://b.quark.cn/apps/qkhomepage_twofoufeb/routes/model?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "秘塔AI搜索", "imgUrl": "53860dffdd76d894.png", "desc": "最好用的AI搜索工具，没有广告，直达结果", "sourceLink": "https://metaso.cn/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Perplexity", "imgUrl": "79d9327c7ffa077b.png", "desc": "强大的对话式AI搜索引擎", "sourceLink": "https://www.perplexity.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "SearchGPT", "imgUrl": "7f0d4a4e03d8c4ba.png", "desc": "OpenAI最新推出的AI搜索引擎", "sourceLink": ""}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "cf48bad6a7257088.png", "desc": "面向程序员的新一代AI搜索引擎", "sourceLink": "https://devv.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "AMiner", "imgUrl": "c7e35aae8ce7398a.png", "desc": "智谱AI推出的大模型学术平台", "sourceLink": "https://www.aminer.cn/open/research?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "心流", "imgUrl": "6993d5b38601662d.png", "desc": "阿里旗下推出的AI搜索助手", "sourceLink": "https://iflow.cn/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "点点", "imgUrl": "9bd50702a3927959.png", "desc": "小红书推出的 AI 搜索应用，主打生活场景", "sourceLink": "https://ai-bot.cn/diandian?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "知乎直答", "imgUrl": "092945453e52108b.png", "desc": "知乎推出的AI搜索引擎，直达问题答案", "sourceLink": "https://zhida.zhihu.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "纳米搜索", "imgUrl": "3f174315c454ade3.png", "desc": "360公司推出的AI搜索应用，一切皆可生成视频", "sourceLink": "https://ai-bot.cn/namiso?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "百度AI探索版", "imgUrl": "160c4b0bd7badaa9.png", "desc": "百度推出的深度AI搜索引擎", "sourceLink": "https://think.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "bbd100eac89be9e6.png", "desc": "完全免费的AI学术搜索引擎", "sourceLink": "https://lumina.sh/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "fae23eb7370aee14.png", "desc": "免费AI智能搜索引擎，支持社交联网搜索和多语种问答结果", "sourceLink": "https://felo.ai/search?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "360AI搜索", "imgUrl": "07b7c4734e0809bc.png", "desc": "360推出的新一代AI搜索引擎", "sourceLink": "https://so.360.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "天工AI搜索", "imgUrl": "551872324234c4ed.png", "desc": "昆仑万维最新推出的结合大模型的AI搜索引擎", "sourceLink": "https://tiangong.cn/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Exa AI", "imgUrl": "1b9db2997903d5ff.png", "desc": "专门为AI模型设计的搜索引擎平台", "sourceLink": "https://exa.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "c7a42d2d75990c57.png", "desc": "全球产品信息AI搜索引擎，能语义化搜索任何公开的产品与公司", "sourceLink": "https://www.reddo.cloud/search_home?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "博查AI搜索", "imgUrl": "3cf2a93059e1b626.png", "desc": "支持多模型的AI搜索引擎", "sourceLink": "https://bochaai.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "链企AI", "imgUrl": "f2e0950c1e473691.png", "desc": "链企智能推出的AI商业搜索工具", "sourceLink": "https://www.lianqiai.cn/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "问问小宇宙", "imgUrl": "a19be0109a3999b7.png", "desc": "小宇宙推出的AI搜索产品", "sourceLink": "https://ask.xiaoyuzhoufm.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Dexa AI", "imgUrl": "7afc6dcb551cdc34.png", "desc": "AI播客搜索工具", "sourceLink": "https://dexa.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "CuspAI", "imgUrl": "f3e7379434387a2f.png", "desc": "剑桥大学推出的材料学专业AI搜索工具", "sourceLink": "https://www.cusp.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "XAnswer", "imgUrl": "71590ebc1e395e37.png", "desc": "支持生成思维导图的免费AI搜索工具", "sourceLink": "https://www.xanswer.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Glean", "imgUrl": "9756e774d8ddc996.png", "desc": "专为职场人设计的AI搜索引擎", "sourceLink": "https://www.glean.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "AlphaSense", "imgUrl": "56415b9c58a1a99c.png", "desc": "专为金融专业人士设计的AI搜索工具", "sourceLink": "https://www.alpha-sense.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Globe Explorer", "imgUrl": "9d8ddb3c1ab57d31.png", "desc": "结构化AI知识搜索引擎", "sourceLink": "https://explorer.globe.engineer/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Reportify", "imgUrl": "60f63ab930a960fb.png", "desc": "AI投资研究问答搜索引擎", "sourceLink": "https://reportify.cc/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Phind", "imgUrl": "408837be0ed64a2f.png", "desc": "专为开发者设计的AI搜索引擎", "sourceLink": "https://www.phind.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "iAsk AI", "imgUrl": "572d28dbe9e89287.png", "desc": "快速准确的AI搜索引擎", "sourceLink": "https://iask.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Consensus", "imgUrl": "18f605499cdeb81f.png", "desc": "AI科研学术搜索引擎", "sourceLink": "https://consensus.app/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Komo Search", "imgUrl": "61eb871f045b9992.png", "desc": "简洁直观的AI搜索引擎", "sourceLink": "https://komo.ai/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Searcholic", "imgUrl": "5765e76b01b4663e.png", "desc": "AI驱动的电子书和文档搜索引擎", "sourceLink": "https://searcholic.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "2ca9d7faa64ceab8.png", "desc": "对话式人工智能搜索引擎", "sourceLink": "https://andisearch.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "ef84f090f8b591b1.png", "desc": "AI驱动的音乐百科搜索引擎", "sourceLink": "https://www.songtell.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "ThinkAny", "imgUrl": "0eacf0d8ed12bef9.png", "desc": "新时代的AI搜索引擎", "sourceLink": "https://thinkany.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "83fd5589aa905ebd.png", "desc": "快速精准的AI搜索引擎", "sourceLink": "https://www.hellomiku.com/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "Qdrant", "imgUrl": "90a70d55437a7751.png", "desc": "开源的向量数据库和向量相似性AI搜索引擎", "sourceLink": "https://qdrant.tech/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "<PERSON><PERSON>", "imgUrl": "525156db1137c300.png", "desc": "一个由AI驱动的 Web3 搜索引擎", "sourceLink": "https://www.adot.tech/?_channel=ai-nav-hnch"}, {"category": "AI搜索引擎", "title": "开搜AI", "imgUrl": "aca153bc62492ae6.png", "desc": "面向大众的免费AI问答搜索引擎", "sourceLink": "https://kaisouai.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "魔音工坊", "imgUrl": "3a0b0c20bebdc73e.png", "desc": "AI配音工具，轻松配出媲美真人的声音", "sourceLink": "https://www.moyin.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "讯飞智作", "imgUrl": "efaa10d82d461864.png", "desc": "科大讯飞推出的AI转语音和配音工具", "sourceLink": "https://peiyin.xunfei.cn/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON>", "imgUrl": "7e4128bf002bfa9a.png", "desc": "高质量的AI音乐创作平台", "sourceLink": "https://www.suno.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "海绵音乐", "imgUrl": "6866150d307b171d.png", "desc": "字节跳动推出的免费AI音乐创作和发现平台", "sourceLink": "https://www.haimian.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "音疯", "imgUrl": "d9b1b3658575d90c.png", "desc": "昆仑万维推出的AI音乐创作平台，一键生成原创歌曲", "sourceLink": "https://www.yinfeng.cn/home?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "ElevenLabs", "imgUrl": "8897a3acb36bf088.png", "desc": "AI文本转语音，支持包含中文在内的28种语言", "sourceLink": "https://try.elevenlabs.io/mqbahm8egbk8?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "琅琅配音", "imgUrl": "24cfab4603e15a05.png", "desc": "智能文本转语音工具", "sourceLink": "https://lang123.top/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Noiz AI", "imgUrl": "8a733f542bae1828.png", "desc": "AI配音工具，支持文本转语音和声音克隆", "sourceLink": "https://ai-bot.cn/noiz-ai?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "MiniMax Audio", "imgUrl": "f71641802dbfb21a.png", "desc": "MiniMax推出的AI语音合成工具，支持声音克隆", "sourceLink": "https://ai-bot.cn/minimax-audio?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "8c0abf6c25106748.png", "desc": "昆仑万维推出的 AI 音乐商用创作平台", "sourceLink": "https://ai-bot.cn/mureka?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "天谱乐", "imgUrl": "77c50a3eb1805ee2.png", "desc": "唱鸭团队推出的首个多模态音乐生成大模型", "sourceLink": "https://ai-bot.cn/tianpuyue?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "音剪", "imgUrl": "1cc465eec41ec9d0.png", "desc": "喜马拉雅推出的一站式AI音频创作平台", "sourceLink": "https://audioeditor.ximalaya.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "讯飞听见", "imgUrl": "465d545689f5cd1c.png", "desc": "科大讯飞推出的在线AI语音转文字工具", "sourceLink": "https://www.iflyrec.com/zhuanwenzi.html?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "MemoAI", "imgUrl": "cab581181dd065ef.png", "desc": "免费的AI语音转文字工具", "sourceLink": "https://memo.ac/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Reecho睿声", "imgUrl": "15a8926e01624478.png", "desc": "超拟真的中英文AI语音克隆/生成平台", "sourceLink": "https://www.reecho.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Udio", "imgUrl": "44c52a846e62f591.png", "desc": "免费的AI音乐创作工具，每月可生成1200首歌曲", "sourceLink": "https://www.udio.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "网易天音", "imgUrl": "5870bc264b01dea0.png", "desc": "网易推出的一站式AI音乐创作工具", "sourceLink": "https://tianyin.163.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "TME Studio", "imgUrl": "42772eba31142792.png", "desc": "腾讯音乐推出的智能音乐创作助手", "sourceLink": "https://y.qq.com/tme_studio/index.html?_channel=ai-nav-hnch#"}, {"category": "AI音频工具", "title": "Lyrics Into Song AI", "imgUrl": "180dfd13bab4b6f5.png", "desc": "在线AI音乐创作工具，输入歌词创建个性化歌曲", "sourceLink": "https://lyricsintosong.ai/zh?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Stable Audio", "imgUrl": "618b1e18ead43578.png", "desc": "Stability AI最新推出的音乐生成工具", "sourceLink": "https://www.stableaudio.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "TextToSpeech", "imgUrl": "a46a139705f433d5.png", "desc": "完全免费的AI文字转语音工具", "sourceLink": "https://texttospeech.im/zh-CN?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "TTSMaker", "imgUrl": "9836935b03e1d38d.png", "desc": "马克配音（MakVoice）推出的免费AI文字转语音工具", "sourceLink": "https://ttsmaker.cn/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "LOVO AI", "imgUrl": "19eb7a073d333929.png", "desc": "专业的AI文字转语音工具，支持500+声音和100种语言", "sourceLink": "https://lovo.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "8d44d501853658dc.png", "desc": "开源的AI语音生成社区，5000多种不同的声音", "sourceLink": "https://uberduck.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Sonauto", "imgUrl": "df6039a5d062f44b.png", "desc": "免费的AI音乐生成和歌曲创作工具", "sourceLink": "https://sonauto.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "天工SkyMusic", "imgUrl": "551872324234c4ed.png", "desc": "昆仑万维发布的国内首个AI音乐生成大模型", "sourceLink": "https://www.tiangong.cn/music?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "大饼AI变声", "imgUrl": "631e328cbcb797c7.png", "desc": "免费专业的AI变声软件，一键实时语音变声", "sourceLink": "https://dubbing.tech/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Supertone Shift", "imgUrl": "79da7ae4ac47563a.png", "desc": "AI驱动的实时语音变换软件", "sourceLink": "https://product.supertone.ai/shift?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Riffusion", "imgUrl": "d78a3c069ed22a69.png", "desc": "AI生成不同风格的音乐，免费开源", "sourceLink": "https://www.riffusion.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Adobe Podcast", "imgUrl": "d285935f116b2b48.png", "desc": "Adobe推出的在线AI音频录制和编辑工具", "sourceLink": "https://podcast.adobe.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "网易云音乐·X Studio", "imgUrl": "25e03d50a2713823.png", "desc": "网易云音乐与小冰智能联合推出的免费AI歌手音乐创作软件", "sourceLink": "https://xstudio.music.163.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "刺鸟配音", "imgUrl": "d26eb4be5844fb21.png", "desc": "刺鸟科技推出的专业AI配音工具", "sourceLink": "https://www.icnpy.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Wondercraft", "imgUrl": "6eff29f498cb39a0.png", "desc": "AI音频内容生成工具，可创建播客有声书等", "sourceLink": "https://www.wondercraft.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Fryderyk", "imgUrl": "91d1cabc8910a048.png", "desc": "AI音乐创作工具，集成了多种乐器声音", "sourceLink": "https://fryderyk.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Voicenotes", "imgUrl": "692cee6dbddf12bc.png", "desc": "AI驱动的语音笔记工具", "sourceLink": "https://voicenotes.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "OptimizerAI", "imgUrl": "6cf71fea114e9971.png", "desc": "AI声音效果生成工具", "sourceLink": "https://www.optimizerai.xyz/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "ACE Studio", "imgUrl": "46493cd999978d81.png", "desc": "AI歌声合成工具，输入歌词与旋律即可生成宛如真人的歌声", "sourceLink": "https://ace-studio.timedomain.cn/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "蓝藻AI", "imgUrl": "a1c7477b35407f8e.png", "desc": "云知声旗下的AI配音和声音克隆平台", "sourceLink": "https://aigc.unisound.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Deepgram", "imgUrl": "70d969f307ad0c27.png", "desc": "快速低成本的AI语音文本互转API平台", "sourceLink": "https://deepgram.partnerlinks.io/ai-bot?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Audiobox", "imgUrl": "ff95500ad5c39510.png", "desc": "Meta推出的免费开源的AI语音和声音生成模型", "sourceLink": "https://audiobox.metademolab.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "RESEMBLE.AI", "imgUrl": "279480b0dbdd2c8e.png", "desc": "AI人声生成工具", "sourceLink": "https://www.resemble.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "IBM Watson文字转语音", "imgUrl": "d0be655c1253e13b.png", "desc": "IBM Watson文字转语音", "sourceLink": "https://www.ibm.com/cloud/watson-text-to-speech?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "FakeYou", "imgUrl": "f324045e3c7295b6.png", "desc": "Deep Fake文本转语音", "sourceLink": "https://fakeyou.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "BGM猫", "imgUrl": "3cadeb96e5522bee.png", "desc": "灵动音科技推出的AI智能生成BGM音乐", "sourceLink": "https://bgmcat.com/home?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "快转字幕", "imgUrl": "8de647f41da1675a.png", "desc": "AI语音视频转文字和字幕的工具", "sourceLink": "https://www.kzzimu.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "悦音配音", "imgUrl": "76f8f0fe84733905.png", "desc": "AI智能在线配音语音合成工具", "sourceLink": "https://yueyin.zhipianbang.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "音虫", "imgUrl": "b65a49013f63680e.png", "desc": "内置AI音乐编曲的音乐制作工具", "sourceLink": "https://www.soundbug.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON>", "imgUrl": "ee14f038782b560d.png", "desc": "AI BGM背景音乐生成工具", "sourceLink": "https://mubert.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "beatoven.ai", "imgUrl": "7dba56e72768e521.png", "desc": "免版税AI音乐创建平台", "sourceLink": "https://www.beatoven.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "BeatBot", "imgUrl": "d270c6fafb7ca81b.png", "desc": "输入文本提示快速生成歌曲和音乐", "sourceLink": "https://beatbot.fm/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Audo Studio", "imgUrl": "e6820a5a11868ecc.png", "desc": "AI音频清洗工具（噪音消除、声音平衡、音量调节）", "sourceLink": "https://audo.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "NaturalReader", "imgUrl": "312f9bf75acd5860.png", "desc": "AI文本转语音工具", "sourceLink": "https://www.naturalreaders.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "AssemblyAI", "imgUrl": "b347f88239e58e3a.png", "desc": "转录和理解语音的AI模型", "sourceLink": "https://www.assemblyai.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "LALAL.AI", "imgUrl": "a6984a44355e76cd.png", "desc": "AI人声乐器分离和提取", "sourceLink": "https://www.lalal.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON>", "imgUrl": "a7cf582be6899830.png", "desc": "AI噪音消除工具", "sourceLink": "https://krisp.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Play.ht", "imgUrl": "65c0eae734ca7a04.png", "desc": "超真实在线AI语音生成", "sourceLink": "https://play.ht/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Murf AI", "imgUrl": "b1951742d0798f56.png", "desc": "AI文本转语音生成工具", "sourceLink": "https://murf.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Lemonaid", "imgUrl": "5b8a3a588e85661c.png", "desc": "AI音乐生成工具", "sourceLink": "https://www.lemonaide.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Soundraw", "imgUrl": "2e10f36c923ee105.png", "desc": "AI音乐生成工具", "sourceLink": "https://soundraw.io/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON>", "imgUrl": "2ba597249215fbe5.png", "desc": "AI快速生成原创音乐的平台", "sourceLink": "https://boomy.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "LOVO AI", "imgUrl": "19eb7a073d333929.png", "desc": "AI人声和文本转语音生成工具", "sourceLink": "https://lovo.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Typecast", "imgUrl": "2559d40c262835b0.png", "desc": "在线AI文字转语音生成工具", "sourceLink": "https://typecast.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Veed AI Voice Generator", "imgUrl": "6a2ba0d1d756b268.png", "desc": "Veed推出的AI语音生成器", "sourceLink": "https://www.veed.io/tools/text-to-speech-video/ai-voice-generator?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "<PERSON><PERSON><PERSON> AI旁白生成器", "imgUrl": "8c4a435d31221294.png", "desc": "Clipchamp的文字转语音生成器", "sourceLink": "https://clipchamp.com/zh-hans/features/ai-voice-over-generator?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "MetaVoice", "imgUrl": "d0490fb557735b4b.png", "desc": "AI实时变声工具", "sourceLink": "https://themetavoice.xyz/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Speechify", "imgUrl": "13557bb1a65ace56.png", "desc": "超2000万人都在用的文字转语音朗读器", "sourceLink": "https://speechify.com/zh-hans?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Voicemaker", "imgUrl": "59d18281f7790d80.png", "desc": "AI文本到语音生成工具", "sourceLink": "https://voicemaker.in/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Voice.ai", "imgUrl": "f95d88133a81b60b.png", "desc": "实时AI变声工具", "sourceLink": "https://voice.ai/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Listnr", "imgUrl": "c8aebf9a218814a2.png", "desc": "AI文本到语音生成器", "sourceLink": "https://www.listnr.tech/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Voicemod", "imgUrl": "764bf8fe7be702a1.png", "desc": "AI变声工具", "sourceLink": "https://www.voicemod.net/ai-voices?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "WellSaid Labs", "imgUrl": "05c98f013188bab5.png", "desc": "AI文本转语音工具", "sourceLink": "https://wellsaidlabs.com/?_channel=ai-nav-hnch"}, {"category": "AI音频工具", "title": "Notta", "imgUrl": "c27dc2b9bf687b05.png", "desc": "AI在线将语音转换成文字", "sourceLink": "https://www.notta.ai/en?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON>", "imgUrl": "d08976d8a68059d4.png", "desc": "海量AI智能体免费用，已接入DeepSeek满血版", "sourceLink": "http://dis.csqixiang.cn/unpo/cozeaibot.html?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "SiliconFlow", "imgUrl": "ebc5501158f26057.png", "desc": "生成式AI计算基础设施平台", "sourceLink": "https://cloud.siliconflow.cn/i/SjhsJgfH?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Zion", "imgUrl": "ac006147176b8659.png", "desc": "全栈开发AI Agent应用的无代码开发平台", "sourceLink": "https://ai-bot.cn/zion?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Dify", "imgUrl": "14f7810349bfcb00.png", "desc": "开源的生成式AI应用开发平台", "sourceLink": "https://ai-bot.cn/dify?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "秒哒", "imgUrl": "e08c4467c033e8fd.png", "desc": "百度推出的零代码AI开发平台，一句话生成应用", "sourceLink": "https://ai-bot.cn/miaoda?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "响指HaiSnap", "imgUrl": "283592ac653d56d6.png", "desc": "AI零代码应用开发平台", "sourceLink": "https://ai-bot.cn/haisnap?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "码上飞", "imgUrl": "cedc0aa249512108.png", "desc": "零代码AI应用开发平台，一句话生成应用", "sourceLink": "https://ai-bot.cn/codeflying?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "NoCode", "imgUrl": "6db24ed2149ac533.png", "desc": "美团推出的零代码AI应用开发平台", "sourceLink": "https://ai-bot.cn/nocode?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Wordware", "imgUrl": "b90bcc29a97b0549.png", "desc": "零代码构建AI Agent和应用的开发平台", "sourceLink": "https://ai-bot.cn/wordware?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "PPIO派欧云", "imgUrl": "2b967cda0bac3226.png", "desc": "AI云端一体化解决方案服务平台", "sourceLink": "https://ppio.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "阿里云百炼", "imgUrl": "adb444cbde1b6d6d.png", "desc": "一站式大模型开发与应用构建平台", "sourceLink": "https://bailian.console.aliyun.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "模力方舟", "imgUrl": "e77344c013fcb255.png", "desc": "AI应用共创平台，提供开发到部署一站式服务", "sourceLink": "https://ai.gitee.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "百宝箱Tbox", "imgUrl": "0ec9a9b81dd93feb.jpg", "desc": "蚂蚁集团推出的一站式 AI 原生应用开发平台", "sourceLink": "https://tbox.alipay.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "BigModel", "imgUrl": "2e5da72d2fa03b8a.png", "desc": "智谱推出的大模型开发平台", "sourceLink": "https://bigmodel.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "无问芯穹", "imgUrl": "e404d1bc1f3d33a3.jpg", "desc": "AI大模型服务平台，提供从算力、模型到应用一站式服务", "sourceLink": "https://cloud.infini-ai.com/platform/ai?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "智谱清流", "imgUrl": "7f70b1de8bf95962.png", "desc": "智谱推出的企业级AI智能体开发平台", "sourceLink": "https://bigmodel.cn/agent?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "文心智能体平台", "imgUrl": "94f4c5f628e12808.png", "desc": "百度推出的智能体构建平台", "sourceLink": "https://agents.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "SkyAgents", "imgUrl": "551872324234c4ed.png", "desc": "昆仑万维推出的 AI Agent 开发平台", "sourceLink": "https://model-platform-skyagents.tiangong.cn/home/<USER>"}, {"category": "AI开发平台", "title": "言犀智能体平台", "imgUrl": "7b192142538411fe.png", "desc": "京东推出的一站式AI智能体开发平台", "sourceLink": "https://yanxi.jd.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "魔乐社区", "imgUrl": "5d245692d05ca59c.png", "desc": "天翼云联合华为推出的AI开发者社区", "sourceLink": "https://modelers.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "BetterYeah AI", "imgUrl": "a541981b5940ccf9.png", "desc": "企业AI应用和助手构建平台", "sourceLink": "https://www.betteryeah.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "飞桨PaddlePaddle", "imgUrl": "9cfa018c183f6f47.png", "desc": "开源深度学习平台", "sourceLink": "https://www.paddlepaddle.org.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "昇思MindSpore", "imgUrl": "f890f1b44e3725de.png", "desc": "华为开源自研AI深度学习框架", "sourceLink": "https://www.mindspore.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "PyTorch", "imgUrl": "6863ebf48d482ebf.png", "desc": "开源机器学习框架", "sourceLink": "https://pytorch.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "6970f8f67ddfc2bf.png", "desc": "AI零代码工作流平台，支持用户自定义工作流程", "sourceLink": "https://www.gumloop.com/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "TensorFlow", "imgUrl": "106900bff1d34394.png", "desc": "Google推出的机器学习和人工智能开源库", "sourceLink": "https://www.tensorflow.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Apache MXNet", "imgUrl": "d4fce77b248c1039.png", "desc": "免费开源的深度学习框架", "sourceLink": "https://mxnet.apache.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Scikit-learn", "imgUrl": "6f77018330d75bae.png", "desc": "Python机器学习库", "sourceLink": "https://scikit-learn.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "MLX", "imgUrl": "29eb2417a6f04f81.png", "desc": "苹果推出的开源机器学习框架，专为Apple Silicon芯片设计", "sourceLink": "https://ml-explore.github.io/mlx/build/html/index.html?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Label Studio", "imgUrl": "861d7f852db2f498.png", "desc": "免费开源的数据标注工具", "sourceLink": "https://labelstud.io/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Vercel AI SDK", "imgUrl": "980807273295047e.png", "desc": "Vercel开源的搭建AI聊天机器人的开发套件，支持React/Svelte/Vue等框架", "sourceLink": "https://sdk.vercel.ai/docs?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON>", "imgUrl": "8a74dc7852b67862.png", "desc": "Python版本的TensorFlow深度学习API", "sourceLink": "https://keras.io/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Caffe", "imgUrl": "e09c523bc6d10ff9.png", "desc": "UC伯克利研究推出的深度学习框架", "sourceLink": "https://caffe.berkeleyvision.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "NumPy", "imgUrl": "cf38181d08f7b8f1.png", "desc": "Python科学计算必备的包", "sourceLink": "https://numpy.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "DL4J", "imgUrl": "7ddb578b092e252a.png", "desc": "开源的使用JVM部署和训练深度学习模型的套件", "sourceLink": "https://deeplearning4j.konduit.ai/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "JAX", "imgUrl": "51183f2453455ee0.png", "desc": "Google推出的用于变换数值函数的机器学习框架", "sourceLink": "https://jax.readthedocs.io/en/latest?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "NLTK", "imgUrl": "54f53c8ccaca6736.png", "desc": "Python自然语言处理工具包", "sourceLink": "https://www.nltk.org/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "9a82143c21c5352b.png", "desc": "开发由语言模型驱动的应用程序的框架", "sourceLink": "https://docs.langchain.com/docs?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Lightning AI", "imgUrl": "13c87eb50d38e477.png", "desc": "快速训练、部署和开发人工智能产品的深度学习框架，由Pytorch Lightning团队推出", "sourceLink": "https://lightning.ai/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON>", "imgUrl": "c584037f6c4e2629.png", "desc": "将AI快速集成到你自己的应用中", "sourceLink": "https://www.tryleap.ai/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "a972010119f625fb.png", "desc": "面壁智能推出的AI智能体软件开发平台，使用自然语言即可创建软件", "sourceLink": "https://chatdev.modelbest.cn/?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "Anakin.ai", "imgUrl": "2ad3f1200a2190a7.png", "desc": "一站式无代码AI应用构建平台", "sourceLink": "https://anakin.ai/zh-cn?_channel=ai-nav-hnch"}, {"category": "AI开发平台", "title": "天壤小白", "imgUrl": "5808b1e19dcec723.png", "desc": "灵活的AI应用构建和开发平台", "sourceLink": "https://www.tianrang.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Ollama", "imgUrl": "be8d3dd75143eb3d.png", "desc": "本地运行Llama和其他大语言模型", "sourceLink": "https://ollama.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Cherry Studio", "imgUrl": "4e1958a23e61a2de.png", "desc": "多模型AI客户端，本地运行AI大模型", "sourceLink": "https://ai-bot.cn/cherry-studio?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "AnythingLLM", "imgUrl": "4902fe1e0967f5d8.png", "desc": "开源的全栈 AI 客户端，支持本地部署和API集成", "sourceLink": "https://ai-bot.cn/anythingllm?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Chatbox AI", "imgUrl": "fa98649afa8f9014.png", "desc": "开源的AI客户端助手，支持多种主流AI模型", "sourceLink": "https://ai-bot.cn/chatbox-ai?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "GPT-4o", "imgUrl": "4e46d5e690f291b1.png", "desc": "OpenAI最新发布的多模态AI大模型，可自然流畅地进行语音对话", "sourceLink": "https://ai-bot.cn/openai-gpt-4o?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "讯飞星辰MaaS", "imgUrl": "dbe15540d663ed7f.png", "desc": "科大讯飞推出的AI大模型定制训练平台", "sourceLink": "https://ai-bot.cn/xunfei-xingchen-maas?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "无阶未来", "imgUrl": "5816ac3579f6603a.png", "desc": "AI应用与弹性算网平台", "sourceLink": "https://www.aivinla.com/register?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Llama 3", "imgUrl": "ff95500ad5c39510.png", "desc": "Meta最新开源推出的新一代大模型", "sourceLink": "https://llama.meta.com/llama3/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "<PERSON>", "imgUrl": "d395190593ac122b.png", "desc": "谷歌推出的新一代轻量级开放模型", "sourceLink": "https://ai.google.dev/gemma?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "豆包大模型", "imgUrl": "ed5d81781e2a018a.png", "desc": "字节跳动推出的AI大模型家族，包括视频生成、语音视觉、通用语言模型等", "sourceLink": "https://www.volcengine.com/product/doubao?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "腾讯混元大模型", "imgUrl": "8d4cff9ab3456efd.png", "desc": "腾讯研发的大语言模型，具备强大的中文创作能力，复杂语境下的逻辑推理能力，以及可靠的任务执行能力", "sourceLink": "https://hunyuan.tencent.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "GPT-4", "imgUrl": "c3152384a3482c59.png", "desc": "OpenAI旗下最新的GPT-4模型", "sourceLink": ""}, {"category": "AI训练模型", "title": "DALL·E 3", "imgUrl": "08cb642bbb5ea0a9.png", "desc": "OpenAI旗下最新的图像生成模型", "sourceLink": ""}, {"category": "AI训练模型", "title": "文心大模型", "imgUrl": "9cfa018c183f6f47.png", "desc": "百度推出的产业级知识增强大模型", "sourceLink": "https://wenxin.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "LLaMA", "imgUrl": "ff95500ad5c39510.png", "desc": "Meta（Facebook）推出的AI大语言模型", "sourceLink": "https://github.com/facebookresearch/llama?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Auto-GPT", "imgUrl": "23bad5303adf9fa9.png", "desc": "爆火的实现GPT-4完全自主的实验性开源项目，GitHub超10万星", "sourceLink": "https://agpt.co/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Jan", "imgUrl": "6eb7f5a9d98be706.png", "desc": "本地运行大模型并进行AI对话的工具，免费开源", "sourceLink": "https://jan.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "AgentGPT", "imgUrl": "3d60b507423de1f7.png", "desc": "在浏览器中组装、配置和部署自主人工智能的开源项目", "sourceLink": "https://agentgpt.reworkd.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "魔搭社区", "imgUrl": "d9b6127b1d71f340.png", "desc": "阿里达摩院推出的AI模型社区，超过300+开源AI模型", "sourceLink": "https://www.modelscope.cn/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "悟道", "imgUrl": "4b48810bb51c6a2e.png", "desc": "智源“悟道”大模型，中国首个+世界最大人工智能大模型", "sourceLink": "https://www.baai.ac.cn/portal/article/index/cid/49/id/518.html?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "MiracleVision奇想智能", "imgUrl": "500a70a419e22a55.png", "desc": "美图推出的AI视觉大模型，支持AI图像、设计和视频创作", "sourceLink": "https://www.miraclevision.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Gradio", "imgUrl": "5dd852cfb0c03b4c.png", "desc": "开源的搭建机器学习模型UI界面的Python库", "sourceLink": "https://gradio.app/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "DeepFloyd IF", "imgUrl": "3500c0bb4efa8b9e.png", "desc": "StabilityAI旗下的DeepFloyd团队推出的图片生成模型", "sourceLink": "https://deepfloyd.ai/deepfloyd-if?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Cohere", "imgUrl": "1938ec8d28552cd2.png", "desc": "构建AI产品的大语言模型平台", "sourceLink": "https://cohere.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Codex", "imgUrl": "5ceeed6629b71447.png", "desc": "OpenAI旗下AI代码生成训练模型", "sourceLink": "https://openai.com/blog/openai-codex?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "序列猴子", "imgUrl": "bfde53962e0e572c.png", "desc": "出门问问推出的一款超大规模的语言模型", "sourceLink": "https://openapi.mobvoi.com/index?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "BLOOM", "imgUrl": "9592ea3e31291214.png", "desc": "HuggingFace推出的大型语言模型（LLM）", "sourceLink": "https://huggingface.co/docs/transformers/model_doc/bloom?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "阿里巴巴M6", "imgUrl": "578995bee07e737e.png", "desc": "阿里巴巴达摩院推出的超大规模中文预训练模型(M6)", "sourceLink": "https://m6.aliyun.com/?_channel=ai-nav-hnch#"}, {"category": "AI训练模型", "title": "<PERSON><PERSON>", "imgUrl": "2b20ac9818906440.png", "desc": "低门槛快速定制大语言模型的引擎", "sourceLink": "https://lamini.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "StableLM", "imgUrl": "03047e5ef4a5b7e0.png", "desc": "Stability AI推出的开源的类ChatGPT大语言模型", "sourceLink": "https://github.com/Stability-AI/StableLM?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Gen-2", "imgUrl": "487da429953ce5d9.png", "desc": "Runway最新推出的AI视频生成模型", "sourceLink": "https://research.runwayml.com/gen2?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "DeepSpeed", "imgUrl": "6c0b6ab12e8b6585.png", "desc": "微软开源的低成本实现类似ChatGPT的模型训练", "sourceLink": "https://www.deepspeed.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "PaLM 2", "imgUrl": "47a288847d8dd89c.png", "desc": "Google的下一代大语言模型，超过3400亿参数", "sourceLink": "https://ai.google/discover/palm2?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "OpenBMB", "imgUrl": "3961547fe607a3e7.png", "desc": "清华团队支持发起的大规模预训练语言模型库与相关工具", "sourceLink": "https://www.openbmb.org/home?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Segment Anything（SAM）", "imgUrl": "ff95500ad5c39510.png", "desc": "Meta最新推出的AI图像分割模型", "sourceLink": "https://segment-anything.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "HuggingFace", "imgUrl": "29cdb0a5d8cc3469.png", "desc": "AI模型开发社区", "sourceLink": "https://huggingface.co/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Imagen", "imgUrl": "779f1f4e81b3e1ea.png", "desc": "Google AI文字到图像生成模型", "sourceLink": "https://imagen.research.google/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "StableVicuna", "imgUrl": "6ff6a1dbf96e4d34.png", "desc": "第一个通过RLHF训练的大规模开源聊天机器人", "sourceLink": "https://chat.lmsys.org/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Watsonx.ai", "imgUrl": "ed5698f8dd5b61ee.png", "desc": "IBM推出的企业级生成式人工智能和机器学习平台", "sourceLink": "https://www.ibm.com/products/watsonx-ai?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Lobe", "imgUrl": "26f6989b322f38c7.png", "desc": "简单免费的机器学习模型训练工具", "sourceLink": "https://www.lobe.ai/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Scale AI", "imgUrl": "e54a8d585b6faff7.png", "desc": "AI机器学习标注训练平台", "sourceLink": "https://scale.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Replicate", "imgUrl": "e089b0752948f64b.png", "desc": "在线运行开源机器学习模型", "sourceLink": "https://replicate.com/?_channel=ai-nav-hnch"}, {"category": "AI训练模型", "title": "Evidently AI", "imgUrl": "ddec6928b543f341.png", "desc": "开源的机器学习模型监测和测试工具", "sourceLink": "https://www.evidentlyai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "文心一格", "imgUrl": "9cfa018c183f6f47.png", "desc": "AI艺术和创意辅助平台", "sourceLink": "https://yige.baidu.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "美图AI文生图", "imgUrl": "eaa18af88932b006.png", "desc": "美图推出的AI文本生成图片的工具", "sourceLink": "https://design.meitu.com/aigc/text-to-image?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "千鹿AI", "imgUrl": "029b55cc757f91c2.png", "desc": "AI设计助手，每日可免费生成300张图像", "sourceLink": "https://qianlu.cc/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "包图AI文生图", "imgUrl": "0e8d6233fd66de41.png", "desc": "一站式图像、插画生成工具", "sourceLink": "https://ibaotu.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "星流AI", "imgUrl": "e4fa6bce0d1fa7a1.png", "desc": "LiblibAI推出的一站式AI图像生成平台", "sourceLink": "https://ai-bot.cn/xingliu-art?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "悠船", "imgUrl": "ebf08a7a160320dc.png", "desc": "Midjourney官方推出的中文版AI图像生成工具", "sourceLink": "https://www.youchuan.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "神采", "imgUrl": "4f481f4ec06c0b1e.png", "desc": "让创意照进现实， AI生成创意插画", "sourceLink": "https://www.ishencai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "天工巧绘SkyPaint", "imgUrl": "e5a5bd4ec24800ff.png", "desc": "免费的AI插画绘制工具，由昆仑万维与奇点智源合作推出", "sourceLink": "https://sky-paint.singularity-ai.com/index.html?_channel=ai-nav-hnch#"}, {"category": "AI图片插画生成", "title": "FlagStudio", "imgUrl": "d5f79c651d2d4e39.png", "desc": "智源研究院推出的AI文本图像绘画生成工具", "sourceLink": "https://flagstudio.baai.ac.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "NightCafe", "imgUrl": "47aa7b572b0409f5.png", "desc": "AI艺术插画在线生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "niji・journey", "imgUrl": "823ba252de9ba66c.png", "desc": "魔法般的二次元绘画生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Deep Dream Generator", "imgUrl": "15c4f67773a0ad15.png", "desc": "AI创建生成梦幻般的插画图片，刻画你的梦中场景", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "无限画", "imgUrl": "70f3ea2b1a8b9360.png", "desc": "千库网推出的AI图片插画生成工具", "sourceLink": "https://588ku.com/ai/wuxianhua/Home?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "BlueWillow", "imgUrl": "97ce4f5dd3cd1282.png", "desc": "免费的AI图像艺术画生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Waifu Labs", "imgUrl": "efbfb61013abccaa.png", "desc": "免费在线AI生成二次元动漫头像", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "dreamlike.art", "imgUrl": "98dd6391212f9f2f.png", "desc": "免费在线插画生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Artbreeder", "imgUrl": "5f0644e3bf7cc98d.png", "desc": "创建令人惊叹的插画和艺术", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Tiamat", "imgUrl": "0eaeac58b2c025b1.png", "desc": "国内团队推出的AI艺术画生成工具", "sourceLink": "https://www.tiamat.world/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Vega AI", "imgUrl": "42bdc65632cbccfa.png", "desc": "在线免费AI插画创作平台，支持文生图，图生图，条件生图等多种绘画模式", "sourceLink": "https://vegaai.net/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Wepik AI", "imgUrl": "ae5ef38319799094.png", "desc": "Freepik推出的AI文本到图像的在线生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "8c32bb83d3ecfec9.png", "desc": "免费在线文本到图像生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "万兴爱画", "imgUrl": "feb6a1e26e722f6f.png", "desc": "万兴科技推出的AI生成高品质艺术画工具", "sourceLink": "https://aigc.wondershare.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Photosonic", "imgUrl": "df53a10ea4f04f25.png", "desc": "Writesonic推出的AI艺术插画生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "9c8b44d974b53af7.png", "desc": "可定制的人工智能图像生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "getimg.ai", "imgUrl": "b7a0570c32938eea.png", "desc": "在线AI图像和插画创作工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "DreamUp", "imgUrl": "4625fe872208974a.png", "desc": "DeviantArt推出的AI插画生成工具", "sourceLink": "https://www.dreamup.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Scribble Diffusion", "imgUrl": "b28700d96b88af21.png", "desc": "将草图转变为精美的插画", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Lexica", "imgUrl": "8704e413b6e4685e.png", "desc": "基于Stable Diffusion的在线插画生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Generated Photos", "imgUrl": "32259645fd1eb8a6.png", "desc": "AI人脸头像生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Picsart AI", "imgUrl": "5329ff6bfa6dc717.png", "desc": "Picsart推出的AI图片生成器", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Imagine by Magic Studio", "imgUrl": "a489a0195b0fd3c9.png", "desc": "AI文字到图片生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "neural.love", "imgUrl": "a0e1a6f4454ff9ae.png", "desc": "AI艺术图片生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "<PERSON><PERSON><PERSON>", "imgUrl": "8ccb7c44e870126c.png", "desc": "AI艺术图片生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Artssy", "imgUrl": "560c66ee31298a8b.png", "desc": "AI图像生成", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "ShutterStock AI图片生成", "imgUrl": "4e14239e0e24b2cf.png", "desc": "Shutterstock推出的AI图片生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Playform", "imgUrl": "946bef9b6a55b202.png", "desc": "专业的高质量AI艺术画生成平台", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Photo Booth by Magic Studio", "imgUrl": "473515868bce84fb.png", "desc": "AI创建个人资料图片", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Supermeme", "imgUrl": "4e8d3107ee5c6e13.png", "desc": "AI MEME梗图生成器", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Fotor", "imgUrl": "090181c1124c0b75.png", "desc": "Fotor推出的在线AI图片生成工具", "sourceLink": "https://www.fotor.com/features/ai-image-generator?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Dream.ai", "imgUrl": "49ca4e9bf6bada2b.png", "desc": "WOMBO推出的AI艺术画生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "无界AI", "imgUrl": "2960d905e204db30.png", "desc": "AI生成艺术插画和二次元人物", "sourceLink": "https://www.wujieai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "站酷梦笔", "imgUrl": "a7e805c387a26cbe.png", "desc": "国内知名设计社区站酷推出的人工智能插画生成工具", "sourceLink": "https://www.zcool.com.cn/ailab?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "笔魂AI绘画", "imgUrl": "7699c7f1319452b6.png", "desc": "AI在线生图工具", "sourceLink": "https://ibihun.com/?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "改图鸭AI图片生成", "imgUrl": "694f319ab642428b.png", "desc": "改图鸭AI图片生成", "sourceLink": "https://www.gaituya.com/aiimg?_channel=ai-nav-hnch"}, {"category": "AI图片插画生成", "title": "Prodia", "imgUrl": "b38436709c6f7a9b.png", "desc": "AI艺术画生成工具", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "Lucid<PERSON>", "imgUrl": "9d0d51d9bdb15347.png", "desc": "AI生成高质量人像照片", "sourceLink": ""}, {"category": "AI图片插画生成", "title": "AI Photos", "imgUrl": "e86d7807de95718f.png", "desc": "AI图片艺术美化", "sourceLink": ""}, {"category": "AI图片背景移除", "title": "吐司AI抠图", "imgUrl": "0dd50ffb9e259b62.png", "desc": "吐司AI推出的智能抠图工具", "sourceLink": "https://tusiart.com/template/807760005523577813?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "稿定AI抠图", "imgUrl": "64711e0ee714e34c.png", "desc": "AI自动去水印、消除背景工具", "sourceLink": "https://www.gaoding.com/utms/dfdc2191877f434880bf01c62e86ea6c?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "美图抠图", "imgUrl": "5edbd0467fc470d1.png", "desc": "美图秀秀推出的AI智能抠图工具，一键移除背景", "sourceLink": "https://cutout.designkit.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Pic Copilot AI抠图", "imgUrl": "3880324fe1846432.png", "desc": "Pic Copilot推出的AI抠图工具，支持批量抠图", "sourceLink": "https://www.piccopilot.com/create/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "鲜艺AI抠图", "imgUrl": "48c4f2014224861e.png", "desc": "免费AI抠图工具，支持离线安装使用", "sourceLink": "https://kt.94xy.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Pixian.AI", "imgUrl": "9cbc854ec2df5575.png", "desc": "免费的AI图片背景抠除工具", "sourceLink": "https://pixian.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "顽兔抠图", "imgUrl": "d21ac8b8f3a6eb57.png", "desc": "阿里推出的一键去除商品图背景工具", "sourceLink": "https://d.design/toolbox/cutout?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Icons8 Background Remover", "imgUrl": "f769b3b4e0e68288.png", "desc": "Icons8出品的免费图片背景移除工具", "sourceLink": "https://icons8.com/bgremover?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "美间AI抠图", "imgUrl": "b048e08a3b693fe6.png", "desc": "美间AI推出的免费智能抠图工具", "sourceLink": "https://www.meijian.com/mj-box/ai-pic-matting-intro?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "BgSub", "imgUrl": "d57f770444071d45.png", "desc": "免费的保护隐私的AI图片背景去除工具", "sourceLink": "https://bgsub.cn/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "ClipDrop Remove Background", "imgUrl": "d21b4a5cc884e77d.png", "desc": "ClipDrop出品的AI图片背景移除工具", "sourceLink": "https://clipdrop.co/remove-background?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Erase.bg", "imgUrl": "1d9f8542659e5afb.png", "desc": "在线抠图和去除图片背景", "sourceLink": "https://www.erase.bg/zh?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "千图设计室AI助手", "imgUrl": "214cfdcd80bf487e.png", "desc": "AI绘画抠图工具集", "sourceLink": "https://hisheai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Adobe Image Background Remover", "imgUrl": "fbfedd1a078ec40d.png", "desc": "Adobe Express的图片背景移除工具", "sourceLink": "https://www.adobe.com/express/feature/image/remove-background?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Removal.AI", "imgUrl": "8c33fb5d243acb75.png", "desc": "AI图片背景移除工具", "sourceLink": "https://removal.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Background Eraser", "imgUrl": "f364c836df17889d.png", "desc": "AI自动删除图片背景", "sourceLink": "https://magicstudio.com/zh/backgrounderaser?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "<PERSON><PERSON>zz<PERSON>", "imgUrl": "9f3161417f106779.png", "desc": "免费在线抠除图片背景", "sourceLink": "https://www.slazzer.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Cutout.Pro抠图", "imgUrl": "5075e42714be9511.png", "desc": "AI批量抠图去背景", "sourceLink": "https://www.cutout.pro/zh-cn/remove-background?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "BGremover", "imgUrl": "5f365eec1aed30f1.png", "desc": "Vance AI推出的图片背景移除工具", "sourceLink": "https://bgremover.vanceai.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Quicktools Background Remover", "imgUrl": "2f86fe0e41375778.png", "desc": "Picsart旗下的Quicktools推出的图片背景移除工具", "sourceLink": "https://tools.picsart.com/image/background-remover?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Zyro AI Background Remover", "imgUrl": "f3e938d93c48dcf1.png", "desc": "Zyro推出的AI图片背景移除工具", "sourceLink": "https://zyro.com/tools/image-background-remover?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "PhotoScissors", "imgUrl": "693b37f8750d35ae.png", "desc": "免费自动图片背景去除", "sourceLink": "https://photoscissors.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "一键抠图", "imgUrl": "7d9cc78471a5faf5.png", "desc": "在线一键抠图换背景", "sourceLink": "https://www.yijiankoutu.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "ClippingMagic", "imgUrl": "7d649e07f43c85fb.png", "desc": "魔术般地去除图片背景", "sourceLink": "https://clippingmagic.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "图可丽", "imgUrl": "38cb549145adf0ff.png", "desc": "AI图片和视频抠图，一键抠图神器", "sourceLink": "https://www.tukeli.net/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Hotpot AI Background Remover", "imgUrl": "96a68d04b9496a71.png", "desc": "Hotpot.ai推出的图片背景移除工具", "sourceLink": "https://hotpot.ai/remove-background?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Stylized", "imgUrl": "eb4be8a3d395ff55.png", "desc": "AI产品图背景替换", "sourceLink": "https://www.stylized.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Booth.ai", "imgUrl": "1ad4c4a159c9ada8.png", "desc": "高质量AI产品展示效果图生成", "sourceLink": "https://www.booth.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "Pixelcut.ai", "imgUrl": "8dd1842b565e9206.png", "desc": "AI产品背景移除和替换", "sourceLink": "https://www.pixelcut.ai/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "<PERSON><PERSON><PERSON><PERSON>", "imgUrl": "ace363fa73efde63.png", "desc": "AI图片编辑和背景移除", "sourceLink": "https://picwish.com/?_channel=ai-nav-hnch"}, {"category": "AI图片背景移除", "title": "PhotoRoom", "imgUrl": "d7ac9c8a7f5abb58.png", "desc": "免费的AI图片背景移除和添加", "sourceLink": "https://www.photoroom.com/background-remover?_channel=ai-nav-hnch"}]