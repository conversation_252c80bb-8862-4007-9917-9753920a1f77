import { readFile, writeFile, mkdir, access } from "node:fs/promises";
import { createHash } from "node:crypto";
import { join, extname } from "node:path";
import { get as httpsGet } from "node:https";
import { get as httpGet } from "node:http";

interface DataItem {
  category: string;
  title: string | null;
  imgUrl: string | null;
  desc: string | null;
}

interface DownloadResult {
  success: boolean;
  fileName?: string;
  error?: string;
  skipped?: boolean;
  reason?: string;
}

interface DownloadSummary {
  results: Array<{
    item: DataItem;
    result: DownloadResult;
  }>;
  statistics: {
    downloadCount: number;
    skipCount: number;
    errorCount: number;
    staticDir: string;
  };
}

/**
 * 下载图片并返回Buffer
 * @param url - 图片URL
 * @returns Promise<Buffer> - 图片数据
 */
async function downloadImage(url: string): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith("https:");
    const get = isHttps ? httpsGet : httpGet;

    get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(
          new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`)
        );
        return;
      }

      const chunks: Buffer[] = [];
      response.on("data", (chunk) => chunks.push(chunk));
      response.on("end", () => resolve(Buffer.concat(chunks)));
      response.on("error", reject);
    }).on("error", reject);
  });
}

/**
 * 计算文件的SHA-256哈希值
 * @param buffer - 文件数据
 * @returns string - 16位短哈希
 */
function calculateFileHash(buffer: Buffer): string {
  const hash = createHash("sha256");
  hash.update(buffer);
  return hash.digest("hex").substring(0, 16);
}

/**
 * 获取图片文件扩展名
 * @param url - 图片URL
 * @param buffer - 图片数据（用于检测格式）
 * @returns string - 文件扩展名
 */
function getImageExtension(url: string, buffer: Buffer): string {
  // 首先尝试从URL获取扩展名
  const urlExt = extname(new URL(url).pathname).toLowerCase();
  if (
    urlExt &&
    [".jpg", ".jpeg", ".png", ".gif", ".webp", ".svg"].includes(urlExt)
  ) {
    return urlExt;
  }

  // 通过文件头检测格式
  const header = buffer.subarray(0, 12);

  if (header[0] === 0xff && header[1] === 0xd8) return ".jpg";
  if (
    header[0] === 0x89 &&
    header[1] === 0x50 &&
    header[2] === 0x4e &&
    header[3] === 0x47
  )
    return ".png";
  if (header[0] === 0x47 && header[1] === 0x49 && header[2] === 0x46)
    return ".gif";
  if (header.includes(Buffer.from("WEBP"))) return ".webp";
  if (header.includes(Buffer.from("<svg"))) return ".svg";

  // 默认返回.png
  return ".png";
}

/**
 * 检查文件是否存在
 * @param filePath - 文件路径
 * @returns Promise<boolean> - 文件是否存在
 */
async function fileExists(filePath: string): Promise<boolean> {
  try {
    await access(filePath);
    return true;
  } catch {
    return false;
  }
}

export const downloadImgAll = async (data: DataItem[]): Promise<DataItem[]> => {
  try {
    // // 读取数据文件
    // const data: DataItem[] = JSON.parse(await readFile("data.json", "utf-8"));

    // 确保static目录存在
    const staticDir = join(process.cwd(), "static");
    try {
      await mkdir(staticDir, { recursive: true });
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    console.log(`开始下载 ${data.length} 个项目的图片...`);

    let downloadCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    // 创建结果数组，复制原始数据
    const result: DataItem[] = data.map((item) => ({ ...item }));

    // 并发下载图片（限制并发数为5）
    const concurrency = 5;
    for (let i = 0; i < data.length; i += concurrency) {
      const batch = data.slice(i, i + concurrency);

      await Promise.allSettled(
        batch.map(async (item, index) => {
          const globalIndex = i + index + 1;
          const resultIndex = i + index;

          if (!item.imgUrl) {
            console.log(
              `[${globalIndex}/${data.length}] 跳过 "${item.title}": 无图片URL`
            );
            skipCount++;
            // 保持原始的 imgUrl 值（null）
            return;
          }

          try {
            console.log(
              `[${globalIndex}/${data.length}] 开始下载: ${item.title}`
            );

            // 下载图片
            const imageBuffer = await downloadImage(item.imgUrl);

            // 计算哈希值
            const hash = calculateFileHash(imageBuffer);

            // 获取文件扩展名
            const extension = getImageExtension(item.imgUrl, imageBuffer);

            // 生成文件名
            const fileName = `${hash}${extension}`;
            const filePath = join(staticDir, fileName);

            // 检查文件是否已存在
            if (await fileExists(filePath)) {
              console.log(
                `[${globalIndex}/${data.length}] 跳过 "${item.title}": 文件已存在 (${fileName})`
              );
              skipCount++;
              // 更新结果数组中的 imgUrl 为文件名
              result[resultIndex].imgUrl = fileName;
              return;
            }

            // 保存文件
            await writeFile(filePath, imageBuffer);

            console.log(
              `[${globalIndex}/${data.length}] ✅ 下载完成: "${item.title}" -> ${fileName}`
            );
            downloadCount++;

            // 更新结果数组中的 imgUrl 为文件名
            result[resultIndex].imgUrl = fileName;
          } catch (error) {
            console.error(
              `[${globalIndex}/${data.length}] ❌ 下载失败: "${item.title}" - ${error}`
            );
            errorCount++;
            // 下载失败时，将 imgUrl 设置为 null
            result[resultIndex].imgUrl = null;
          }
        })
      );
    }

    console.log("\n下载统计:");
    console.log(`✅ 成功下载: ${downloadCount} 个文件`);
    console.log(`⏭️  跳过: ${skipCount} 个文件`);
    console.log(`❌ 失败: ${errorCount} 个文件`);
    console.log(`📁 文件保存位置: ${staticDir}`);

    // 返回修改后的数据，其中 imgUrl 已替换为文件名
    return result;
  } catch (error) {
    console.error("程序执行失败:", error);
    throw error; // 抛出错误而不是退出进程，让调用者处理
  }
};

/**
 * 前端计算文件hash（使用js-sha256）
 * @param file - 文件对象
 * @returns Promise<string> - 16位短hash
 */
export async function calculateClientFileHash(file: File): Promise<string> {
  // 动态导入js-sha256以避免服务器端错误
  const { sha256 } = await import("js-sha256");

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const hash = sha256.create();
        hash.update(new Uint8Array(arrayBuffer));
        // 取前16个字符作为短哈希（64位）
        const shortHash = hash.hex().substring(0, 16);
        resolve(shortHash);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
}
