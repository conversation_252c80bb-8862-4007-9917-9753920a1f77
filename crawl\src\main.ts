import { chromium, Page } from "@playwright/test";
import { writeFile } from "node:fs/promises";
import { downloadImgAll } from "./download";

const main = async () => {
  // 从网站获取实际数据
  console.log("\n=== 从网站获取实际数据 ===");
  const browser = await chromium.launch({
    headless: false,
  });
  const page = await browser.newPage();

  await page.goto("https://ai-bot.cn/");
  await page.waitForLoadState("load");
  await page.waitForLoadState("domcontentloaded");
  await page.waitForSelector("div.content-layout");
  const boxEl = await page.locator("div.content-layout");
  const data: {
    category: string;
    title: string | null;
    imgUrl: string | null;
    desc: string | null;
    sourceLink: string;
  }[] = [];
  // 使用CSS相邻兄弟选择器获取紧挨着的元素组
  const adjacentGroups = await boxEl
    .locator("div.d-flex.flex-fill.align-items-center.mb-4 + div.row.io-mx-n2")
    .all();

  console.log(`找到 ${adjacentGroups.length} 组紧挨着的元素`);

  // 处理每组紧挨着的元素
  for (let i = 0; i < adjacentGroups.length; i++) {
    const contentDiv = adjacentGroups[i];
    const headerDiv = contentDiv.locator("xpath=preceding-sibling::div[1]");

    const headerText = await headerDiv.textContent();
    const contentText = await contentDiv.textContent();
    console.log(`\n第 ${i + 1} 组:`);
    console.log(`Header: ${headerText?.trim()}`);
    console.log(`Content: ${contentText?.trim().substring(0, 20)}...`);
    if (headerText?.includes("所有该分类工具 >>")) {
      continue;
    }
    const categoryText = await headerDiv
      .locator("h4.text-gray.text-lg.m-0")
      .textContent()
      .then((r) => r?.trim());

    if (!categoryText) {
      continue;
    }
    console.log(`Category: ${categoryText?.trim()}`);

    const items = await contentDiv
      .locator("div.url-body.default ")
      .all()
      .then((res) =>
        Promise.all(
          res.map(async (it) => {
            const [imgUrl, title, desc, sourceLink] = await Promise.all([
              it
                .locator(
                  "div.url-img.rounded-circle.mr-2.d-flex.align-items-center.justify-content-center > img"
                )
                .getAttribute("data-src"),
              it.locator("div.url-info.flex-fill strong").textContent(),
              it.locator("div.url-info.flex-fill p").textContent(),
              it.locator("a").getAttribute("data-url"),
            ]);

            return {
              title,
              imgUrl,
              desc,
              sourceLink,
            };
          })
        )
      );
    items.forEach((item) => {
      if (!data.find((d) => d.title === item.title && item.desc === d.desc)) {
        data.push({
          category: categoryText,
          title: item.title,
          imgUrl: item.imgUrl,
          desc: item.desc,
          sourceLink: item.sourceLink!,
        });
      }
    });
  }

  await page.locator("a", { hasText: "所有该分类工具 >>" }).first().waitFor();
  const menusLinkArr = await page
    .locator("a", { hasText: "所有该分类工具 >>" })
    .all()
    .then(async (res) => [
      ...new Set(await Promise.all(res.map((it) => it.getAttribute("href")))),
    ]);

  console.log("============================");
  // 使用CSS相邻兄弟选择器获取紧挨着的元素组
  const adjacentTabsGroups = await boxEl
    .locator(
      "h4.text-gray.text-lg + div.d-flex.flex-fill.flex-tab.align-items-center"
    )
    .all();

  console.log(`找到 ${adjacentTabsGroups.length} 组紧挨着的元素`);

  // 处理每组紧挨着的元素
  for (let i = 0; i < adjacentTabsGroups.length; i++) {
    const tabsDiv = adjacentTabsGroups[i];
    const headerDiv = tabsDiv.locator("xpath=preceding-sibling::h4[1]");
    const contentDiv = tabsDiv.locator("xpath=following-sibling::div[1]");

    const headerText = await headerDiv.textContent();
    const tabText = await tabsDiv.textContent();
    const contentText = await contentDiv.textContent();

    const categoryText = await headerDiv.textContent().then((r) => r?.trim());

    if (!categoryText) {
      continue;
    }

    const tabMenu = await tabsDiv.locator(
      "ul.nav.nav-pills.tab-auto-scrollbar.menu.overflow-x-auto"
    );

    console.log(tabMenu);

    const tabMenuItems = await tabMenu.locator("a").all();

    console.log(categoryText);

    for (const tabMenuItem of tabMenuItems) {
      const link = (await tabMenuItem.getAttribute("data-link"))?.trim();
      console.log(link);
      if (link) {
        if (!menusLinkArr.includes(link)) {
          menusLinkArr.push(link);
        }
        continue;
      }
      await tabMenuItem.click();
      const tabMenuItemText = await tabMenuItem.textContent();
      if (!tabMenuItemText) {
        continue;
      }
      await new Promise((resolve) => setTimeout(resolve, 2500));
      const contentDiv = tabsDiv.locator("xpath=following-sibling::div[1]");
      const items = await contentDiv
        .locator("div.url-body.default ")
        .all()
        .then((res) =>
          Promise.all(
            res.map(async (it) => {
              const [imgUrl, title, desc, sourceLink] = await Promise.all([
                it
                  .locator(
                    "div.url-img.rounded-circle.mr-2.d-flex.align-items-center.justify-content-center > img"
                  )
                  .getAttribute("data-src"),
                it.locator("div.url-info.flex-fill strong").textContent(),
                it.locator("div.url-info.flex-fill p").textContent(),
                it.locator("a").getAttribute("data-url"),
              ]);

              return {
                title,
                imgUrl,
                desc,
                sourceLink,
              };
            })
          )
        );

      items.forEach((item) => {
        if (!data.find((d) => d.title === item.title && item.desc === d.desc)) {
          data.push({
            category: tabMenuItemText,
            title: item.title,
            imgUrl: item.imgUrl,
            desc: item.desc,
            sourceLink: item.sourceLink!,
          });
        }
      });
    }

    console.log(`\n第 ${i + 1} 组:`);
    console.log(`Header: ${headerText?.trim()}`);
    console.log(`Tab: ${tabText?.trim().substring(0, 20)}...`);
    console.log(`Content: ${contentText?.trim().substring(0, 20)}...`);
  }

  console.log(menusLinkArr);

  // const menus = parseMenuElement(menuElement);
  // console.log("网站实际菜单结构:");
  // console.log(formatMenusToJson(menus));

  // // 显示网站菜单统计信息
  // const webStats = getMenuStats(menus);
  // console.log("\n网站菜单统计信息:");
  // console.log(`- 总菜单项: ${webStats.totalItems}`);
  // console.log(`- 有子菜单的项: ${webStats.itemsWithChildren}`);
  // console.log(`- 无子菜单的项: ${webStats.itemsWithoutChildren}`);
  // console.log(`- 子菜单总数: ${webStats.totalChildren}`);

  for (const link of menusLinkArr) {
    if (!link?.includes("https:")) {
      continue;
    }

    for (let index = 0; index < 100; index++) {
      const pageLink = link + `page/${index + 1}/`;
      const it = await getEveryLinkData(page, pageLink);
      if (it && Array.isArray(it)) {
        it.forEach((item) => {
          if (
            !data.find((d) => d.title === item.title && item.desc === d.desc)
          ) {
            data.push(item);
          }
        });

        console.log(pageLink, "数据获取完毕");
      } else {
        console.log(pageLink, "页面无数据，跳过");
        // 如果连续几页都没有数据，可能已经到了最后一页，可以考虑提前退出
        break;
      }
    }
  }

  await browser.close();

  data.forEach((it) => {
    if (!it.desc) {
      it.desc = it.title;
    }
    if (it.sourceLink) {
      const sourceLink = it.sourceLink.trim();
      const url = new URL(sourceLink);
      url.search = "";
      url.searchParams.append("_channel", "ai-nav-hnch");
      const newSourceLink = url.toString();
      it.sourceLink = newSourceLink;
    }
  });

  console.log("\n=== 网站实际数据 ===");
  console.log(data.length);

  const result = await downloadImgAll(data);

  await writeFile("./data.json", JSON.stringify(result, null, 2));
};

const getEveryLinkData = async (page: Page, link: string) => {
  try {
    await page.goto(link, { timeout: 30000 });
    await page.waitForLoadState("load");
    await page.waitForLoadState("domcontentloaded");

    await new Promise((resolve) => setTimeout(resolve, 1000));
    // div.content-layout 判断这个元素是否存在，不存在就return
    const contentLayout = await page.locator("div.content-layout").count();
    if (contentLayout === 0) {
      console.log(`页面 ${link} 不存在 content-layout 元素，跳过`);
      return null;
    }
  } catch (error) {
    console.log(`访问页面 ${link} 时出错:`, error);
    return null;
  }
  try {
    const boxEl = page.locator("div.content-layout").first();

    const category = await boxEl.locator("h4").first().innerText();
    const items = await boxEl
      .locator("div.url-body.default ")
      .all()
      .then((res) =>
        Promise.all(
          res.map(async (it) => {
            const [imgUrl, title, desc, sourceLink] = await Promise.all([
              it
                .locator(
                  "div.url-img.rounded-circle.mr-2.d-flex.align-items-center.justify-content-center > img"
                )
                .getAttribute("data-src"),
              it.locator("div.url-info.flex-fill strong").textContent(),
              it.locator("div.url-info.flex-fill p").textContent(),
              it.locator("a").getAttribute("data-url"),
            ]);

            return {
              title,
              imgUrl,
              desc,
              sourceLink,
            };
          })
        )
      );

    const data: {
      category: string;
      title: string | null;
      imgUrl: string | null;
      desc: string | null;
      sourceLink: string;
    }[] = items.map((item) => ({
      category,
      title: item.title,
      imgUrl: item.imgUrl,
      desc: item.desc,
      sourceLink: item.sourceLink!,
    }));

    return data;
  } catch (error) {
    console.log(`提取页面 ${link} 数据时出错:`, error);
    return null;
  }
};

main().catch(console.error);
