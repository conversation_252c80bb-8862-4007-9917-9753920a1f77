import { JSDOM } from "jsdom";

export interface MenuItem {
  title: string;
  children?: MenuItem[];
}

/**
 * 解析 HTML 菜单元素并转换为指定的 JSON 格式
 * @param htmlString HTML 字符串
 * @returns 菜单数组 [{title: string, children?: MenuItem[]}]
 */
export const parseMenuElement = (htmlString: string): MenuItem[] => {
  const dom = new JSDOM(htmlString);
  const document = dom.window.document;
  
  const menus: MenuItem[] = [];
  
  // 获取所有顶级菜单项
  const topLevelItems = document.querySelectorAll(
    ".sidebar-menu-inner > ul > li.sidebar-item"
  );
  
  topLevelItems.forEach((item: Element) => {
    const titleElement = item.querySelector("a span");
    if (!titleElement) return;

    const title = titleElement.textContent?.trim() || "";
    const menuItem: MenuItem = { title };

    // 检查是否有子菜单
    const subMenu = item.querySelector("ul");
    if (subMenu) {
      const children: MenuItem[] = [];
      const subItems = subMenu.querySelectorAll("li");

      subItems.forEach((subItem: Element) => {
        const subTitleElement = subItem.querySelector("a span");
        if (subTitleElement) {
          const subTitle = subTitleElement.textContent?.trim() || "";
          children.push({ title: subTitle });
        }
      });

      if (children.length > 0) {
        menuItem.children = children;
      }
    }

    menus.push(menuItem);
  });

  return menus;
};

/**
 * 将菜单数组转换为格式化的 JSON 字符串
 * @param menus 菜单数组
 * @param indent 缩进空格数，默认为 2
 * @returns 格式化的 JSON 字符串
 */
export const formatMenusToJson = (menus: MenuItem[], indent: number = 2): string => {
  return JSON.stringify(menus, null, indent);
};

/**
 * 统计菜单项数量
 * @param menus 菜单数组
 * @returns 统计信息
 */
export const getMenuStats = (menus: MenuItem[]) => {
  let totalItems = 0;
  let itemsWithChildren = 0;
  let totalChildren = 0;

  menus.forEach(menu => {
    totalItems++;
    if (menu.children && menu.children.length > 0) {
      itemsWithChildren++;
      totalChildren += menu.children.length;
    }
  });

  return {
    totalItems,
    itemsWithChildren,
    totalChildren,
    itemsWithoutChildren: totalItems - itemsWithChildren
  };
};
