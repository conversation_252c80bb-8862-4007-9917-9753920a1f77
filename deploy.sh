#!/bin/bash

# AI Nav 部署脚本
# 用于部署静态文件修复

set -e

echo "🚀 开始部署 AI Nav 静态文件访问修复..."

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down

# 重新构建并启动服务
echo "🔨 重新构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 检查健康状态
echo "🏥 检查应用健康状态..."
for i in {1..10}; do
    if curl -f http://localhost:3000/api/health >/dev/null 2>&1; then
        echo "✅ 应用健康检查通过"
        break
    else
        echo "⏳ 等待应用启动... ($i/10)"
        sleep 10
    fi

    if [ $i -eq 10 ]; then
        echo "❌ 应用健康检查失败"
        echo "📋 查看日志:"
        docker-compose logs web
        exit 1
    fi
done

# 检查静态文件访问
echo "📁 检查静态文件访问..."
echo "🔍 调试信息:"
curl -s http://localhost:3000/api/debug/uploads | jq '.' || echo "无法获取调试信息"

echo "🖼️ 测试图片访问:"
if curl -f -I http://localhost:3000/uploads/de2e7d5a6f9c6472.png >/dev/null 2>&1; then
    echo "✅ 静态文件访问正常"
else
    echo "⚠️ 静态文件访问可能有问题，但应用已启动"
fi

# 测试上传目录调试
echo "🧪 测试上传目录状态..."
if curl -f http://localhost:3000/api/debug/uploads >/dev/null 2>&1; then
    echo "✅ 上传目录调试 API 正常"
    echo "📋 查看上传目录详情: curl http://localhost:3000/api/debug/uploads"
else
    echo "❌ 上传目录调试 API 失败"
fi

# 测试静态文件访问
echo "🧪 测试静态文件访问..."
if curl -f http://localhost:3000/uploads/test.txt >/dev/null 2>&1; then
    echo "✅ 静态文件访问正常"
else
    echo "⚠️  静态文件测试失败（可能是因为测试文件不存在，这是正常的）"
fi

echo "🎉 AI Nav 静态文件访问修复部署完成！"
echo ""
echo "📋 本次修复内容："
echo "  ✅ 添加了静态文件 API 路由 (/uploads/[...path])"
echo "  ✅ 改进了 Docker 容器权限设置"
echo "  ✅ 增强了启动脚本的日志输出"
echo "  ✅ 添加了调试 API (/api/debug/uploads)"
echo "  ✅ 优化了 Next.js 配置"
echo "📊 服务状态:"
docker-compose ps

echo ""
echo "🔗 访问链接:"
echo "   - 主页: http://localhost:3000"
echo "   - 健康检查: http://localhost:3000/api/health"
echo "   - 上传目录调试: http://localhost:3000/api/debug/uploads"
echo "   - 管理后台: http://localhost:3000/admin"

echo ""
echo "📝 查看日志命令:"
echo "   docker-compose logs -f web"

echo ""
echo "🧹 缓存清理建议:"
echo "   如果静态文件仍无法访问，请清理 CDN 缓存"
echo "   - Cloudflare: 登录面板 -> 缓存 -> 清除所有"
echo "   - 或使用 API: curl -X POST \"https://api.cloudflare.com/client/v4/zones/YOUR_ZONE_ID/purge_cache\" -H \"Authorization: Bearer YOUR_API_TOKEN\" -H \"Content-Type: application/json\" --data '{\"purge_everything\":true}'"

echo ""
echo "🔧 故障排除:"
echo "   1. 检查调试信息: curl https://ai-nav.hnch.pro/api/debug/uploads"
echo "   2. 测试特定文件: curl -I https://ai-nav.hnch.pro/uploads/de2e7d5a6f9c6472.png"
echo "   3. 查看容器日志: docker-compose logs web"
echo "   4. 检查文件权限: docker-compose exec web ls -la /app/public/uploads/"
