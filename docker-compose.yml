services:
  # 主应用服务 - 生产环境配置
  web:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    restart: unless-stopped
    container_name: ai_nav_web
    # ports:
    #   - "3000:3000"
    user: "0:0" # 以 root 用户启动，启动脚本会处理权限并切换用户
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      # 认证配置
      - AUTH_SECRET=${AUTH_SECRET}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - NEXTAUTH_URL_INTERNAL=${NEXTAUTH_URL_INTERNAL}
      - NEXT_PUBLIC_SITE_URL=${NEXT_PUBLIC_SITE_URL}
      # 数据库配置
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      # Logto 认证配置
      - AUTH_LOGTO_ISSUER=${AUTH_LOGTO_ISSUER}
      - AUTH_LOGTO_ID=${AUTH_LOGTO_ID}
      - AUTH_LOGTO_SECRET=${AUTH_LOGTO_SECRET}
      # 安全和域名配置
      - NEXT_PUBLIC_ALLOWED_DOMAINS=${NEXT_PUBLIC_ALLOWED_DOMAINS}
      - NEXT_IMAGE_DOMAINS=${NEXT_IMAGE_DOMAINS}
      # 功能开关
      - NEXT_PUBLIC_ENABLE_ANALYTICS=${NEXT_PUBLIC_ENABLE_ANALYTICS}
      # 生产环境特定配置
      - AUTH_TRUST_HOST=true
      - NEXT_TELEMETRY_DISABLED=1
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - proxy-net
    volumes:
      # 持久化上传文件 - 只挂载 uploads 目录
      - ai_nav_uploads:/app/public/uploads
      # 确保卷挂载后的权限正确
      # 注意：卷挂载会覆盖容器内的目录内容
    env_file:
      - .env
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # PostgreSQL 数据库 - 生产环境配置
  db:
    image: postgres:15-alpine
    restart: unless-stopped
    container_name: ai_nav_postgres
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
      # 生产环境性能优化
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - ai_nav_postgres_data:/var/lib/postgresql/data
      # 生产环境可以挂载自定义配置
      # - ./config/postgres.conf:/etc/postgresql/postgresql.conf:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - proxy-net
    env_file:
      - .env
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    restart: unless-stopped
    container_name: ai_nav_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - ai_nav_redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - proxy-net
    env_file:
      - .env
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: "0.5"
        reservations:
          memory: 128M
          cpus: "0.25"

volumes:
  ai_nav_postgres_data:
    driver: local
  ai_nav_uploads:
    driver: local
  ai_nav_redis_data:
    driver: local

networks:
  proxy-net:
    external: true
