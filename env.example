# ====================================
# 环境配置
# ====================================
NODE_ENV=production

# ====================================
# 数据库配置 (生产环境必须配置)
# ====================================
DB_USER=postgres
DB_PASSWORD=your_secure_password
DB_NAME=ai_nav
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:5432/${DB_NAME}?schema=public

# ====================================
# 站点配置 (生产环境必须配置)
# ====================================
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXTAUTH_URL=https://your-domain.com
NEXTAUTH_URL_INTERNAL=http://web:3000

# ====================================
# 认证配置 (生产环境必须配置)
# ====================================
# 生产环境使用安全密钥: openssl rand -base64 32
AUTH_SECRET=your_secure_auth_secret
AUTH_TRUST_HOST=true

# Logto 认证配置
AUTH_LOGTO_ISSUER="xxxxx"
AUTH_LOGTO_ID="xxxxxx"
AUTH_LOGTO_SECRET="xxxxx"

# ====================================
# 性能与缓存配置
# ====================================
NEXT_TELEMETRY_DISABLED=1
REDIS_PASSWORD=redis_secure_password

# ====================================
# 安全相关配置
# ====================================
# 设置允许的域名，用逗号分隔
# NEXT_PUBLIC_ALLOWED_DOMAINS=your-domain.com,www.your-domain.com

# ====================================
# 图片优化配置
# ====================================
# 如果使用 Next.js 图片优化，可以配置远程图片域名
# NEXT_IMAGE_DOMAINS=ai-bot.cn,your-cdn-domain.com

# ====================================
# SEO 和分析配置
# ====================================
NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
GOOGLE_SITE_VERIFICATION="your-google-verification-code"
BAIDU_SITE_VERIFICATION="your-baidu-verification-code"

# 其他分析工具 (可选)
# NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn
# NEXT_PUBLIC_UMAMI_WEBSITE_ID=your_umami_id
# NEXT_PUBLIC_UMAMI_SRC=https://analytics.your-domain.com/umami.js

# ====================================
# 社交媒体配置
# ====================================
TWITTER_HANDLE="@ai_nav"
FACEBOOK_APP_ID="your-facebook-app-id"

# ====================================
# 功能开关 (根据需求启用/禁用)
# ====================================
# NEXT_PUBLIC_ENABLE_ANALYTICS=false
# NEXT_PUBLIC_ENABLE_FEATURE_X=true
