import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // 编译器配置
  compiler: {
    // removeConsole: process.env.NODE_ENV === "production", // 生产环境移除 console
    styledComponents: true, // 启用 styled-components 支持
  },

  // 生产环境配置
  productionBrowserSourceMaps: false, // 生产环境不生成 source map
  output: "standalone", // 生成独立的部署包，适合容器化部署

  // 静态文件配置
  assetPrefix: process.env.NODE_ENV === "production" ? "" : "",
  trailingSlash: false,

  // SSR 配置
  reactStrictMode: true, // 启用 React 严格模式

  // 图片优化配置
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**", // 允许所有 HTTPS 图片
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "3000",
      },
      {
        protocol: "https",
        hostname: "ai-nav.hnch.pro",
      },
    ],
  },

  // 实验性功能
  experimental: {
    // 启用服务器操作
    serverActions: {
      allowedOrigins: ["ai-nav.hnch.pro"], // 允许的域名
    },
    // 优化包大小
    optimizePackageImports: ["lodash", "date-fns", "lucide-react"],
    // 启用 PPR (Partial Prerendering) - 需要 canary 版本
    // ppr: true,
  },

  // 性能优化
  poweredByHeader: false, // 移除 X-Powered-By 头
  compress: true, // 启用 gzip 压缩

  // 缓存配置
  onDemandEntries: {
    // 页面在内存中保持的时间
    maxInactiveAge: 25 * 1000,
    // 同时保持的页面数
    pagesBufferLength: 2,
  },

  // 构建输出配置
  typescript: {
    // 构建时检查类型错误
    ignoreBuildErrors: false,
  },
  eslint: {
    // 构建时检查 ESLint 错误
    ignoreDuringBuilds: false,
  },
};

export default nextConfig;
