{"name": "ai-nav", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.12.1", "scripts": {"fetch:menu": "tsx scripts/fetch-menu-data.ts", "crawl:ai-bot": "tsx scripts/crawl-ai-bot.ts", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "6.9.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@types/request-ip": "^0.0.41", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "ioredis": "^5.6.1", "js-sha256": "^0.11.1", "lucide-react": "^0.511.0", "mime-types": "^3.0.1", "next": "15.3.2", "next-auth": "5.0.0-beta.28", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-resizable-panels": "^3.0.2", "recharts": "^2.15.3", "request-ip": "^3.3.0", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "use-debounce": "^10.0.5", "vaul": "^1.1.2", "zod": "^3.25.32"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.3", "@stagewise/toolbar-next": "^0.2.0", "@tailwindcss/postcss": "^4", "@types/mime-types": "^3.0.0", "@types/node": "latest", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "prisma": "^6.9.0", "tailwindcss": "^4", "tsx": "^4.19.4", "tw-animate-css": "^1.3.0", "typescript": "^5"}}