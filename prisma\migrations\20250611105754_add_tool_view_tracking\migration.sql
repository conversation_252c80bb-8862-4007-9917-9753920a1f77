-- AlterTable
ALTER TABLE "Tool" ADD COLUMN     "lastViewedAt" TIMESTAMP(3);

-- CreateTable
CREATE TABLE "ToolView" (
    "id" TEXT NOT NULL,
    "toolId" TEXT NOT NULL,
    "ipAddress" TEXT NOT NULL,
    "viewedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "userAgent" TEXT,

    CONSTRAINT "ToolView_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ToolView_toolId_idx" ON "ToolView"("toolId");

-- CreateIndex
CREATE UNIQUE INDEX "ToolView_toolId_ipAddress_viewedAt_key" ON "ToolView"("toolId", "ipAddress", "viewedAt");

-- AddForeignKey
ALTER TABLE "ToolView" ADD CONSTRAINT "ToolView_toolId_fkey" FOREIGN KEY ("toolId") REFERENCES "Tool"("id") ON DELETE CASCADE ON UPDATE CASCADE;
