// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Category {
  id          String @id @default(cuid())
  name        String @unique
  slug        String @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  tools       Tool[]
  icon        String?
  order       Int     @default(0) // Added order field

  parentId    String?
  parent      Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
}

model Tool {
  id          String @id @default(cuid())
  name        String
  description String
  url         String
  iconUrl     String?
  website     String? // 工具的官方网站
  tags        String[] // 工具的标签，用于搜索和分类
  isFree      Boolean  @default(false)
  isFeatured  Boolean  @default(false) // 是否为精选工具
  views       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  category   Category? @relation(fields: [categoryId], references: [id])
  categoryId String?
  lastViewedAt DateTime?

  viewRecords ToolView[]
}

model ToolView {
  id        String   @id @default(cuid())
  toolId    String
  ipAddress String
  viewedAt  DateTime @default(now())
  userAgent String?

  tool Tool @relation(fields: [toolId], references: [id], onDelete: Cascade)

  @@unique([toolId, ipAddress, viewedAt])
  @@index([toolId])
}
