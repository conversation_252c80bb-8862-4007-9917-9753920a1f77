import { readFile } from "fs/promises";
import { PrismaClient } from "@prisma/client";

interface DataItem {
  category: string;
  title: string | null;
  imgUrl: string | null;
  desc: string | null;
  sourceLink: string;
}

interface AIResponse {
  slug: string;
  reason: string;
}

// 分类slug缓存，避免重复检查
const categorySlugCache = new Set<string>();

/**
 * 初始化分类slug缓存
 * @param prisma Prisma客户端
 */
const initSlugCache = async (prisma: PrismaClient) => {
  console.log("🔄 初始化分类slug缓存...");

  // 缓存现有的分类slug
  const existingCategories = await prisma.category.findMany({
    select: { slug: true },
  });
  existingCategories.forEach((cat) => categorySlugCache.add(cat.slug));

  console.log(`📊 缓存了 ${categorySlugCache.size} 个分类slug`);
};

/**
 * 生成唯一的分类slug
 * @param baseSlug 基础slug
 * @returns string 唯一的slug
 */
const generateUniqueSlug = (baseSlug: string): string => {
  let uniqueSlug = baseSlug;
  let counter = 1;

  while (categorySlugCache.has(uniqueSlug)) {
    uniqueSlug = `${baseSlug}-${counter}`;
    counter++;
  }

  // 将新生成的slug添加到缓存中
  categorySlugCache.add(uniqueSlug);

  return uniqueSlug;
};

/**
 * 将中文或非URL友好的标题转换为URL友好的slug
 * @param title 原始标题
 * @returns Promise<string> URL友好的slug
 * @example
 * await fetchTrAI("你好世界") => "hello-world"
 * await fetchTrAI("AI智能体") => "ai-agent"
 */
const fetchTrAI = async (title: string): Promise<string> => {
  try {
    const apiKey = "sk-vUrdTSJ1zJCyMrEVJbjNY8nxbBlz2AFNLkPzDQOnEp5FNs07";

    const response = await fetch(
      "https://airouter.mxyhi.com/v1/chat/completions",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
          Authorization: `Bearer ${apiKey}`,
        },
        body: JSON.stringify({
          model: "deepseek-ai/DeepSeek-V3",
          messages: [
            {
              role: "system",
              content: `你是一个专业的URL slug生成器。请将用户提供的中文或其他语言的标题转换为简洁、语义化的英文URL slug。

要求：
1. 使用小写英文字母和连字符(-)
2. 保持语义准确性，体现原标题的核心含义
3. 简洁明了，避免冗余词汇
4. 符合SEO最佳实践
5. 长度控制在2-5个单词之间

请以JSON格式返回结果：
{
  "slug": "生成的URL友好slug",
  "reason": "转换理由的简短说明"
}`,
            },
            {
              role: "user",
              content: `请将以下标题转换为URL友好的slug：${title}`,
            },
          ],
          max_tokens: 200,
          temperature: 0.3,
          response_format: { type: "json_object" },
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse: AIResponse = JSON.parse(data.choices[0].message.content);

    console.log(`✅ "${title}" -> "${aiResponse.slug}" (${aiResponse.reason})`);
    return aiResponse.slug;
  } catch (error) {
    console.error(`❌ 转换标题失败 "${title}":`, error);
    // 降级处理：生成简单的slug
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, "") // 移除特殊字符
      .replace(/\s+/g, "-") // 空格替换为连字符
      .replace(/-+/g, "-") // 多个连字符合并为一个
      .trim();
  }
};

/**
 * 批量处理数据，提高性能
 * @param items 数据项数组
 * @param batchSize 批次大小
 * @param processor 处理函数
 */
const processBatch = async <T, R>(
  items: T[],
  batchSize: number,
  processor: (item: T) => Promise<R>
): Promise<R[]> => {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    console.log(
      `📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(
        items.length / batchSize
      )} (${batch.length} 项)`
    );

    const batchResults = await Promise.all(batch.map(processor));

    results.push(...batchResults);

    // 避免API限流，批次间稍作延迟
    if (i + batchSize < items.length) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }

  return results;
};

const main = async () => {
  const prisma = new PrismaClient();

  try {
    console.log("🚀 开始处理数据...");

    // 初始化slug缓存
    await initSlugCache(prisma);

    // 读取数据文件
    const data: DataItem[] = JSON.parse(await readFile("./data.json", "utf-8"));
    console.log(`📊 共读取 ${data.length} 条数据`);

    // 获取所有唯一的分类
    const uniqueCategories = [...new Set(data.map((item) => item.category))];
    console.log(
      `📂 发现 ${uniqueCategories.length} 个分类: ${uniqueCategories.join(
        ", "
      )}`
    );

    // 批量检查和创建分类
    const existingCategories = await prisma.category.findMany({
      where: {
        name: { in: uniqueCategories },
      },
      select: { name: true },
    });

    const existingCategoryNames = new Set(
      existingCategories.map((cat) => cat.name)
    );
    const newCategories = uniqueCategories.filter(
      (cat) => !existingCategoryNames.has(cat)
    );

    if (newCategories.length > 0) {
      console.log(`➕ 创建新分类: ${newCategories.join(", ")}`);

      // 为新分类生成URL友好的唯一slug
      const categoryData = await processBatch(
        newCategories,
        3, // 每批处理3个分类，避免API限流
        async (categoryName) => {
          const baseSlug = await fetchTrAI(categoryName);
          const uniqueSlug = generateUniqueSlug(baseSlug);

          console.log(`📂 分类 "${categoryName}" -> slug: "${uniqueSlug}"`);

          return {
            name: categoryName,
            slug: uniqueSlug,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
        }
      );

      // 批量插入分类
      await prisma.category.createMany({
        data: categoryData,
        skipDuplicates: true,
      });

      console.log(`✅ 成功创建 ${newCategories.length} 个分类`);
    } else {
      console.log("ℹ️  所有分类已存在，无需创建");
    }

    // 处理数据项
    console.log("📝 开始处理数据项...");

    let newToolsCount = 0;
    let updatedToolsCount = 0;
    const newTools: any[] = [];
    const updatePromises: Promise<any>[] = [];

    await processBatch(
      data.filter((item) => item.title), // 过滤掉没有标题的项
      5, // 每批处理5个项目
      async (item) => {
        // 跳过没有标题的项目
        if (!item.title) {
          console.warn(`⚠️  跳过没有标题的项目`);
          return null;
        }

        const category = await prisma.category.findFirst({
          where: { name: item.category },
        });

        if (!category) {
          console.warn(`⚠️  分类不存在: ${item.category}`);
          return null;
        }

        // 生成工具URL和网站链接
        const hasValidSourceLink =
          item.sourceLink && item.sourceLink.trim() !== "";
        const toolUrl = hasValidSourceLink
          ? item.sourceLink
          : `https://cn.bing.com/search?q=${encodeURIComponent(item.title)}`;
        const website = hasValidSourceLink
          ? item.sourceLink
          : `https://cn.bing.com/search?q=${encodeURIComponent(item.title)}`;

        // 检查是否已存在相同标题的工具
        const existingTool = await prisma.tool.findFirst({
          where: {
            name: item.title,
            categoryId: category.id,
          },
        });

        if (existingTool) {
          // 更新现有工具
          console.log(`🔄 更新工具: "${item.title}" -> ${toolUrl}`);

          const updatePromise = prisma.tool.update({
            where: { id: existingTool.id },
            data: {
              description: item.desc || existingTool.description || "暂无描述",
              url: toolUrl,
              website: website,
              iconUrl: `/uploads/${item.imgUrl}` || existingTool.iconUrl,
              // tags: [],
              // tags: [item.category], // 使用分类作为标签
              updatedAt: new Date(),
            },
          });

          updatePromises.push(updatePromise);
          updatedToolsCount++;
          return { type: "update", tool: existingTool };
        } else {
          // 创建新工具
          console.log(`🔧 添加工具: "${item.title}" -> ${toolUrl}`);

          const newTool = {
            name: item.title,
            description: item.desc || "暂无描述",
            url: toolUrl,
            website: website,
            iconUrl: `/uploads/${item.imgUrl}`,
            categoryId: category.id,
            // isFree: false, // 默认设为免费
            isFeatured: false,
            // tags: [item.category], // 使用分类作为标签
            tags: [],
            views: 0,
          };

          newTools.push(newTool);
          newToolsCount++;
          return { type: "create", tool: newTool };
        }
      }
    );

    // 执行所有更新操作
    if (updatePromises.length > 0) {
      console.log(`🔄 执行 ${updatePromises.length} 个工具更新...`);
      await Promise.all(updatePromises);
      console.log(`✅ 成功更新 ${updatedToolsCount} 个工具`);
    }

    // 批量插入新工具
    if (newTools.length > 0) {
      await prisma.tool.createMany({
        data: newTools,
        skipDuplicates: true,
      });
      console.log(`✅ 成功添加 ${newToolsCount} 个新工具`);
    }

    // 输出处理结果统计
    console.log(`📊 处理结果统计:`);
    console.log(`   - 新增工具: ${newToolsCount} 个`);
    console.log(`   - 更新工具: ${updatedToolsCount} 个`);
    console.log(`   - 总计处理: ${newToolsCount + updatedToolsCount} 个`);

    if (newToolsCount === 0 && updatedToolsCount === 0) {
      console.log("ℹ️  没有工具需要添加或更新");
    }

    console.log("🎉 数据处理完成！");
  } catch (error) {
    console.error("❌ 处理数据时发生错误:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
};

// 优雅处理进程退出
process.on("SIGINT", async () => {
  console.log("\n⏹️  收到中断信号，正在清理...");
  process.exit(0);
});

process.on("SIGTERM", async () => {
  console.log("\n⏹️  收到终止信号，正在清理...");
  process.exit(0);
});

main().catch((error) => {
  console.error("💥 程序执行失败:", error);
  process.exit(1);
});
