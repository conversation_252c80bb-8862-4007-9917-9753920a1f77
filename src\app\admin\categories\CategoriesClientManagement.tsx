"use client";

import { use<PERSON><PERSON>back, useEffect, useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { PlusCircle, RefreshCw, Trash2, Edit3 } from "lucide-react";
import type { CategoryListItem } from "@/lib/data/categories";
import { Tree, TreeItem } from "@/components/ui/tree";

// 用于排序的最小化类型
interface CategoryOrderUpdate {
  id: string;
  parentId: string | null;
  order: number;
}

export interface ClientCategory extends CategoryListItem {
  children?: ClientCategory[];
}

interface CategoriesClientManagementProps {
  initialCategories: ClientCategory[];
}

// 转换函数：将扁平分类数组转换为树形结构
const buildCategoryTree = (categories: ClientCategory[]): ClientCategory[] => {
  const categoryMap = new Map<string, ClientCategory>();
  const rootCategories: ClientCategory[] = [];

  // 首先创建所有节点的映射
  categories.forEach((category) => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // 构建树形结构
  categories.forEach((category) => {
    const node = categoryMap.get(category.id)!;
    if (category.parentId) {
      const parent = categoryMap.get(category.parentId);
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(node);
      }
    } else {
      rootCategories.push(node);
    }
  });

  // 对每个层级的子分类按 order 字段排序
  const sortChildren = (items: ClientCategory[]): ClientCategory[] => {
    return items
      .sort((a, b) => (a.order || 0) - (b.order || 0))
      .map((item) => ({
        ...item,
        children: item.children ? sortChildren(item.children) : undefined,
      }));
  };

  return sortChildren(rootCategories);
};

// 转换函数：将分类数据转换为 Tree 组件所需的 TreeItem 格式
const categoriesToTreeItems = (categories: ClientCategory[]): TreeItem[] => {
  return categories.map(
    (category): TreeItem => ({
      id: category.id,
      label: category.name || "未命名分类", // 确保 label 始终为字符串
      icon: category.icon || undefined, // 确保 icon 为 string | undefined
      children: category.children
        ? categoriesToTreeItems(category.children)
        : undefined,
    })
  );
};

// 转换函数：将树形结构转换为扁平数组
// 将树形结构转换为带有顺序和层级的扁平数组
const flattenTreeWithOrder = (
  items: TreeItem[],
  parentId: string | null = null
): CategoryOrderUpdate[] => {
  const result: CategoryOrderUpdate[] = [];

  items.forEach((item, index) => {
    // 每个层级内的顺序从1开始
    const currentOrder = index + 1;
    result.push({
      id: item.id,
      parentId,
      order: currentOrder,
    });

    // 递归处理子项
    if (item.children?.length) {
      result.push(...flattenTreeWithOrder(item.children, item.id));
    }
  });

  return result;
};

// 工具函数：从 TreeItem 转换回 ClientCategory
const treeItemToCategory = (
  treeItem: TreeItem,
  categories: ClientCategory[]
): ClientCategory | undefined => {
  return categories.find((cat) => cat.id === treeItem.id);
};

export default function CategoriesClientManagement({
  initialCategories,
}: CategoriesClientManagementProps) {
  const [categories, setCategories] =
    useState<ClientCategory[]>(initialCategories);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null);
  const [editingCategory, setEditingCategory] = useState<ClientCategory | null>(
    null
  );
  const [formData, setFormData] = useState<{
    name: string;
    slug: string;
    parentId: string;
    description: string;
    icon: string;
  }>({
    name: "",
    slug: "",
    parentId: "",
    description: "",
    icon: "",
  });
  const [isSlugManuallyEdited, setIsSlugManuallyEdited] = useState(false);

  const generateSlug = (name: string): string => {
    return name
      .toString()
      .toLowerCase()
      .trim()
      .replace(/\s+/g, "-")
      .replace(/[^\w-]+/g, "")
      .replace(/--+/g, "-");
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setFormData((prev) => {
      const newState = { ...prev, name: newName };
      if (!isSlugManuallyEdited || !prev.slug) {
        newState.slug = generateSlug(newName);
      }
      return newState;
    });
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({ ...prev, slug: e.target.value }));
    setIsSlugManuallyEdited(true);
  };

  const regenerateSlug = () => {
    if (formData.name) {
      setFormData((prev) => ({ ...prev, slug: generateSlug(prev.name) }));
      setIsSlugManuallyEdited(false);
    } else {
      toast.info("请输入分类名称以生成Slug。");
    }
  };

  const loadCategories = useCallback(async () => {
    setLoading(true);
    try {
      const res = await fetch("/api/admin/categories");
      const data = await res.json();
      if (data.success && Array.isArray(data.data)) {
        const categories = data.data.map((cat: any) => ({
          ...cat,
          createdAt: new Date(cat.createdAt),
        }));
        setCategories(categories);
      } else {
        toast.error(data.message || "加载分类失败");
        setCategories(initialCategories);
      }
    } catch (error) {
      toast.error("加载分类时发生网络错误");
      setCategories(initialCategories);
    } finally {
      setLoading(false);
    }
  }, [initialCategories]);

  const getAvailableParentCategories = (): ClientCategory[] => {
    if (!editingCategory) {
      return categories;
    }
    const descendantIds = new Set<string>();
    const getAllDescendants = (
      categoryId: string,
      allCats: ClientCategory[]
    ) => {
      descendantIds.add(categoryId);
      const directChildren = allCats.filter((c) => c.parentId === categoryId);
      for (const child of directChildren) {
        if (!descendantIds.has(child.id)) {
          getAllDescendants(child.id, allCats);
        }
      }
    };
    getAllDescendants(editingCategory.id, categories);
    return categories.filter((cat) => !descendantIds.has(cat.id));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const url = editingCategory
        ? `/api/admin/categories/${editingCategory.id}`
        : "/api/admin/categories";
      const method = editingCategory ? "PUT" : "POST";

      const payload = {
        ...formData,
        parentId:
          formData.parentId === "" || formData.parentId === "null"
            ? null
            : formData.parentId,
      };

      const res = await fetch(url, {
        method: method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (data.success) {
        toast.success(editingCategory ? "分类更新成功！" : "分类创建成功！");
        setOpen(false);
        loadCategories();
        resetForm();
      } else {
        toast.error(data.message || "操作失败，请检查表单数据。");
      }
    } catch (error) {
      toast.error("操作失败，请稍后重试。");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = useCallback(async (id: string) => {
    setCategoryToDelete(id);
    setDeleteDialogOpen(true);
  }, []);

  const confirmDelete = useCallback(
    async (id: string) => {
      setLoading(true);
      try {
        const res = await fetch(`/api/admin/categories/${id}`, {
          method: "DELETE",
        });

        const data = await res.json();

        if (!data.success) {
          const error = new Error(data.message || "删除分类失败");
          error.name = data.code || "DELETE_CATEGORY_ERROR";
          throw error;
        }

        toast.success(data.message || "分类删除成功！");
        loadCategories();
      } catch (error: any) {
        // 只处理业务错误，不处理取消操作
        if (error.name !== "AbortError") {
          console.error("删除分类时出错:", error);
          toast.error(error?.message || "删除失败，请稍后重试");
        }
        return false; // 返回 false 表示操作失败
      } finally {
        setLoading(false);
        setDeleteDialogOpen(false);
      }
      return true; // 返回 true 表示操作成功
    },
    [loadCategories]
  );

  const handleEdit = useCallback((category: ClientCategory) => {
    setEditingCategory(category);
    setFormData({
      name: category.name || "",
      slug: category.slug || "",
      parentId: category.parentId || "",
      description: category.description || "",
      icon: category.icon || "",
    });
    setIsSlugManuallyEdited(true);
    setOpen(true);
  }, []);

  const resetForm = () => {
    setFormData({
      name: "",
      slug: "",
      parentId: "",
      description: "",
      icon: "",
    });
    setEditingCategory(null);
    setIsSlugManuallyEdited(false);
  };

  const handleOrderChange = async (treeItems: TreeItem[]) => {
    setLoading(true);
    try {
      // 将新的树形结构转换为更新数据
      const categories = flattenTreeWithOrder(treeItems);

      // 发送更新请求
      const res = await fetch("/api/admin/categories", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ categories }),
      });
      const data = await res.json();

      if (data.success) {
        toast.success("分类顺序更新成功！");
        loadCategories(); // 重新加载最新数据
      } else {
        toast.error(data.message || "更新顺序失败。");
      }
    } catch (error) {
      toast.error("更新顺序失败，请稍后重试。");
    } finally {
      setLoading(false);
    }
  };

  const availableParents = useMemo(
    () => getAvailableParentCategories(),
    [categories, editingCategory]
  );

  // 将分类数据转换为树形结构
  const categoryTree = useMemo(() => {
    const tree = buildCategoryTree(categories);
    return categoriesToTreeItems(tree);
  }, [categories]);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">分类管理</h1>
        <div className="flex items-center space-x-2">
          <Button onClick={loadCategories} variant="outline" disabled={loading}>
            <RefreshCw
              className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            刷新列表
          </Button>
          <Button
            onClick={() => {
              resetForm();
              setOpen(true);
            }}
            disabled={loading}
          >
            <PlusCircle className="w-4 h-4 mr-2" />
            新增分类
          </Button>
        </div>
      </div>

      {loading && categories.length === 0 && <p>正在加载分类数据...</p>}
      {!loading && categories.length === 0 && <p>暂无分类数据。</p>}

      {categories.length > 0 && (
        <div className="py-4">
          <Tree
            items={categoryTree}
            onOrderChange={handleOrderChange}
            onEdit={(item) => {
              const category = treeItemToCategory(item, categories);
              if (category) {
                handleEdit(category);
              }
            }}
            onDelete={(item) => handleDelete(item.id)}
            onAddChild={(item) => {
              resetForm();
              setFormData((prev) => ({
                ...prev,
                parentId: item.id,
              }));
              setOpen(true);
            }}
          />
        </div>
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除分类</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除这个分类吗？此操作不可撤销，其下的子分类的归属将被解除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>取消</AlertDialogCancel>
            <AlertDialogAction
              disabled={loading}
              onClick={async () => {
                if (categoryToDelete) {
                  await confirmDelete(categoryToDelete);
                }
              }}
            >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (loading) return;
          setOpen(isOpen);
          if (!isOpen) resetForm();
        }}
      >
        <DialogContent className="sm:max-w-[480px]">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? "编辑分类" : "新增分类"}
            </DialogTitle>
            <DialogDescription>
              {editingCategory
                ? `正在编辑分类: ${editingCategory.name}`
                : "创建一个新的分类。Slug 将根据名称自动生成，也可以手动修改。"}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 pt-4">
            <div className="space-y-1.5">
              <Label htmlFor="name">
                名称 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={handleNameChange}
                required
              />
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="slug">
                Slug <span className="text-red-500">*</span>
              </Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={handleSlugChange}
                  required
                />
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={regenerateSlug}
                  aria-label="Regenerate slug"
                >
                  <RefreshCw className="w-4 h-4" />
                </Button>
              </div>
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="parentId">父分类</Label>
              <Select
                value={formData.parentId || "null"}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    parentId: value === "null" ? "" : value,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择父分类 (可选)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="null">无 (顶级分类)</SelectItem>
                  {availableParents.map((cat: ClientCategory) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={3}
              />
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="icon">图标名称 (Lucide Icon)</Label>
              <Input
                id="icon"
                value={formData.icon}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, icon: e.target.value }))
                }
                placeholder="例如 Home, Settings"
              />
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading
                  ? editingCategory
                    ? "更新中..."
                    : "创建中..."
                  : editingCategory
                  ? "保存更改"
                  : "创建分类"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
