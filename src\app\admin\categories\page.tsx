import { getAllCategories, CategoryListItem } from "@/lib/data/categories";
import CategoriesClientManagement, {
  ClientCategory,
} from "./CategoriesClientManagement";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default async function CategoriesPage() {
  let rawCategories: CategoryListItem[] = [];
  
  try {
    rawCategories = await getAllCategories();
  } catch (error) {
    console.error('Error fetching categories:', error);
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>加载失败</AlertTitle>
          <AlertDescription>
            无法加载分类列表。请稍后重试或检查数据库连接。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // The ClientCategory interface extends CategoryListItem and adds an optional `children` property.
  // The getAllCategories function in src/lib/data/categories.ts returns CategoryListItem[],
  // so a direct mapping is appropriate to ensure the type is ClientCategory[].
  const initialCategories: ClientCategory[] = rawCategories.map((cat) => ({
    ...cat,
    // `children` will be undefined here, which is fine as it's optional in ClientCategory.
    // The client component can build the children structure if needed.
  }));

  return (
    <div className="container mx-auto p-6">
      <CategoriesClientManagement initialCategories={initialCategories} />
    </div>
  );
}
