"use client";

import { useEffect, useState, useMemo } from "react";
import dynamic from "next/dynamic";
import { DashboardCard } from "@/components/admin/DashboardCard";
// import { ChartContainer } from "@/components/ui/chart"; // ChartContainer might not be used if we dynamic import the whole chart card
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LayoutGrid, Wrench, Users, Clock } from "lucide-react";
import type { DashboardData, CategoryDistribution } from "@/lib/data/dashboard"; // Import the type
import { CACHE, MESSAGES, TIME } from "@/lib/constants/app.constants";

// Define props for the dynamically imported chart component
interface DynamicBarChartProps {
  data: CategoryDistribution[];
}

const DynamicBarChart = dynamic<DynamicBarChartProps>(
  () => import("@/components/admin/charts/DynamicAdminBarChart"), // Assume we create this new component
  {
    loading: () => <p className="text-center p-4">{MESSAGES.LOADING.CHART}</p>,
    ssr: false, // Charts are usually client-side interactive
  }
);

interface DashboardClientContentProps {
  initialData: DashboardData | null;
}

/**
 * 仪表盘客户端内容组件
 * @param props - 组件属性
 * @param props.initialData - 初始数据
 * @returns 仪表盘客户端内容JSX元素
 */
export default function DashboardClientContent({
  initialData,
}: DashboardClientContentProps) {
  const [data, setData] = useState<DashboardData | null>(initialData);
  const [error, setError] = useState<string | null>(null);
  // Loading state for subsequent fetches, initial load is handled by SSR
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchData = async () => {
    setIsRefreshing(true);
    setError(null);
    try {
      const response = await fetch("/api/admin/dashboard/stats");
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})); // Try to parse error
        throw new Error(
          errorData.error || `获取仪表盘数据失败: ${response.status}`
        );
      }
      const json: DashboardData = await response.json();
      setData(json);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "获取仪表盘数据时发生未知错误"
      );
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    // Set up interval for refreshing data
    const interval = setInterval(fetchData, CACHE.DASHBOARD_REFRESH_INTERVAL);
    return () => clearInterval(interval);
  }, []);

  // Memoize chart data to prevent unnecessary re-renders if other state changes
  const chartData = useMemo(
    () => data?.distribution || [],
    [data?.distribution]
  );

  // 检查是否为空数据库状态（有数据但所有统计都是0）
  const isEmptyDatabase = useMemo(() => {
    const displayData = data || initialData;
    return (
      displayData &&
      displayData.stats.categoriesCount === 0 &&
      displayData.stats.toolsCount === 0 &&
      displayData.stats.totalViews === 0
    );
  }, [data, initialData]);

  if (!data && !initialData && !error) {
    // This case should ideally be covered by SSR loading or error state in parent
    return <div className="p-6">{MESSAGES.LOADING.DASHBOARD}</div>;
  }

  if (error && !data) {
    // Show error if initial data also failed or no data to show
    return <div className="p-6 text-red-500">错误: {error}</div>;
  }

  // If there's an error but we have some (possibly stale) data, show data + error
  // Or if initialData is null and an error occurred during initial SSR fetch (handled by parent)

  const displayData = data || initialData; // Prefer fresh data, fallback to initial

  return (
    <div className="p-6 space-y-6">
      {error && (
        <div className="p-4 text-red-500 bg-red-100 rounded-md">
          刷新错误: {error} (正在显示上次成功加载或初始数据)
        </div>
      )}
      {isRefreshing && !error && (
        <div className="p-4 text-blue-500 bg-blue-100 rounded-md">
          {MESSAGES.LOADING.REFRESHING}
        </div>
      )}

      {displayData ? (
        <>
          {/* 空数据库状态提示 */}
          {isEmptyDatabase && (
            <div className="p-4 text-blue-600 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-medium mb-2">欢迎使用AI导航管理后台！</h3>
              <p className="text-sm">
                当前数据库为空，您可以开始添加分类和工具来构建您的AI工具导航站点。
              </p>
            </div>
          )}

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <DashboardCard
              title="总分类数"
              value={displayData.stats.categoriesCount}
              icon={LayoutGrid}
            />
            <DashboardCard
              title="总工具数"
              value={displayData.stats.toolsCount}
              icon={Wrench}
            />
            <DashboardCard
              title="总访问量"
              value={displayData.stats.totalViews}
              icon={Users}
            />
            <DashboardCard
              title="最近新增"
              value={displayData.stats.recentToolsCount}
              description={`最近${TIME.RECENT_TOOLS_DAYS}天`}
              icon={Clock}
            />
          </div>

          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <CardTitle>分类工具分布</CardTitle>
              </CardHeader>
              <CardContent className="h-[400px] w-full">
                {/* Render the dynamically imported chart */}
                {chartData && chartData.length > 0 ? (
                  <DynamicBarChart data={chartData} />
                ) : (
                  <div className="flex flex-col items-center justify-center h-full text-gray-500">
                    <LayoutGrid className="h-12 w-12 mb-4 text-gray-300" />
                    <p className="text-center">
                      {isEmptyDatabase
                        ? "暂无分类数据，请先添加分类和工具"
                        : MESSAGES.EMPTY.NO_CHART_DATA}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      ) : (
        !error && <div className="p-6">{MESSAGES.EMPTY.NO_DASHBOARD_DATA}</div>
        // This state should be rare if initialData is handled correctly by parent
      )}
    </div>
  );
}
