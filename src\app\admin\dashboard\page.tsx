import { getDashboardData, DashboardData } from "@/lib/data/dashboard";
import DashboardClientContent from "./DashboardClientContent"; // 导入新的客户端组件
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "仪表盘 - 管理后台",
  description: "查看站点统计数据和工具分布情况。",
};

export default async function DashboardPage() {
  const initialData: DashboardData | null = await getDashboardData();

  if (!initialData) {
    // 处理 getDashboardData 返回 null 的情况（数据库连接错误等）
    return (
      <div className="p-6">
        <h1 className="text-2xl font-semibold mb-4">仪表盘</h1>
        <div className="text-red-500 bg-red-100 p-4 rounded-md">
          加载仪表盘数据失败。请检查数据库连接或稍后重试。
        </div>
      </div>
    );
  }

  return <DashboardClientContent initialData={initialData} />;
}
