import { auth, isAdmin } from "@/lib/auth";
import { redirect } from "next/navigation";
import { Sidebar } from "@/components/admin/Sidebar";
import { AdminHeader } from "@/components/admin/AdminHeader";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  try {
    const session = await auth();

    if (!session) {
      return redirect("/auth/signin?callbackUrl=/admin");
    }

    // 验证管理员权限
    if (!isAdmin(session)) {
      return redirect("/");
    }

    return (
      <div className="min-h-screen">
        <div className="flex h-screen">
          <Sidebar />
          <div className="flex-1 flex flex-col overflow-hidden">
            <AdminHeader />
            <main className="flex-1 overflow-y-auto">
              <div className="container mx-auto px-6 py-8">{children}</div>
            </main>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error("Admin layout error:", error);
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <div className="text-center p-8">
          <h1 className="text-2xl font-bold text-red-600 mb-4">系统错误</h1>
          <p className="text-gray-600">抱歉，访问管理页面时出现错误</p>
        </div>
      </div>
    );
  }
}
