"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  RefreshCw,
  ExternalLink,
  Database,
  Clock,
  FileText,
  Trash2,
  CheckCircle,
  XCircle,
} from "lucide-react";

interface SitemapStats {
  database: {
    categories: number;
    tools: number;
    estimatedSitemapEntries: number;
  };
  cache: {
    lastModified: string | null;
    status: {
      sitemap: string;
      categories: string;
      tools: string;
      tags: string;
    };
    ttl?: {
      sitemap: number;
      categories: number;
      tools: number;
      tags: number;
    };
  };
  urls: {
    sitemap: string;
    robots: string;
  };
}

export default function SitemapManagePage() {
  const [stats, setStats] = useState<SitemapStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [regenerating, setRegenerating] = useState(false);
  const [clearing, setClearing] = useState(false);

  // 获取sitemap统计信息
  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/sitemap");
      if (!response.ok) {
        throw new Error("Failed to fetch sitemap stats");
      }
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error("Error fetching sitemap stats:", error);
      toast.error("获取sitemap统计信息失败");
    } finally {
      setLoading(false);
    }
  };

  // 重新生成sitemap
  const regenerateSitemap = async () => {
    setRegenerating(true);
    try {
      const response = await fetch("/api/admin/sitemap", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ clearCache: true }),
      });

      if (!response.ok) {
        throw new Error("Failed to regenerate sitemap");
      }

      const result = await response.json();
      toast.success(result.message);
      
      // 刷新统计信息
      await fetchStats();
    } catch (error) {
      console.error("Error regenerating sitemap:", error);
      toast.error("重新生成sitemap失败");
    } finally {
      setRegenerating(false);
    }
  };

  // 清除缓存
  const clearCache = async () => {
    setClearing(true);
    try {
      const response = await fetch("/api/admin/sitemap", {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to clear cache");
      }

      const result = await response.json();
      toast.success(result.message);
      
      // 刷新统计信息
      await fetchStats();
    } catch (error) {
      console.error("Error clearing cache:", error);
      toast.error("清除缓存失败");
    } finally {
      setClearing(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center">
          <p className="text-muted-foreground">无法加载sitemap统计信息</p>
          <Button onClick={fetchStats} className="mt-4">
            重试
          </Button>
        </div>
      </div>
    );
  }

  const formatTTL = (seconds: number) => {
    if (seconds <= 0) return "已过期";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  const getCacheStatusBadge = (status: string) => {
    return status === "cached" ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        <CheckCircle className="h-3 w-3 mr-1" />
        已缓存
      </Badge>
    ) : (
      <Badge variant="secondary" className="bg-red-100 text-red-800">
        <XCircle className="h-3 w-3 mr-1" />
        未缓存
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sitemap 管理</h1>
          <p className="text-muted-foreground">
            管理网站的sitemap生成和缓存
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={clearCache}
            variant="outline"
            disabled={clearing}
          >
            {clearing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Trash2 className="h-4 w-4 mr-2" />
            )}
            清除缓存
          </Button>
          <Button
            onClick={regenerateSitemap}
            disabled={regenerating}
          >
            {regenerating ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            重新生成
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* 数据库统计 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              数据库统计
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>分类数量:</span>
              <Badge variant="outline">{stats.database.categories}</Badge>
            </div>
            <div className="flex justify-between">
              <span>工具数量:</span>
              <Badge variant="outline">{stats.database.tools}</Badge>
            </div>
            <div className="flex justify-between">
              <span>预估条目数:</span>
              <Badge variant="outline">{stats.database.estimatedSitemapEntries}</Badge>
            </div>
          </CardContent>
        </Card>

        {/* 缓存状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              缓存状态
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span>主sitemap:</span>
              {getCacheStatusBadge(stats.cache.status.sitemap)}
            </div>
            <div className="flex justify-between items-center">
              <span>分类数据:</span>
              {getCacheStatusBadge(stats.cache.status.categories)}
            </div>
            <div className="flex justify-between items-center">
              <span>工具数据:</span>
              {getCacheStatusBadge(stats.cache.status.tools)}
            </div>
            <div className="flex justify-between items-center">
              <span>标签数据:</span>
              {getCacheStatusBadge(stats.cache.status.tags)}
            </div>
            {stats.cache.lastModified && (
              <>
                <Separator />
                <div className="text-sm text-muted-foreground">
                  最后更新: {new Date(stats.cache.lastModified).toLocaleString()}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sitemap 链接 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Sitemap 链接
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <span>主 Sitemap:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(stats.urls.sitemap, "_blank")}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              查看
            </Button>
          </div>
          <div className="flex items-center justify-between">
            <span>Robots.txt:</span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(stats.urls.robots, "_blank")}
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              查看
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
