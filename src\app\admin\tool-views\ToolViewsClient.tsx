"use client";

import { useState, useMemo, useCallback } from "react";
import useSWR from "swr";
import { AdminTable, Column } from "@/components/admin/AdminTable";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useDebounce } from "use-debounce";
import { FormattedDate } from "@/components/ui/FormattedDate";
import { Tool } from "@prisma/client";
import { PaginationState } from "@/types/global";

// 定义 API 返回的数据结构
interface ToolViewData {
  tool: Tool;
  viewCount: number;
  lastViewed: Date | null;
}

interface ApiResponse {
  data: ToolViewData[];
  pagination: PaginationState;
}

interface ToolViewsClientProps {
  initialData: ApiResponse;
}

const fetcher = (url: string) => fetch(url).then((res) => res.json());

const ToolViewsClient = ({ initialData }: ToolViewsClientProps) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm] = useDebounce(searchTerm, 500);

  const { data, error, isLoading, mutate } = useSWR(
    () => {
      const params = new URLSearchParams({
        page: String(page),
        pageSize: String(pageSize),
      });
      if (debouncedSearchTerm) {
        params.set("search", debouncedSearchTerm);
      }
      return `/api/admin/tool-views?${params.toString()}`;
    },
    fetcher,
    {
      fallbackData: initialData,
      keepPreviousData: true,
      revalidateOnFocus: false,
    }
  );

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1); // 改变页面大小时重置到第一页
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(1); // 新搜索时重置到第一页
  };

  const columns: Column<ToolViewData>[] = useMemo(
    () => [
      {
        key: "tool",
        title: "工具名称",
        render: (record) => record.tool.name,
      },
      {
        key: "viewCount",
        title: "浏览次数",
        render: (record) => record.viewCount,
      },
      {
        key: "lastViewed",
        title: "最后浏览时间",
        render: (record) =>
          record.lastViewed ? (
            <FormattedDate date={record.lastViewed} />
          ) : (
            "暂无"
          ),
      },
    ],
    []
  );

  const apiResponse = data?.data as ApiResponse | undefined;
  const toolViews = apiResponse?.data ?? [];
  const pagination = apiResponse?.pagination;

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold">工浏览统计</h1>
        <Button onClick={() => mutate()} variant="outline" disabled={isLoading}>
          <RefreshCw
            className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
          />
          刷新
        </Button>
      </div>

      <div className="flex items-center">
        <Input
          placeholder="按工具名称搜索..."
          value={searchTerm}
          onChange={handleSearchChange}
          className="max-w-sm"
        />
      </div>

      {error && <p className="text-red-500">加载数据失败。</p>}

      <AdminTable
        columns={columns}
        data={toolViews}
        loading={isLoading}
        pagination={pagination}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />
    </div>
  );
};

export default ToolViewsClient;
