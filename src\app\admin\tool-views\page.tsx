import ToolViewsClient from "@/app/admin/tool-views/ToolViewsClient";
import { getToolViews } from "@/lib/data/admin/tool-views";
import { ADMIN } from "@/lib/constants/app.constants";
import { Metadata } from "next";

export const dynamic = "force-dynamic"; // Opt into dynamic rendering

export const metadata: Metadata = {
  title: "浏览统计 - 管理后台",
  description: "查看工具浏览次数和统计数据。",
};

interface ToolViewsPageProps {
  searchParams: Promise<{
    page?: string;
    pageSize?: string;
    search?: string;
  }>;
}

const ToolViewsPage = async ({ searchParams }: ToolViewsPageProps) => {
  // 等待 searchParams Promise 解析
  const params = await searchParams;
  const page = Number(params.page) || 1;
  const pageSize = Number(params.pageSize) || ADMIN.DEFAULT_PAGE_SIZE;
  const search = params.search;

  const initialData = await getToolViews({ page, pageSize, search });

  return <ToolViewsClient initialData={initialData} />;
};

export default ToolViewsPage;
