"use client";

import { use<PERSON><PERSON>back, useEffect, useState, useMemo } from "react";
import { AdminTable, Column } from "@/components/admin/AdminTable";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { PlusCircle, RefreshCw, Trash2, Edit3, Upload } from "lucide-react";
import { ImageUpload } from "@/components/ui/image-upload";
import { CategorySearchSelect } from "@/components/admin/CategorySearchSelect";
import type { ToolWithCategory } from "@/lib/data/tools"; // For initial data
import type { CategoryListItem } from "@/lib/data/categories"; // For initial categories

// Client-side representation of a Tool
export interface ClientTool
  extends Omit<ToolWithCategory, "category" | "createdAt"> {
  categoryName?: string; // For display in table
  createdAt: string; // Keep as string for simplicity or Date if needed for client formatting
}

// Simplified category for dropdown
export interface ClientCategorySimple {
  id: string;
  name: string;
}

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

interface ToolsClientManagementProps {
  initialTools: ClientTool[];
  initialCategories: ClientCategorySimple[];
  initialPagination: PaginationState;
}

export default function ToolsClientManagement({
  initialTools,
  initialCategories,
  initialPagination,
}: ToolsClientManagementProps) {
  // 初始加载
  const [tools, setTools] = useState<ClientTool[]>(initialTools);

  const [categories, setCategories] =
    useState<ClientCategorySimple[]>(initialCategories);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [editingTool, setEditingTool] = useState<ClientTool | null>(null);
  const [pagination, setPagination] =
    useState<PaginationState>(initialPagination);
  const [filters, setFilters] = useState({
    search: "",
    categoryId: "", // 空字符串表示全部分类
    isFeatured: undefined as boolean | undefined,
  });

  // 添加防抖效果，避免频繁触发搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      if (filters.search || filters.categoryId) {
        loadTools(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [filters.search, filters.categoryId]);

  const initialFormData = useMemo(
    () => ({
      name: "",
      description: "",
      url: "",
      iconUrl: "",
      website: "",
      tags: [] as string[],
      isFree: false,
      isFeatured: false,
      categoryId: "",
    }),
    []
  );

  const [formData, setFormData] = useState(initialFormData);

  const loadTools = useCallback(
    async (pageToLoad: number) => {
      // Changed parameter name for clarity and type
      setLoading(true);
      try {
        // 构建查询参数
        const params = new URLSearchParams({
          page: String(pageToLoad), // Use pageToLoad directly
          pageSize: String(pagination.pageSize),
        });

        if (filters.search) params.set("search", filters.search);
        if (filters.categoryId) params.set("categoryId", filters.categoryId);
        if (filters.isFeatured !== undefined)
          params.set("isFeatured", String(filters.isFeatured));

        console.log(
          `[TOOLS_CLIENT] Fetching: /api/admin/tools?${params.toString()}`
        );
        const res = await fetch(`/api/admin/tools?${params.toString()}`);
        console.log(
          `[TOOLS_CLIENT] Response status: ${res.status}, ok: ${res.ok}`
        );

        if (!res.ok) {
          let errorText = `API request failed with status ${res.status}`;
          try {
            const text = await res.text();
            console.error(`[TOOLS_CLIENT] API error response text: ${text}`);
            errorText += `: ${text.substring(0, 100)}...`; // 避免日志过长
          } catch (textError) {
            console.error(
              `[TOOLS_CLIENT] Could not read error response text:`,
              textError
            );
          }
          throw new Error(errorText);
        }

        let responseData;
        try {
          // 克隆响应对象以备后用，因为 .json() 会消耗响应体
          const resClone = res.clone();
          responseData = await res.json();
          console.log("[TOOLS_CLIENT] Parsed JSON data:", responseData);
        } catch (jsonError) {
          console.error("[TOOLS_CLIENT] Failed to parse JSON:", jsonError);
          try {
            // 如果 JSON 解析失败，尝试从克隆的响应中读取原始文本
            const rawText = await res.clone().text(); // 使用 res.clone() 避免 "body already used"
            console.error(
              "[TOOLS_CLIENT] Raw response text that failed JSON parsing (first 500 chars):",
              rawText.substring(0, 500)
            );
          } catch (rawTextError) {
            console.error(
              "[TOOLS_CLIENT] Could not get raw text after JSON parse failure:",
              rawTextError
            );
          }
          throw jsonError; // 重新抛出 JSON 解析错误
        }

        if (responseData.success) {
          console.log("[TOOLS_CLIENT] API call successful, processing data...");
          if (responseData.data && Array.isArray(responseData.data.data)) {
            setTools(
              responseData.data.data.map((tool: any) => ({
                ...tool,
                categoryName: tool.category?.name,
                createdAt: new Date(tool.createdAt).toISOString(),
              }))
            );
          } else {
            console.error(
              "[TOOLS_CLIENT] Expected responseData.data.data to be an array, but got:",
              responseData.data?.data
            );
            toast.error("从服务器接收到的工具数据格式不正确。");
            setTools([]);
          }

          if (responseData.data && responseData.data.pagination) {
            setPagination(responseData.data.pagination);
          } else {
            console.error(
              "[TOOLS_CLIENT] Expected responseData.data.pagination to exist, but got:",
              responseData.data?.pagination
            );
            toast.error("从服务器接收到的分页数据格式不正确。");
            // 可以考虑设置一个默认的或错误的分页状态
          }
        } else {
          console.error(
            "[TOOLS_CLIENT] API call returned success:false. Message:",
            responseData.message
          );
          toast.error(
            responseData.message || "加载工具列表失败 (API success:false)"
          );
        }
      } catch (error) {
        console.error("[TOOLS_CLIENT] Error in loadTools catch block:", error);
        toast.error("加载工具列表时发生网络错误。");
      } finally {
        setLoading(false);
      }
    },
    // pagination.page is removed from dependencies as pageToLoad is passed directly.
    [pagination.pageSize, filters, setLoading, setTools, setPagination]
  );

  // useEffect for initial load, and when filters or pageSize change.
  // This effect will always load page 1 for the given filters/pageSize.
  useEffect(() => {
    loadTools(1); // Always load page 1 when filters or pageSize change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    filters.search,
    filters.categoryId,
    filters.isFeatured,
    pagination.pageSize,
    loadTools,
  ]);
  // Added loadTools to dependencies: if the function definition changes (e.g. its own deps change), re-run.

  // Optionally, load categories if they can change and need refresh
  // For now, assume initialCategories is sufficient for the form dropdown.

  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setEditingTool(null);
  }, [initialFormData, setFormData, setEditingTool]);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      try {
        console.log("开始提交表单，当前formData:", formData);

        // 验证必填字段
        if (!formData.name || !formData.url) {
          const missingFields = [];
          if (!formData.name) missingFields.push("名称");
          if (!formData.url) missingFields.push("URL");

          console.error("缺少必填字段:", missingFields);
          toast.error(`请填写所有必填字段（${missingFields.join("、")}）`);
          setLoading(false);
          return;
        }

        const url = editingTool
          ? `/api/admin/tools/${editingTool.id}`
          : "/api/admin/tools";
        const method = editingTool ? "PUT" : "POST";

        console.log("准备提交的表单数据:", {
          ...formData,
          iconUrl: formData.iconUrl || "(空字符串)",
          tags: Array.isArray(formData.tags)
            ? formData.tags
            : String(formData.tags)
                .split(",")
                .map((t) => t.trim())
                .filter(Boolean),
        });

        const payload = {
          ...formData,
          // 确保 iconUrl 是字符串，如果未上传则使用空字符串
          iconUrl: formData.iconUrl || "",
          tags: Array.isArray(formData.tags)
            ? formData.tags
            : (formData.tags as unknown as string)
                .split(",")
                .map((tag) => tag.trim())
                .filter(Boolean),
        };

        const res = await fetch(url, {
          method: method,
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(payload),
        });
        const data = await res.json();
        if (data.success) {
          toast.success(editingTool ? "工具更新成功！" : "工具创建成功！");
          setOpen(false);
          // 保持当前页面，如果是新增则回到第一页
          loadTools(editingTool ? pagination.page : 1);
          resetForm();
        } else {
          toast.error(data.message || "操作失败，请检查表单数据。");
        }
      } catch (error) {
        toast.error("操作失败，请稍后重试。");
      } finally {
        setLoading(false);
      }
    },
    [
      editingTool,
      formData,
      loadTools,
      resetForm,
      setLoading,
      setOpen,
      pagination.page,
    ]
  );

  const handleDelete = useCallback(
    async (id: string) => {
      if (!confirm("确定要删除这个工具吗？")) return;
      setLoading(true);
      try {
        const res = await fetch(`/api/admin/tools/${id}`, {
          method: "DELETE",
        });
        const data = await res.json();
        if (data.success) {
          toast.success("工具删除成功！");

          // 智能处理页面跳转：
          // 如果当前页面只有1条数据且不是第一页，则跳转到上一页
          // 否则保持当前页面
          const shouldGoToPreviousPage =
            tools.length === 1 && pagination.page > 1;
          const targetPage = shouldGoToPreviousPage
            ? pagination.page - 1
            : pagination.page;

          loadTools(targetPage);
        } else {
          toast.error(data.message || "删除失败。");
        }
      } catch (error) {
        toast.error("删除失败，请稍后重试。");
      } finally {
        setLoading(false);
      }
    },
    [loadTools, setLoading, tools.length, pagination.page]
  );

  const handleEdit = useCallback(
    (tool: ClientTool) => {
      console.log("Editing tool:", tool); // 调试日志
      setEditingTool(tool);
      setFormData({
        name: tool.name || "",
        description: tool.description || "",
        url: tool.url || "",
        // 确保 iconUrl 是字符串，如果未定义则使用空字符串
        iconUrl: tool.iconUrl ? String(tool.iconUrl) : "",
        website: tool.website || "",
        tags: tool.tags || [],
        isFree: tool.isFree || false,
        isFeatured: tool.isFeatured || false,
        categoryId: tool.categoryId || "",
      });
      setOpen(true);
    },
    [setEditingTool, setFormData, setOpen]
  );

  const handleTagsChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const tagsString = e.target.value;
      setFormData((prev) => ({
        ...prev,
        tags: tagsString
          .split(",")
          .map((tag) => tag.trim())
          .filter(Boolean),
      }));
    },
    [setFormData]
  );

  const columns: Column<ClientTool>[] = useMemo(
    () => [
      { key: "name", title: "名称" },
      {
        key: "categoryName", // Display category name
        title: "分类",
        render: (record) => record.categoryName || "N/A",
      },
      {
        key: "isFree",
        title: "免费",
        render: (record) => (record.isFree ? "是" : "否"),
      },
      {
        key: "isFeatured",
        title: "推荐",
        render: (record) => (record.isFeatured ? "是" : "否"),
      },
      {
        key: "id",
        title: "操作",
        render: (record) => (
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleEdit(record)}
              disabled={loading}
            >
              <Edit3 className="w-4 h-4 mr-1" /> 编辑
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleDelete(record.id)}
              disabled={loading}
            >
              <Trash2 className="w-4 h-4 mr-1" /> 删除
            </Button>
          </div>
        ),
      },
    ],
    [loading, handleEdit, handleDelete]
  );

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold">工具管理</h1>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => loadTools(pagination.page)}
              variant="outline"
              disabled={loading}
            >
              <RefreshCw
                className={`w-4 h-4 mr-2 ${loading ? "animate-spin" : ""}`}
              />
              刷新列表
            </Button>
            <Button
              onClick={() => {
                resetForm();
                setOpen(true);
              }}
              disabled={loading}
            >
              <PlusCircle className="w-4 h-4 mr-2" />
              新增工具
            </Button>
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="搜索工具名称或描述..."
              value={filters.search}
              onChange={(e) => {
                setFilters((prev) => ({ ...prev, search: e.target.value }));
              }}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  loadTools(1);
                }
              }}
              className="w-full"
            />
          </div>
          <div className="w-full md:w-48">
            <Select
              value={filters.categoryId || "all"}
              onValueChange={(value) => {
                setFilters((prev) => ({
                  ...prev,
                  categoryId: value === "all" ? "" : value,
                }));
                loadTools(1);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="全部分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部分类</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button
            onClick={() => loadTools(1)}
            disabled={loading}
            className="w-full md:w-auto"
          >
            搜索
          </Button>
        </div>
      </div>

      {loading && tools.length === 0 && <p>正在加载工具数据...</p>}
      {!loading && tools.length === 0 && <p>暂无工具数据。</p>}

      {tools.length > 0 && (
        <>
          <AdminTable<ClientTool>
            columns={columns}
            data={tools}
            pagination={pagination}
            onPageChange={(page) => loadTools(page)}
            onPageSizeChange={(newPageSize) => {
              // 更新分页大小
              setPagination((prev) => ({
                ...prev,
                pageSize: newPageSize,
                page: 1,
              }));
              // 注意：不需要手动调用loadTools，因为useEffect会监听pagination.pageSize的变化
            }}
            loading={loading}
          />
        </>
      )}

      <Dialog
        open={open}
        onOpenChange={(isOpen) => {
          if (loading) return;
          setOpen(isOpen);
          if (!isOpen) resetForm();
        }}
      >
        <DialogContent className="sm:max-w-[600px]">
          {" "}
          {/* Adjusted width */}
          <DialogHeader>
            <DialogTitle>{editingTool ? "编辑工具" : "新增工具"}</DialogTitle>
            <DialogDescription>
              {editingTool
                ? `正在编辑工具: ${editingTool.name}`
                : "创建一个新的工具。"}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="name">
                  名称 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, name: e.target.value }))
                  }
                  required
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="categoryId">
                  分类 <span className="text-red-500">*</span>
                </Label>
                <CategorySearchSelect
                  value={formData.categoryId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, categoryId: value }))
                  }
                  categories={categories}
                  placeholder="选择分类"
                  required
                />
              </div>
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="description">
                描述 <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1.5">
                <Label htmlFor="url">
                  工具链接 <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="url"
                  type="url"
                  value={formData.url}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, url: e.target.value }))
                  }
                  required
                />
              </div>
              <div className="space-y-1.5">
                <Label htmlFor="website">官方网站</Label>
                <Input
                  id="website"
                  type="url"
                  value={formData.website}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      website: e.target.value,
                    }))
                  }
                />
              </div>
            </div>

            <div className="space-y-1.5">
              <ImageUpload
                value={formData.iconUrl}
                onChange={(url) => {
                  setFormData((prev) => ({
                    ...prev,
                    // 确保URL是字符串
                    iconUrl: url || "",
                  }));
                }}
                label="工具图标"
                maxSizeMB={8}
                accept="image/png,image/jpeg,image/webp"
              />
              <p className="text-xs text-gray-500 mt-1">
                支持 JPG, PNG, WebP 格式，最大 8MB，建议尺寸为 512x512 像素
              </p>
            </div>

            <div className="space-y-1.5">
              <Label htmlFor="tags">标签 (逗号分隔)</Label>
              <Input
                id="tags"
                value={
                  Array.isArray(formData.tags)
                    ? formData.tags.join(",")
                    : formData.tags
                }
                onChange={handleTagsChange}
              />
            </div>

            <div className="flex items-center space-x-6 pt-2">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isFree"
                  checked={formData.isFree}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, isFree: checked }))
                  }
                />
                <Label htmlFor="isFree">免费</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isFeatured"
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({ ...prev, isFeatured: checked }))
                  }
                />
                <Label htmlFor="isFeatured">推荐</Label>
              </div>
            </div>

            <DialogFooter className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={loading}
              >
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading
                  ? editingTool
                    ? "更新中..."
                    : "创建中..."
                  : editingTool
                  ? "保存更改"
                  : "创建工具"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
