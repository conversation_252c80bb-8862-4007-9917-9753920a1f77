import {
  getPaginatedToolsWithCategory,
  ToolWithCategory,
} from "@/lib/data/tools";
import { getAllCategories, CategoryListItem } from "@/lib/data/categories";
import ToolsClientManagement, {
  ClientTool,
  ClientCategorySimple,
} from "./ToolsClientManagement";

// Helper function to map ToolWithCategory to ClientTool
function mapToClientTool(tool: ToolWithCategory): ClientTool {
  return {
    id: tool.id,
    name: tool.name,
    description: tool.description,
    url: tool.url,
    iconUrl: tool.iconUrl,
    website: tool.website,
    tags: tool.tags,
    isFree: tool.isFree,
    isFeatured: tool.isFeatured,
    categoryId: tool.categoryId, // Already confirmed as string
    categoryName: tool.category?.name, // Optional chaining for safety, though category should exist
    createdAt: tool.createdAt.toISOString(), // Convert Date to string
  };
}

// Helper function to map CategoryListItem to ClientCategorySimple
function mapToClientCategorySimple(
  category: CategoryListItem
): ClientCategorySimple {
  return {
    id: category.id,
    name: category.name,
  };
}

interface SearchParams {
  page?: string;
  pageSize?: string;
  // Add other potential search params if needed for initial load
}

export default async function ToolsPage({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) {
  // 等待 searchParams Promise 解析
  const params = await searchParams;
  const page = params?.page || "1";
  const pageSize = params?.pageSize || "12";
  const currentPage = Math.max(1, Number(page));
  const currentPageSize = Math.max(1, Number(pageSize));

  // Fetch initial data in parallel
  const [{ tools: rawTools, totalCount }, rawCategories] = await Promise.all([
    getPaginatedToolsWithCategory({
      page: currentPage,
      pageSize: currentPageSize,
    }),
    getAllCategories(),
  ]);

  const initialTools: ClientTool[] = rawTools.map(mapToClientTool);
  const initialCategories: ClientCategorySimple[] = rawCategories.map(
    mapToClientCategorySimple
  );

  const totalPages = Math.ceil(totalCount / currentPageSize);

  return (
    <ToolsClientManagement
      initialTools={initialTools}
      initialCategories={initialCategories}
      initialPagination={{
        page: currentPage,
        pageSize: currentPageSize,
        total: totalCount,
        totalPages: totalPages,
        hasNext: currentPage < totalPages,
        hasPrevious: currentPage > 1,
      }}
    />
  );
}
