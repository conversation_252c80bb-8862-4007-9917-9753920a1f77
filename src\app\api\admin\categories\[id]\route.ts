import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  validateAdminPermission,
  successResponse,
  errorResponse,
  badRequestResponse,
  notFoundResponse,
} from "@/lib/api-utils";
import { API_CODES } from "@/lib/enums";
import { z } from "zod";

// 错误码
const ERROR_CODES = {
  CATEGORY_NOT_FOUND: "CATEGORY_NOT_FOUND",
  CATEGORY_NAME_EXISTS: "CATEGORY_NAME_EXISTS",
  CATEGORY_SLUG_EXISTS: "CATEGORY_SLUG_EXISTS",
  CATEGORY_HAS_TOOLS: "CATEGORY_HAS_TOOLS",
  INVALID_INPUT: "INVALID_INPUT",
};

// 请求体验证schema
const updateCategorySchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(50, "分类名称最多50个字符"),
  slug: z.string().min(1, "Slug不能为空"),
  description: z.string().optional(),
  icon: z.string().optional(),
  parentId: z.string().nullable().optional(),
});

// PUT /api/admin/categories/[id] - 更新分类
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await validateAdminPermission();

    const params = await context.params;
    const { id } = params;
    const body = await request.json();

    // 验证请求体
    let validatedData;
    try {
      validatedData = updateCategorySchema.parse(body);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return badRequestResponse(error.errors[0].message);
      }
      throw error;
    }

    // 检查分类是否存在
    const existingCategory = await prisma.category.findUnique({
      where: { id },
    });

    if (!existingCategory) {
      return notFoundResponse("分类不存在");
    }

    // 如果更改了名称或slug，检查是否已被使用
    if (
      validatedData.name !== existingCategory.name ||
      validatedData.slug !== existingCategory.slug
    ) {
      // 检查名称是否已被使用
      if (validatedData.name !== existingCategory.name) {
        const nameExists = await prisma.category.findFirst({
          where: {
            name: validatedData.name,
            id: { not: id },
          },
        });

        if (nameExists) {
          return errorResponse(
            "分类名称已存在",
            API_CODES.RESOURCE_EXISTS.code
          );
        }
      }

      // 检查slug是否已被使用
      if (validatedData.slug !== existingCategory.slug) {
        const slugExists = await prisma.category.findFirst({
          where: {
            slug: validatedData.slug,
            id: { not: id },
          },
        });

        if (slugExists) {
          return errorResponse(
            "Slug 已被使用",
            API_CODES.RESOURCE_EXISTS.code as keyof typeof API_CODES
          );
        }
      }
    }

    // 更新分类
    const category = await prisma.category.update({
      where: { id },
      data: validatedData,
    });

    return successResponse(category, "分类更新成功");
  } catch (error) {
    console.error("更新分类时出错:", error);
    return errorResponse(
      error as Error,
      "INTERNAL_ERROR",
      "更新分类失败，请稍后重试"
    );
  }
}

// DELETE /api/admin/categories/[id] - 删除分类
export async function DELETE(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    await validateAdminPermission();

    const params = await context.params;
    const { id } = params;

    // 检查分类是否存在
    const category = await prisma.category.findUnique({
      where: { id },
      include: {
        tools: true, // 包含关联的工具，用于检查是否可以删除
      },
    });

    if (!category) {
      return notFoundResponse("分类不存在");
    }

    // 检查分类是否还有关联的工具
    if (category.tools.length > 0) {
      return errorResponse(
        "该分类下存在工具，无法删除",
        API_CODES.OPERATION_FAILED.code as keyof typeof API_CODES
      );
    }

    // 删除分类
    await prisma.category.delete({
      where: { id },
    });

    return successResponse({ success: true }, "分类删除成功");
  } catch (error) {
    console.error("删除分类时出错:", error);
    return errorResponse(
      error as Error,
      "INTERNAL_ERROR",
      "删除分类失败，请稍后重试"
    );
  }
}
