import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  validateAdminPermission,
  successResponse,
  errorResponse,
  validationErrorResponse,
} from "@/lib/api-utils";
import { z } from "zod";
import { Prisma } from "@prisma/client";

export const revalidate = 0; // 禁用缓存，确保实时数据

// 请求体验证schema
const categorySchema = z.object({
  name: z.string().min(1, "分类名称不能为空").max(50, "分类名称最多50个字符"),
  slug: z.string().min(1, "Slug不能为空"),
  description: z.string().optional(),
  icon: z.string().optional(),
  parentId: z.string().nullable().optional(),
});

// 批量更新验证schema
const updateOrderSchema = z.object({
  categories: z.array(
    z.object({
      id: z.string(),
      parentId: z.string().nullable().optional(),
      order: z.number().optional(),
    })
  ),
});

// GET /api/admin/categories - 获取分类列表
export async function GET() {
  try {
    await validateAdminPermission();

    const categories = await prisma.category.findMany({
      orderBy: [
        {
          order: "asc",
        },
        {
          createdAt: "desc",
        },
      ],
    });

    return successResponse(categories);
  } catch (error) {
    return errorResponse(error as Error);
  }
}

// POST /api/admin/categories - 创建新分类
export async function POST(request: NextRequest) {
  try {
    await validateAdminPermission();

    const body = await request.json();

    // 验证请求体
    const validatedData = categorySchema.parse(body);

    // 检查分类名是否已存在
    const existingCategory = await prisma.category.findFirst({
      where: {
        OR: [{ name: validatedData.name }, { slug: validatedData.slug }],
      },
    });

    if (existingCategory) {
      if (existingCategory.name === validatedData.name) {
        throw new Error("分类名称已存在");
      }
      if (existingCategory.slug === validatedData.slug) {
        throw new Error("Slug已存在");
      }
    }

    // 使用事务获取最大order并创建新分类
    const category = await prisma.$transaction(
      async (tx: Prisma.TransactionClient) => {
        const maxOrderResult = await tx.$queryRaw<{ max_order: number }[]>`
        SELECT COALESCE(MAX("order"), 0) as max_order FROM "Category"
      `;

        return await tx.category.create({
          data: {
            name: validatedData.name,
            slug: validatedData.slug,
            description: validatedData.description,
            icon: validatedData.icon,
            parentId: validatedData.parentId,
            order: maxOrderResult[0].max_order + 1,
          },
        });
      }
    );

    return successResponse(category);
  } catch (error) {
    if (error instanceof z.ZodError) {
      // 使用专门的验证错误响应函数
      return validationErrorResponse(error.errors[0].message);
    }
    return errorResponse(error as Error);
  }
}

// PUT /api/admin/categories - 批量更新分类顺序或单个分类信息
export async function PUT(request: NextRequest) {
  try {
    await validateAdminPermission();

    const body = await request.json();

    // 检查是否是批量更新分类顺序的请求
    if ("categories" in body) {
      const { categories } = updateOrderSchema.parse(body);

      // 为了保证更新的原子性，使用事务
      await prisma.$transaction(async (tx: Prisma.TransactionClient) => {
        // 批量更新分类的父级关系和排序
        for (const category of categories) {
          await tx.category.update({
            where: { id: category.id },
            data: {
              parentId: category.parentId,
              order: category.order || 0,
            },
          });
        }
      });

      return successResponse({ message: "分类顺序更新成功" });
    }

    // 单个分类更新处理
    const validatedData = categorySchema.parse(body);

    if (!body.id) {
      throw new Error("更新分类时必须提供id");
    }

    // 检查分类名和slug是否已存在(排除当前记录)
    const existingCategory = await prisma.category.findFirst({
      where: {
        OR: [{ name: validatedData.name }, { slug: validatedData.slug }],
        NOT: {
          id: body.id,
        },
      },
    });

    if (existingCategory) {
      if (existingCategory.name === validatedData.name) {
        throw new Error("分类名称已存在");
      }
      if (existingCategory.slug === validatedData.slug) {
        throw new Error("Slug已存在");
      }
    }

    const updatedCategory = await prisma.category.update({
      where: { id: body.id },
      data: {
        name: validatedData.name,
        slug: validatedData.slug,
        description: validatedData.description,
        icon: validatedData.icon,
        parentId: validatedData.parentId,
      },
    });

    return successResponse(updatedCategory);
  } catch (error) {
    if (error instanceof z.ZodError) {
      // 使用专门的验证错误响应函数
      return validationErrorResponse(error.errors[0].message);
    }
    return errorResponse(error as Error);
  }
}
