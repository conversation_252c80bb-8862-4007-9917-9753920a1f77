import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { TIME, DATABASE } from "@/lib/constants/app.constants";

export const revalidate = 600; // 数据每 10 分钟重新验证一次

export async function GET() {
  console.log(
    `API /api/admin/dashboard/stats called at ${new Date().toISOString()}`
  );
  try {
    const now = new Date();
    const lastMonth = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate() - TIME.RECENT_TOOLS_DAYS
    );

    // 获取统计数据
    const [categoriesCount, toolsCount, recentToolsCount, totalViews] =
      await Promise.all([
        prisma.category.count(),
        prisma.tool.count(),
        prisma.tool.count({
          where: {
            createdAt: {
              gte: lastMonth,
            },
          },
        }),
        prisma.tool.aggregate({
          _sum: {
            views: true,
          },
        }),
      ]);

    // 获取趋势数据
    const categoryWithTools = await prisma.category.findMany({
      include: {
        _count: {
          select: { tools: true },
        },
      },
      orderBy: {
        tools: {
          _count: DATABASE.ORDER_BY.CREATED_DESC, // 按工具数量排序
        },
      },
    });

    const distribution = categoryWithTools.map((category: any) => ({
      name: category.name,
      count: category._count.tools,
    }));

    const responseData = {
      stats: {
        categoriesCount,
        toolsCount,
        recentToolsCount,
        totalViews: totalViews._sum.views || 0,
      },
      distribution,
    };

    console.log(
      `API dashboard stats response:`,
      `Stats: ${JSON.stringify(responseData.stats)}`
    );

    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Dashboard stats error:", error);
    return NextResponse.json({ error: "获取统计数据失败" }, { status: 500 });
  }
}
