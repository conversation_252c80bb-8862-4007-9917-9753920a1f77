import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  validateAdminPermission,
  successResponse,
  errorResponse,
} from "@/lib/api-utils";
import { z } from "zod";
import { Prisma } from "@prisma/client";
import { DATABASE } from "@/lib/constants/app.constants";

// Pagination and search query validation schema
const querySchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default("1"),
  pageSize: z.string().regex(/^\d+$/).transform(Number).default("10"),
  search: z.string().optional(),
});

export const revalidate = 0; // No caching for this route

/**
 * @swagger
 * /api/admin/tool-views:
 *   get:
 *     summary: Get tool view statistics
 *     description: Retrieves a paginated list of tool views, including view counts and last viewed dates, with support for searching by tool name.
 *     tags: [Admin]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: The page number for pagination.
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: The number of items per page.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: A search term to filter tool views by tool name.
 *     responses:
 *       200:
 *         description: A successful response with the list of tool views and pagination details.
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ToolViewListResponse'
 *       400:
 *         description: Invalid query parameters.
 *       401:
 *         description: Unauthorized.
 *       403:
 *         description: Forbidden.
 */
export async function GET(request: NextRequest) {
  try {
    await validateAdminPermission();

    const { searchParams } = new URL(request.url);
    const { page, pageSize, search } = querySchema.parse(
      Object.fromEntries(searchParams.entries())
    );

    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: Prisma.ToolWhereInput = {};
    if (search) {
      where.name = {
        contains: search,
        mode: DATABASE.QUERY_MODE.INSENSITIVE,
      };
    }

    // First, get the list of tools that match the search criteria
    const tools = await prisma.tool.findMany({
      where,
      select: { id: true },
    });

    const toolIds = tools.map((tool) => tool.id);

    if (search && toolIds.length === 0) {
      // If search is active and no tools are found, return empty result
      return successResponse({
        data: [],
        pagination: {
          page,
          pageSize,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrevious: false,
        },
      });
    }

    // Then, aggregate the view data for those tools
    const viewAggregations = await prisma.toolView.groupBy({
      by: ["toolId"],
      where: {
        toolId: {
          in: toolIds.length > 0 ? toolIds : undefined, // Filter by toolIds if search is active
        },
      },
      _count: {
        id: true,
      },
      _max: {
        viewedAt: true,
      },
      orderBy: {
        _max: {
          viewedAt: "desc",
        },
      },
      skip,
      take,
    });

    // Get the total count of grouped views for pagination
    const totalResult = await prisma.toolView.groupBy({
      by: ["toolId"],
      where: {
        toolId: {
          in: toolIds.length > 0 ? toolIds : undefined,
        },
      },
    });
    const total = totalResult.length;

    // Get the full tool details for the aggregated views
    const aggregatedToolIds = viewAggregations.map((agg) => agg.toolId);
    const toolDetails = await prisma.tool.findMany({
      where: {
        id: {
          in: aggregatedToolIds,
        },
      },
    });

    const toolMap = new Map(toolDetails.map((tool) => [tool.id, tool]));

    const data = viewAggregations.map((agg) => ({
      tool: toolMap.get(agg.toolId),
      viewCount: agg._count.id,
      lastViewed: agg._max.viewedAt,
    }));

    const totalPages = Math.ceil(total / pageSize);

    return successResponse({
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      },
    });
  } catch (error) {
    console.error("[API_ERROR] /api/admin/tool-views GET:", error);
    return errorResponse(error as Error);
  }
}
