import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  validateAdminPermission,
  successResponse,
  errorResponse,
  validationErrorResponse,
} from "@/lib/api-utils";
import { z } from "zod";

// 请求体验证schema
const updateToolSchema = z.object({
  name: z.string().min(1, "工具名称不能为空").max(50, "工具名称最多50个字符"),
  description: z.string().min(1, "工具描述不能为空"),
  url: z.string().url("请输入有效的URL"),
  iconUrl: z
    .string()
    .min(1, "图标路径不能为空")
    .or(z.literal("").transform(() => null))
    .nullable()
    .optional(),
  website: z
    .string()
    .url("请输入有效的网站URL")
    .or(z.literal("").transform(() => null))
    .nullable()
    .optional(),
  tags: z.array(z.string()).default([]),
  isFree: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  categoryId: z.string().min(1, "请选择分类").nullable().optional(),
});

// PUT /api/admin/tools/[id] - 更新工具
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    console.log(`[TOOLS_API] 开始更新工具: ${JSON.stringify(context.params)}`);
    await validateAdminPermission();

    const params = await context.params;
    const { id } = params;
    console.log(`[TOOLS_API] 工具ID: ${id}`);

    const body = await request.json();
    console.log("[TOOLS_API] 请求体:", JSON.stringify(body, null, 2));

    try {
      // 验证请求体
      const validatedData = updateToolSchema.parse(body);
      console.log(
        "[TOOLS_API] 验证后的数据:",
        JSON.stringify(validatedData, null, 2)
      );

      // 检查工具是否存在
      const existingTool = await prisma.tool.findUnique({
        where: { id },
      });

      if (!existingTool) {
        console.error(`[TOOLS_API] 错误: 工具不存在, ID: ${id}`);
        throw new Error("工具不存在");
      }

      // 检查分类是否存在
      if (validatedData.categoryId) {
        const category = await prisma.category.findUnique({
          where: { id: validatedData.categoryId },
        });

        if (!category) {
          console.error(
            `[TOOLS_API] 错误: 分类不存在, ID: ${validatedData.categoryId}`
          );
          throw new Error("所选分类不存在");
        }
      }

      // 准备更新数据
      const updateData = {
        ...validatedData,
        iconUrl: validatedData.iconUrl || null,
        website: validatedData.website || null,
      };

      console.log("[TOOLS_API] 更新数据:", JSON.stringify(updateData, null, 2));

      // 更新工具
      const tool = await prisma.tool.update({
        where: { id },
        data: updateData,
        include: {
          category: true,
        },
      });

      console.log(`[TOOLS_API] 工具更新成功: ${id}`);
      return successResponse(tool);
    } catch (validationError) {
      console.error("[TOOLS_API] 验证或处理错误:", validationError);
      throw validationError;
    }
  } catch (error) {
    console.error("[TOOLS_API] 更新工具时出错:", error);
    if (error instanceof z.ZodError) {
      const errorMessage = error.errors[0]?.message || "未知验证错误";
      console.error(
        `[TOOLS_API] 验证错误详情: ${JSON.stringify(error.errors, null, 2)}`
      );
      // 使用专门的验证错误响应函数
      return validationErrorResponse(errorMessage);
    }
    return errorResponse(error as Error);
  }
}

// DELETE /api/admin/tools/[id] - 删除工具
export async function DELETE(
  _request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    console.log(`[TOOLS_API] 开始删除工具: ${JSON.stringify(context.params)}`);
    await validateAdminPermission();

    const params = await context.params;
    const { id } = params;
    console.log(`[TOOLS_API] 要删除的工具ID: ${id}`);

    // 检查工具是否存在
    const tool = await prisma.tool.findUnique({
      where: { id },
    });

    if (!tool) {
      console.error(`[TOOLS_API] 错误: 要删除的工具不存在, ID: ${id}`);
      throw new Error("工具不存在");
    }

    // 删除工具
    await prisma.tool.delete({
      where: { id },
    });

    console.log(`[TOOLS_API] 工具删除成功: ${id}`);
    return successResponse({ message: "工具已删除" });
  } catch (error) {
    console.error(`[TOOLS_API] 删除工具时出错:`, error);
    return errorResponse(error as Error);
  }
}
