import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  validateAdminPermission,
  successResponse,
  errorResponse,
  validationErrorResponse,
} from "@/lib/api-utils";
import { z } from "zod";
import { Prisma } from "@prisma/client";
import { DATABASE } from "@/lib/constants/app.constants";

// 请求体验证schema
const toolSchema = z.object({
  name: z.string().min(1, "工具名称不能为空").max(50, "工具名称最多50个字符"),
  description: z.string().min(1, "工具描述不能为空"),
  url: z.string().url("请输入有效的URL"),
  iconUrl: z
    .string()
    .optional()
    .transform((val) => (val === "" || val === undefined ? undefined : val)),
  website: z
    .string()
    .optional()
    .transform((val) => (val === "" || val === undefined ? undefined : val))
    .refine(
      (val) => val === undefined || z.string().url().safeParse(val).success,
      {
        message: "请输入有效的网站URL",
      }
    ),
  tags: z.array(z.string()).default([]),
  isFree: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  categoryId: z.string().min(1, "请选择分类"),
});

export const revalidate = 300; // Revalidate every 5 minutes

// 分页查询参数验证schema
const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/).transform(Number).default("1"),
  pageSize: z.string().regex(/^\d+$/).transform(Number).default("10"),
  search: z.string().optional(),
  categoryId: z.string().optional(),
  isFeatured: z
    .enum(["true", "false"])
    .transform((val) => val === "true")
    .optional(),
});

// GET /api/admin/tools - 获取工具列表（支持分页）
export async function GET(request: NextRequest) {
  try {
    await validateAdminPermission();

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const { page, pageSize, search, categoryId, isFeatured } =
      paginationSchema.parse(Object.fromEntries(searchParams.entries()));

    // 计算跳过的记录数
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    // 构建查询条件
    const where: Prisma.ToolWhereInput = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: DATABASE.QUERY_MODE.INSENSITIVE } },
        {
          description: {
            contains: search,
            mode: DATABASE.QUERY_MODE.INSENSITIVE,
          },
        },
        { tags: { hasSome: [search] } },
      ];
    }

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (isFeatured !== undefined) {
      where.isFeatured = isFeatured;
    }

    // 获取总数和分页数据
    const [total, tools] = await Promise.all([
      prisma.tool.count({ where }),
      prisma.tool.findMany({
        where,
        include: {
          category: true,
        },
        orderBy: {
          createdAt: DATABASE.ORDER_BY.CREATED_DESC,
        },
        skip,
        take,
      }),
    ]);

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    return successResponse({
      data: tools,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      },
    });
  } catch (error) {
    console.error("[API_ERROR] /api/admin/tools GET:", error); // 添加日志
    return errorResponse(error as Error);
  }
}

// POST /api/admin/tools - 创建新工具
export async function POST(request: NextRequest) {
  try {
    await validateAdminPermission();

    const body = await request.json();

    // 验证请求体
    const validatedData = toolSchema.parse(body);

    // 检查分类是否存在
    const category = await prisma.category.findUnique({
      where: { id: validatedData.categoryId },
    });

    if (!category) {
      throw new Error("所选分类不存在");
    }

    // 创建新工具
    const tool = await prisma.tool.create({
      data: validatedData,
      include: {
        category: true,
      },
    });

    return successResponse(tool);
  } catch (error) {
    console.error("[API_ERROR] /api/admin/tools POST:", error); // 添加日志
    if (error instanceof z.ZodError) {
      // 使用专门的验证错误响应函数
      return validationErrorResponse(error.errors[0].message);
    }
    return errorResponse(error as Error);
  }
}
