import { NextResponse } from "next/server";
import { readdir, stat, access, constants } from "fs/promises";
import { join } from "path";

/**
 * 调试 API：检查上传目录状态和静态文件访问
 */
export async function GET() {
  try {
    const uploadsDir = join(process.cwd(), "public", "uploads");

    console.log(`[DEBUG] 当前工作目录: ${process.cwd()}`);
    console.log(`[DEBUG] 上传目录路径: ${uploadsDir}`);

    // 检查目录是否存在
    let dirExists = false;
    let dirAccessible = false;
    let files: string[] = [];
    let dirStats: any = null;
    let fileDetails: any[] = [];

    try {
      await access(uploadsDir, constants.F_OK);
      dirExists = true;
      console.log(`[DEBUG] 目录存在: ${uploadsDir}`);

      try {
        await access(uploadsDir, constants.R_OK | constants.W_OK);
        dirAccessible = true;
        console.log(`[DEBUG] 目录可读写: ${uploadsDir}`);

        // 读取目录内容
        files = await readdir(uploadsDir);
        console.log(`[DEBUG] 目录文件列表:`, files);

        // 获取目录统计信息
        dirStats = await stat(uploadsDir);

        // 获取每个文件的详细信息
        for (const file of files) {
          try {
            const filePath = join(uploadsDir, file);
            const fileStats = await stat(filePath);
            fileDetails.push({
              name: file,
              path: filePath,
              size: fileStats.size,
              mtime: fileStats.mtime,
              mode: fileStats.mode.toString(8),
              isFile: fileStats.isFile(),
              isDirectory: fileStats.isDirectory(),
            });
          } catch (fileError) {
            fileDetails.push({
              name: file,
              error:
                fileError instanceof Error ? fileError.message : "未知错误",
            });
          }
        }
      } catch (error) {
        console.log(`[DEBUG] 目录权限错误:`, error);
      }
    } catch (error) {
      console.log(`[DEBUG] 目录不存在:`, error);
    }

    // 检查特定文件
    const testFile = "98c8a23c7e653dca.png";
    const testFilePath = join(uploadsDir, testFile);
    let fileExists = false;
    let fileStats: any = null;

    try {
      await access(testFilePath, constants.F_OK | constants.R_OK);
      fileExists = true;
      fileStats = await stat(testFilePath);
      console.log(`[DEBUG] 测试文件存在: ${testFilePath}`);
    } catch (error) {
      console.log(`[DEBUG] 测试文件不存在: ${testFilePath}`, error);
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      workingDirectory: process.cwd(),
      uploadsDirectory: uploadsDir,
      directory: {
        exists: dirExists,
        accessible: dirAccessible,
        stats: dirStats
          ? {
              mode: dirStats.mode.toString(8),
              uid: dirStats.uid,
              gid: dirStats.gid,
              size: dirStats.size,
              mtime: dirStats.mtime,
            }
          : null,
        files: files,
        fileCount: files.length,
        fileDetails: fileDetails,
      },
      testFile: {
        name: testFile,
        path: testFilePath,
        exists: fileExists,
        stats: fileStats
          ? {
              mode: fileStats.mode.toString(8),
              uid: fileStats.uid,
              gid: fileStats.gid,
              size: fileStats.size,
              mtime: fileStats.mtime,
            }
          : null,
      },
      process: {
        uid: process.getuid?.(),
        gid: process.getgid?.(),
        platform: process.platform,
        version: process.version,
      },
    };

    return NextResponse.json(debugInfo, { status: 200 });
  } catch (error) {
    console.error(`[DEBUG] 调试 API 错误:`, error);
    return NextResponse.json(
      {
        error: "调试失败",
        message: error instanceof Error ? error.message : "未知错误",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
