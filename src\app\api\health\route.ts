import { NextResponse } from "next/server";
import { access, constants } from "fs/promises";
import { join } from "path";

/**
 * 健康检查 API 端点
 * 用于 Docker 容器健康检查和负载均衡器探测
 */
export async function GET() {
  try {
    // 检查上传目录是否存在且可访问
    const uploadsDir = join(process.cwd(), "public", "uploads");
    let uploadsStatus = "ok";

    try {
      await access(
        uploadsDir,
        constants.F_OK | constants.R_OK | constants.W_OK
      );
    } catch {
      uploadsStatus = "error: uploads directory not accessible";
    }

    // 检查基本服务状态
    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || "0.1.0",
      uploads: uploadsStatus,
    };

    return NextResponse.json(healthStatus, { status: 200 });
  } catch (error) {
    console.log("=============== unhealthy ===============");
    console.log(error);
    // 如果出现错误，返回不健康状态
    const errorStatus = {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : "Unknown error",
    };

    return NextResponse.json(errorStatus, { status: 503 });
  }
}

/**
 * 支持 HEAD 请求，用于简单的健康检查
 */
export async function HEAD() {
  try {
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
