import { NextResponse } from "next/server";
import { getMenuItems } from "@/lib/menu-utils";

/**
 * GET handler for fetching hierarchical menu items.
 * @async
 * @param {Request} _request - The incoming request object.
 * @returns {Promise<NextResponse>} A promise that resolves to the Next.js response.
 */
export async function GET(_request: Request): Promise<NextResponse> {
  try {
    const menuItems = await getMenuItems();
    return NextResponse.json(menuItems);
  } catch (error) {
    // The error is already logged in getMenuItems
    // We can customize the response further if needed
    return NextResponse.json(
      { message: "Failed to fetch menu items due to an internal error." },
      { status: 500 }
    );
  }
}
