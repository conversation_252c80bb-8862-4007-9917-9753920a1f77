import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import redis from "@/lib/redis";
import { getClientIp } from "request-ip";

const BOT_UA_PATTERNS = [
  /bot/i,
  /spider/i,
  /crawler/i,
  /slurp/i,
  /googlebot/i,
  /bingbot/i,
  /yandexbot/i,
  /duckduckbot/i,
  /baiduspider/i,
  /sogou/i,
  /360spider/i,
  /bytespider/i,
  /headless/i,
];

const isBot = (userAgent: string | null): boolean => {
  if (!userAgent) {
    return false;
  }
  return BOT_UA_PATTERNS.some((pattern) => pattern.test(userAgent));
};

/**
 * POST /api/tools/[id]/view
 * 增加工具的浏览次数
 *
 * @param request - Next.js请求对象
 * @param context - 路由参数上下文
 * @returns 更新后的浏览次数
 */
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const { id } = params;
    const ip =
      process.env.NODE_ENV === "development"
        ? "127.0.0.1"
        : getClientIp(request as any);
    const userAgent = request.headers.get("user-agent");

    if (isBot(userAgent)) {
      return NextResponse.json({ success: true, message: "Bot view ignored" });
    }

    if (!id) {
      return NextResponse.json({ error: "工具ID不能为空" }, { status: 400 });
    }

    if (!ip) {
      return NextResponse.json({ error: "无法获取IP地址" }, { status: 400 });
    }

    const redisKey = `tool-view:${id}:${ip}`;
    const viewRecord = await redis.get(redisKey);

    if (viewRecord) {
      return NextResponse.json({ success: true, message: "重复浏览" });
    }

    const [tool, _] = await Promise.all([
      prisma.tool.update({
        where: { id },
        data: {
          views: { increment: 1 },
          lastViewedAt: new Date(),
        },
        select: { views: true },
      }),
      prisma.toolView.create({
        data: {
          toolId: id,
          ipAddress: ip,
          userAgent: userAgent || "Unknown",
        },
      }),
      redis.set(redisKey, "1", "EX", 60 * 5), // 5分钟内同一IP不重复计数
    ]);

    return NextResponse.json({
      success: true,
      views: tool.views,
    });
  } catch (error) {
    console.error("Error incrementing tool views:", error);
    return NextResponse.json({ error: "服务器内部错误" }, { status: 500 });
  }
}

/**
 * 处理不支持的HTTP方法
 */
export async function GET() {
  return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ error: "Method Not Allowed" }, { status: 405 });
}
