import { NextRequest, NextResponse } from "next/server";
import { getPaginatedRecommendedTools } from "@/lib/data/tools";
import { PAGINATION } from "@/lib/constants/app.constants";

/**
 * GET /api/tools/recommended
 * 获取推荐工具列表（分页）
 * 
 * Query Parameters:
 * - page: 页码（可选，默认为1）
 * - pageSize: 每页大小（可选，默认为10）
 * 
 * @param request - Next.js请求对象
 * @returns 推荐工具列表和分页信息
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // 解析查询参数
    const pageParam = searchParams.get("page");
    const pageSizeParam = searchParams.get("pageSize");
    
    // 验证和转换参数
    const page = pageParam ? Math.max(1, parseInt(pageParam, 10)) : PAGINATION.DEFAULT_PAGE;
    const pageSize = pageSizeParam 
      ? Math.min(Math.max(1, parseInt(pageSizeParam, 10)), PAGINATION.MAX_PAGE_SIZE)
      : PAGINATION.RECOMMENDED_PAGE_SIZE;
    
    // 验证参数有效性
    if (isNaN(page) || isNaN(pageSize)) {
      return NextResponse.json(
        { 
          error: "Invalid parameters", 
          message: "Page and pageSize must be valid numbers" 
        },
        { status: 400 }
      );
    }
    
    // 获取推荐工具数据
    const result = await getPaginatedRecommendedTools({
      page,
      pageSize,
    });
    
    // 计算分页信息
    const totalPages = Math.ceil(result.totalCount / pageSize);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;
    
    // 返回结果
    return NextResponse.json({
      tools: result.tools,
      totalCount: result.totalCount,
      pagination: {
        currentPage: page,
        pageSize,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    });
    
  } catch (error) {
    console.error("Error in GET /api/tools/recommended:", error);
    
    return NextResponse.json(
      { 
        error: "Internal Server Error", 
        message: "Failed to fetch recommended tools" 
      },
      { status: 500 }
    );
  }
}

/**
 * 处理不支持的HTTP方法
 * @param request - Next.js请求对象
 * @returns 405 Method Not Allowed响应
 */
export async function POST() {
  return NextResponse.json(
    { error: "Method Not Allowed" },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: "Method Not Allowed" },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: "Method Not Allowed" },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: "Method Not Allowed" },
    { status: 405 }
  );
}
