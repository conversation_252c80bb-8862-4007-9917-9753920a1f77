import { NextResponse } from "next/server";
import { writeFile, mkdir, access } from "fs/promises";
import { join, extname } from "path";
import { constants } from "fs";
import { calculateServerFileHash } from "@/lib/utils/hash.utils";

/**
 * 检查文件是否已存在
 * @param filePath - 文件路径
 * @returns 文件是否存在
 */
async function fileExists(filePath: string): Promise<boolean> {
  try {
    await access(filePath, constants.F_OK);
    return true;
  } catch {
    return false;
  }
}

/**
 * 确保上传目录存在
 * @param dirPath - 目录路径
 */
async function ensureUploadDir(dirPath: string): Promise<void> {
  try {
    await access(dirPath, constants.F_OK | constants.W_OK);
    console.log(`[UPLOAD] 上传目录已存在且可写: ${dirPath}`);
  } catch (error) {
    console.log(`[UPLOAD] 创建上传目录: ${dirPath}`);
    try {
      await mkdir(dirPath, { recursive: true });
      console.log(`[UPLOAD] 上传目录创建成功: ${dirPath}`);
    } catch (mkdirError) {
      console.error(`[UPLOAD] 创建上传目录失败: ${dirPath}`, mkdirError);
      throw mkdirError;
    }
  }
}

/**
 * 处理文件上传的POST请求
 * @param request - 请求对象
 * @returns 上传结果
 */
export async function POST(request: Request) {
  try {
    console.log(`[UPLOAD] 开始处理文件上传请求`);
    const uploadDir = join(process.cwd(), "public/uploads");
    console.log(`[UPLOAD] 上传目录路径: ${uploadDir}`);
    console.log(`[UPLOAD] 当前工作目录: ${process.cwd()}`);

    // 确保上传目录存在
    await ensureUploadDir(uploadDir);

    const formData = await request.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      console.log(`[UPLOAD] 错误: 没有上传文件`);
      return NextResponse.json(
        { success: false, message: "没有上传文件" },
        { status: 400 }
      );
    }

    console.log(
      `[UPLOAD] 文件信息: 名称=${file.name}, 大小=${file.size}, 类型=${file.type}`
    );

    // 验证文件类型
    const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, message: "不支持的图片格式" },
        { status: 400 }
      );
    }

    // 验证文件大小（最大8MB）
    const maxSize = 8 * 1024 * 1024; // 8MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { success: false, message: "图片大小不能超过8MB" },
        { status: 400 }
      );
    }

    // 读取文件内容并计算hash
    console.log(`[UPLOAD] 开始读取文件内容并计算hash`);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    // 计算文件hash作为文件名
    const fileHash = calculateServerFileHash(buffer);
    const fileExt = extname(file.name).toLowerCase();
    const fileName = `${fileHash}${fileExt}`;
    const filePath = join(uploadDir, fileName);
    const publicUrl = `/uploads/${fileName}`;

    console.log(`[UPLOAD] 文件hash: ${fileHash}, 文件名: ${fileName}`);

    // 检查文件是否已存在（去重）
    const exists = await fileExists(filePath);
    if (exists) {
      console.log(`[UPLOAD] 文件已存在，直接返回URL: ${publicUrl}`);
      return NextResponse.json({
        success: true,
        url: publicUrl,
        fileName,
        message: "上传成功",
        isDuplicate: true,
      });
    }

    // 文件不存在，保存新文件
    console.log(`[UPLOAD] 开始保存文件到: ${filePath}`);
    try {
      await writeFile(filePath, buffer);
      console.log(`[UPLOAD] 文件保存成功: ${filePath}`);
    } catch (writeError) {
      console.error(`[UPLOAD] 文件写入失败: ${filePath}`, writeError);
      throw writeError;
    }

    console.log(`[UPLOAD] 上传成功，返回URL: ${publicUrl}`);
    return NextResponse.json({
      success: true,
      url: publicUrl,
      fileName,
      message: "上传成功",
      isDuplicate: false,
    });
  } catch (error) {
    console.error("[UPLOAD] 文件上传错误:", error);

    // 提供更详细的错误信息
    let errorMessage = "文件上传失败";
    if (error instanceof Error) {
      console.error("[UPLOAD] 错误详情:", {
        name: error.name,
        message: error.message,
        stack: error.stack,
      });

      // 根据错误类型提供更具体的错误信息
      if (
        error.message.includes("EACCES") ||
        error.message.includes("permission")
      ) {
        errorMessage = "文件上传失败：权限不足";
      } else if (error.message.includes("ENOSPC")) {
        errorMessage = "文件上传失败：磁盘空间不足";
      } else if (error.message.includes("ENOENT")) {
        errorMessage = "文件上传失败：目录不存在";
      }
    }

    return NextResponse.json(
      { success: false, message: errorMessage },
      { status: 500 }
    );
  }
}
