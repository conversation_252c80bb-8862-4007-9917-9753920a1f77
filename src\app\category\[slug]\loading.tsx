import { Skeleton } from "@/components/ui/skeleton";
import { CSS_CLASSES } from "@/lib/constants/app.constants";
import { RESPONSIVE_GRID } from "@/lib/utils/responsive.utils";

/**
 * 分类页面加载状态
 */
export default function CategoryLoading() {
  return (
    <div className={CSS_CLASSES.CONTAINER.WITH_PADDING}>
      <div className={CSS_CLASSES.SPACING.PAGE_Y}>
        {/* 面包屑加载状态 */}
        <div className="flex items-center gap-2 mb-6">
          <Skeleton className="h-4 w-12" />
          <span className="text-muted-foreground">/</span>
          <Skeleton className="h-4 w-20" />
        </div>

        {/* 标题和描述加载状态 */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Skeleton className="h-8 w-32" />
            <Skeleton className="h-6 w-16" />
          </div>
          <Skeleton className="h-4 w-96 max-w-full" />
        </div>

        {/* 工具列表加载状态 */}
        <div className={RESPONSIVE_GRID.TOOL_CARDS}>
          {[...Array(12)].map((_, i) => (
            <div key={i} className="space-y-3">
              <Skeleton className="h-32 w-full rounded-lg" />
            </div>
          ))}
        </div>

        {/* 分页加载状态 */}
        <div className="flex items-center justify-center gap-2 mt-8">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-9" />
          <Skeleton className="h-9 w-9" />
          <Skeleton className="h-9 w-9" />
          <Skeleton className="h-9 w-20" />
        </div>

        {/* 分页信息加载状态 */}
        <div className="text-center mt-4">
          <Skeleton className="h-4 w-48 mx-auto" />
        </div>
      </div>
    </div>
  );
}
