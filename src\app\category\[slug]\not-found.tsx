import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ROUTES } from "@/lib/constants/app.constants";

/**
 * 分类未找到页面
 */
export default function CategoryNotFound() {
  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <div className="max-w-md mx-auto">
        <h1 className="text-4xl font-bold mb-4">404</h1>
        <h2 className="text-xl font-semibold mb-4">分类未找到</h2>
        <p className="text-muted-foreground mb-8">
          抱歉，您访问的分类页面不存在或已被删除。
        </p>
        <div className="space-y-4">
          <Link href={ROUTES.HOME}>
            <Button className="w-full">
              返回首页
            </Button>
          </Link>
          <Link href={ROUTES.SEARCH}>
            <Button variant="outline" className="w-full">
              搜索工具
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
