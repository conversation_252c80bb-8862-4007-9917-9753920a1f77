import { notFound } from "next/navigation";
import { Suspense } from "react";
import Link from "next/link";
import {
  getPaginatedToolsWithCategory,
  getCategoryBySlug,
  getToolsCountByCategory,
  type ToolWithCategory,
} from "@/lib/data/tools";
import ToolCardHorizontal from "@/components/ui/ToolCardHorizontal";
import Breadcrumbs from "@/components/Breadcrumbs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import {
  PAGINATION,
  MESSAGES,
  CSS_CLASSES,
  ROUTES,
} from "@/lib/constants/app.constants";
import { transformToExtendedTool } from "@/lib/utils/data-transform.utils";
import { RESPONSIVE_GRID } from "@/lib/utils/responsive.utils";
import { generateSeoMetadata } from "@/lib/utils/seo.utils";
import {
  parsePaginationParams,
  buildPaginationQuery,
  generatePageNumbers,
} from "@/lib/utils/pagination.utils";
import { CategoryPaginationControls } from "@/components/CategoryPaginationControls";

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
  searchParams: Promise<{
    page?: string;
    pageSize?: string;
  }>;
}

/**
 * 分页导航组件
 */
function PaginationNav({
  currentPage,
  totalPages,
  categorySlug,
  pageSize,
}: {
  currentPage: number;
  totalPages: number;
  categorySlug: string;
  pageSize: number;
}) {
  if (totalPages <= 1) return null;

  const getPageUrl = (page: number) => {
    return `${ROUTES.CATEGORY}/${categorySlug}${buildPaginationQuery({
      page: page > 1 ? page : undefined,
      pageSize:
        pageSize !== PAGINATION.CATEGORY_PAGE_SIZE ? pageSize : undefined,
    })}`;
  };

  const pageNumbers = generatePageNumbers(currentPage, totalPages);

  return (
    <Pagination className="mt-8">
      <PaginationContent>
        {/* 上一页 */}
        <PaginationItem>
          {currentPage > 1 ? (
            <PaginationPrevious href={getPageUrl(currentPage - 1)} />
          ) : (
            <PaginationPrevious
              href="#"
              className="pointer-events-none opacity-50"
            />
          )}
        </PaginationItem>

        {/* 页码 */}
        {pageNumbers.map((page, index) => {
          if (page === "ellipsis") {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }

          const isActive = page === currentPage;

          return (
            <PaginationItem key={page}>
              <PaginationLink href={getPageUrl(page)} isActive={isActive}>
                {page}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        {/* 下一页 */}
        <PaginationItem>
          {currentPage < totalPages ? (
            <PaginationNext href={getPageUrl(currentPage + 1)} />
          ) : (
            <PaginationNext
              href="#"
              className="pointer-events-none opacity-50"
            />
          )}
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}

/**
 * 工具列表组件
 */
async function CategoryToolList({
  categoryId,
  categorySlug,
  page,
  pageSize,
}: {
  categoryId: string;
  categorySlug: string;
  page: number;
  pageSize: number;
}) {
  try {
    const result = await getPaginatedToolsWithCategory({
      page,
      pageSize,
      categoryId,
    });

    const { tools, totalCount } = result;
    const totalPages = Math.ceil(totalCount / pageSize);

    if (tools.length === 0) {
      return (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium">{MESSAGES.EMPTY.NO_TOOLS}</h3>
          <p className="text-muted-foreground mt-2">
            该分类下暂无工具，请查看其他分类。
          </p>
          <Link href={ROUTES.HOME} className="mt-4 inline-block">
            <Button variant="outline">返回首页</Button>
          </Link>
        </div>
      );
    }

    return (
      <>
        <div className={RESPONSIVE_GRID.TOOL_CARDS}>
          {tools.map((tool) => {
            const toolData = transformToExtendedTool(tool);
            return (
              <div key={tool.id} className="h-full">
                <ToolCardHorizontal tool={toolData} />
              </div>
            );
          })}
        </div>

        {/* 分页控制栏 */}
        <CategoryPaginationControls
          currentPage={page}
          currentPageSize={pageSize}
          totalCount={totalCount}
          categorySlug={categorySlug}
        />

        <PaginationNav
          currentPage={page}
          totalPages={totalPages}
          categorySlug={categorySlug}
          pageSize={pageSize}
        />
      </>
    );
  } catch (error) {
    console.error("Error fetching category tools:", error);
    return (
      <Alert variant="destructive" className="my-8">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{MESSAGES.ERROR.LOAD_FAILED}</AlertTitle>
        <AlertDescription>{MESSAGES.ERROR.TOOLS_LOAD_FAILED}</AlertDescription>
      </Alert>
    );
  }
}

/**
 * 生成页面元数据
 */
export async function generateMetadata({ params }: CategoryPageProps) {
  const resolvedParams = await params;
  const category = await getCategoryBySlug(resolvedParams.slug);

  if (!category) {
    return generateSeoMetadata({
      title: "分类未找到 - AI导航",
      description: "抱歉，您访问的分类页面不存在。",
    });
  }

  return generateSeoMetadata({
    title: `${category.name} - AI工具分类 - AI导航`,
    description:
      category.description ||
      `探索 ${category.name} 分类下的AI工具和资源，发现最新最热门的AI应用。`,
    keywords: [category.name, "AI工具", "人工智能", "AI导航"],
    type: "website",
  });
}

/**
 * 分类页面组件
 */
export default async function CategoryPage({
  params,
  searchParams,
}: CategoryPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { slug } = resolvedParams;
  const { page, pageSize } = parsePaginationParams(
    resolvedSearchParams,
    PAGINATION.CATEGORY_PAGE_SIZE
  );

  // 获取分类信息
  const category = await getCategoryBySlug(slug);

  if (!category) {
    notFound();
  }

  // 面包屑数据
  const breadcrumbItems = [
    { label: "首页", href: ROUTES.HOME },
    { label: category.name, href: `${ROUTES.CATEGORY}/${slug}` },
  ];

  return (
    <div className={CSS_CLASSES.CONTAINER.WITH_PADDING}>
      <div className={CSS_CLASSES.SPACING.PAGE_Y}>
        {/* 面包屑导航 */}
        <Breadcrumbs items={breadcrumbItems} />

        {/* 分类标题和描述 */}
        <div className="mt-6 mb-8">
          <div className="flex items-center gap-4 mb-4">
            <h1 className="text-3xl font-bold">{category.name}</h1>
            <Suspense fallback={<Badge variant="outline">加载中...</Badge>}>
              <CategoryToolCount categoryId={category.id} />
            </Suspense>
          </div>
          {category.description && (
            <p className="text-muted-foreground text-lg">
              {category.description}
            </p>
          )}
        </div>

        {/* 工具列表 */}
        <Suspense
          fallback={
            <div className={`${CSS_CLASSES.ANIMATE_PULSE} space-y-4`}>
              {[...Array(10)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg" />
              ))}
            </div>
          }
        >
          <CategoryToolList
            categoryId={category.id}
            categorySlug={slug}
            page={page}
            pageSize={pageSize}
          />
        </Suspense>
      </div>
    </div>
  );
}

/**
 * 分类工具数量组件
 */
async function CategoryToolCount({ categoryId }: { categoryId: string }) {
  try {
    const count = await getToolsCountByCategory(categoryId);
    return <Badge variant="outline">{count} 个工具</Badge>;
  } catch (error) {
    console.error("Error fetching category tool count:", error);
    return <Badge variant="outline">工具</Badge>;
  }
}
