import { Metadata } from "next";
import { Inter } from "next/font/google";
import { Suspense } from "react";
import "./globals.css";
import Providers from "@/components/Providers";
import RootLayoutContent from "@/components/RootLayoutContent";
import StagewiseToolbarWrapper from "@/components/StagewiseToolbarWrapper";
import Loading from "@/app/loading";
import { generateWebsiteStructuredData } from "@/lib/utils/seo.utils";
import GoogleAnalytics from "@/components/GoogleAnalytics";

const inter = Inter({ subsets: ["latin"] });

export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"
  ),
  title: {
    template: "%s | AI 工具导航",
    default: "AI 工具导航", // 网站的默认标题
  },
  description:
    "发现最新最酷的 AI 工具和资源。探索各类智能工具，提升您的工作效率和创造力。", // 更新了描述，使其更通用
  keywords: [
    "AI工具",
    "人工智能",
    "AI导航",
    "AI资源",
    "AI应用",
    "机器学习",
    "深度学习",
  ],
  authors: [{ name: "AI导航团队" }],
  creator: "AI导航团队",
  publisher: "AI导航",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
    siteName: "AI 工具导航",
    title: "AI 工具导航 - 发现最佳AI工具和资源",
    description:
      "发现最新最酷的 AI 工具和资源。探索各类智能工具，提升您的工作效率和创造力。",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "AI 工具导航",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "AI 工具导航 - 发现最佳AI工具和资源",
    description:
      "发现最新最酷的 AI 工具和资源。探索各类智能工具，提升您的工作效率和创造力。",
    images: ["/og-image.png"],
    creator: "@ai_nav",
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    // baidu: process.env.BAIDU_SITE_VERIFICATION, // 如果需要百度验证
  },
};

/**
 * Root layout component for the application.
 * Fetches menu items dynamically and provides them to the SideMenu.
 * @param {Readonly<{ children: React.ReactNode }>} props - The component props.
 * @returns {Promise<JSX.Element>} The rendered root layout.
 */
export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const websiteStructuredData = generateWebsiteStructuredData();

  return (
    <html lang="zh-CN">
      <head>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: websiteStructuredData,
          }}
        />
        {/* Favicon */}
        <link rel="icon" href="/favicon.ico" />
        <link
          rel="apple-touch-icon"
          sizes="180x180"
          href="/apple-touch-icon.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="32x32"
          href="/favicon-32x32.png"
        />
        <link
          rel="icon"
          type="image/png"
          sizes="16x16"
          href="/favicon-16x16.png"
        />
        <link rel="manifest" href="/site.webmanifest" />
        {/* 预连接到外部域名 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        {/* DNS预取 */}
        <link rel="dns-prefetch" href="//www.google-analytics.com" />
      </head>
      <body
        className={`${inter.className} flex flex-col min-h-screen`}
        suppressHydrationWarning={true}
      >
        <GoogleAnalytics />
        <StagewiseToolbarWrapper />
        <Providers>
          <Suspense fallback={<Loading />}>
            <RootLayoutContent>{children}</RootLayoutContent>
          </Suspense>
        </Providers>
      </body>
    </html>
  );
}
