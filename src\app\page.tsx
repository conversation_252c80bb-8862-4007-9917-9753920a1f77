import {
  getPaginatedToolsWithCategory,
  getToolsCountByCategory,
  getPaginatedRecommendedTools,
  getToolsByCategories,
  type ToolWithCategory,
} from "@/lib/data/tools";
import { getAllCategories } from "@/lib/data/categories";
import ToolCardHorizontal from "@/components/ui/ToolCardHorizontal";
import RecommendedTools from "@/components/RecommendedTools";
import { Suspense } from "react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import SearchBox from "@/components/SearchBox";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  PAGINATION,
  MESSAGES,
  CATEGORY,
  CSS_CLASSES,
  ROUTES,
} from "@/lib/constants/app.constants";
import {
  transformToExtendedTool,
  type CategoryInfo,
} from "@/lib/utils/data-transform.utils";
import { RESPONSIVE_GRID } from "@/lib/utils/responsive.utils";
import {
  generateSeoMetadata,
  generateStructuredData,
} from "@/lib/utils/seo.utils";
import Script from "next/script";

/**
 * 渲染分类工具列表
 * @param categoryTools - 分类下的工具列表
 * @param categoryKey - 分类键
 * @param categoryTotalCount - 分类下工具总数
 * @returns 分类工具列表JSX元素
 */
function renderCategorySection(
  categoryTools: ToolWithCategory[],
  categoryKey: string,
  categoryTotalCount: number
) {
  const category = categoryTools[0]?.category;
  const categorySlug = category?.slug || category?.id || categoryKey;
  const hasMore = categoryTotalCount > PAGINATION.HOME_CATEGORY_THRESHOLD;

  return (
    <div
      key={categoryKey}
      id={categorySlug}
      className={CSS_CLASSES.SCROLL_OFFSET}
    >
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <Link href={`${ROUTES.CATEGORY}/${categorySlug}`} className="group">
          <h2 className="text-2xl font-bold group-hover:text-primary transition-colors">
            {category?.name || CATEGORY.UNCATEGORIZED_NAME}
          </h2>
        </Link>
        <div className="flex items-center gap-3 mt-2 md:mt-0">
          <Badge variant="outline">{categoryTotalCount} 个工具</Badge>
          {hasMore && (
            <Link href={`${ROUTES.CATEGORY}/${categorySlug}`}>
              <Button variant="outline" size="sm">
                查看全部
              </Button>
            </Link>
          )}
        </div>
      </div>

      <div className={RESPONSIVE_GRID.TOOL_CARDS}>
        {categoryTools.map((tool) => {
          const toolData = transformToExtendedTool(tool);
          return (
            <div key={tool.id} className="h-full">
              <ToolCardHorizontal tool={toolData} />
            </div>
          );
        })}
      </div>

      {hasMore && (
        <div className="mt-6 text-center">
          <Link href={`${ROUTES.CATEGORY}/${categorySlug}`}>
            <Button variant="outline">
              查看全部 {categoryTotalCount} 个工具
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}

/**
 * 生成页面元数据
 * @returns 页面元数据配置
 */
export async function generateMetadata() {
  return generateSeoMetadata({
    title: "AI导航 - 发现最佳AI工具和资源",
    description:
      "欢迎来到 AI 工具导航！发现最新、最热门的AI应用与技术，探索各类智能工具，提升您的工作效率和创造力。",
    keywords: ["AI工具", "人工智能", "AI导航", "AI资源", "AI应用"],
    type: "website",
  });
}

/**
 * 推荐工具列表组件 - 获取并显示推荐工具
 * @returns 推荐工具列表JSX元素
 */
async function RecommendedToolsList() {
  try {
    const result = await getPaginatedRecommendedTools({
      page: PAGINATION.DEFAULT_PAGE,
      pageSize: PAGINATION.RECOMMENDED_PAGE_SIZE,
    });

    if (result.totalCount === 0) {
      return null;
    }

    return (
      <RecommendedTools
        initialTools={result.tools}
        initialTotalCount={result.totalCount}
        initialPage={PAGINATION.DEFAULT_PAGE}
      />
    );
  } catch (error) {
    console.error("Error fetching recommended tools:", error);
    return null;
  }
}

/**
 * 工具列表组件 - 按分类显示工具，每个分类显示前30个工具
 * @returns 工具列表JSX元素
 */
async function ToolList() {
  let categories: CategoryInfo[] = [];
  let toolsByCategory: Record<string, ToolWithCategory[]> = {};

  try {
    // 首先获取所有分类
    const categoriesResult = await getAllCategories();

    // 转换分类数据格式
    categories = categoriesResult.map((category) => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      order: category.order || 0,
    }));

    // 如果有分类，则为每个分类获取前30个工具
    if (categories.length > 0) {
      const categoriesWithLimit = categories.map((category) => ({
        categoryId: category.id,
        limit: PAGINATION.HOME_CATEGORY_TOOLS_COUNT,
      }));

      toolsByCategory = await getToolsByCategories(categoriesWithLimit);
    }
  } catch (error) {
    console.error("Error fetching tools:", error);
    return (
      <Alert variant="destructive" className="my-8">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{MESSAGES.ERROR.LOAD_FAILED}</AlertTitle>
        <AlertDescription>{MESSAGES.ERROR.TOOLS_LOAD_FAILED}</AlertDescription>
      </Alert>
    );
  }

  // 如果没有分类数据，显示空状态
  if (categories.length === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium">{MESSAGES.EMPTY.NO_TOOLS}</h3>
        <p className="text-muted-foreground mt-2">
          {MESSAGES.EMPTY.NO_TOOLS_AVAILABLE}
        </p>
      </div>
    );
  }

  // 按分类order排序
  const sortedCategories = categories.sort((a, b) => a.order - b.order);

  // 获取每个分类的工具总数
  const categoryCountsPromises = sortedCategories.map(async (category) => {
    const count = await getToolsCountByCategory(category.id);
    return { categoryId: category.id, count };
  });

  const categoryCounts = await Promise.all(categoryCountsPromises);
  const categoryCountMap = categoryCounts.reduce<Record<string, number>>(
    (acc, { categoryId, count }) => {
      acc[categoryId] = count;
      return acc;
    },
    {}
  );

  return (
    <div className="space-y-12">
      {sortedCategories.map((category) => {
        const categoryTools = toolsByCategory[category.id] || [];
        const categoryTotalCount = categoryCountMap[category.id] || 0;

        // 只显示有工具的分类
        if (categoryTools.length === 0) {
          return null;
        }

        return (
          <div key={category.id}>
            {renderCategorySection(
              categoryTools,
              category.slug || category.id,
              categoryTotalCount
            )}
          </div>
        );
      })}
    </div>
  );
}

/**
 * 首页组件 - 显示AI工具导航主页
 * @returns 首页JSX元素
 */
export default async function HomePage() {
  let tools: ToolWithCategory[] = [];

  try {
    // 尝试获取工具列表，但在构建时可能会失败
    const result = await getPaginatedToolsWithCategory({
      page: PAGINATION.DEFAULT_PAGE,
      pageSize: PAGINATION.HOME_MAX_TOOLS,
    }).catch(() => ({ tools: [] }));
    tools = result.tools;
  } catch (error) {
    console.error("Error in HomePage:", error);
  }

  // 生成首页结构化数据
  const breadcrumbStructuredData = generateStructuredData("BreadcrumbList", {
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        name: "首页",
        item: process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro",
      },
    ],
  });

  return (
    <>
      {/* 首页结构化数据 */}
      <Script
        id="homepage-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: breadcrumbStructuredData,
        }}
      />

      <div className={CSS_CLASSES.CONTAINER.WITH_PADDING}>
        <div className={`${CSS_CLASSES.SPACING.PAGE_Y} text-center`}>
          <h1 className="mb-4 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
            探索AI导航
          </h1>
          <div className="mx-auto max-w-2xl">
            <SearchBox
              placeholder="搜索AI工具、资源和创意项目..."
              className="w-full text-base sm:text-lg"
            />
            <p className="mt-4 text-muted-foreground">
              发现最新、最热门的AI应用与技术。
            </p>
          </div>
        </div>

        {/* 推荐工具区域 */}
        <Suspense
          fallback={
            <div className="mb-12">
              <div className="flex items-center gap-3 mb-6">
                <div className="h-6 w-6 bg-gray-200 rounded animate-pulse" />
                <div className="h-8 w-32 bg-gray-200 rounded animate-pulse" />
              </div>
              <div className={`${CSS_CLASSES.ANIMATE_PULSE} space-y-4`}>
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg" />
                ))}
              </div>
            </div>
          }
        >
          <RecommendedToolsList />
        </Suspense>

        {tools.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium">欢迎使用AI导航</h3>
            <p className="text-muted-foreground mt-2">
              {MESSAGES.LOADING.TOOLS}
            </p>
          </div>
        ) : (
          <Suspense
            fallback={
              <div className={`${CSS_CLASSES.ANIMATE_PULSE} space-y-4`}>
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded-lg" />
                ))}
              </div>
            }
          >
            <ToolList />
          </Suspense>
        )}
      </div>
    </>
  );
}
