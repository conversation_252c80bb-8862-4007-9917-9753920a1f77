import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import prisma from "../../lib/prisma";
import Breadcrumbs from "../../components/Breadcrumbs";
import { Badge } from "../../components/ui/badge";
import { Tool, Category } from "@prisma/client"; // Import Prisma types
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../../components/ui/pagination";
import { SearchPaginationControls } from "../../components/SearchPaginationControls";
import { PAGINATION, ROUTES } from "../../lib/constants/app.constants";
import { RESPONSIVE_GRID } from "../../lib/utils/responsive.utils";
import {
  parsePaginationParams,
  buildPaginationQuery,
  generatePageNumbers,
} from "../../lib/utils/pagination.utils";

interface EnrichedTool extends Tool {
  category: Category | null;
}

interface SearchResult {
  tools: EnrichedTool[];
  totalCount: number;
}

/**
 * 搜索结果专用的工具卡片组件，包含分类和标签显示
 */
function SearchToolCard({ tool }: { tool: EnrichedTool }) {
  return (
    <div className="h-full bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
      <Link href={`/tool/${tool.id}`} className="block h-full flex flex-col">
        <div className="p-4 flex-grow">
          <div className="flex items-start space-x-4">
            {/* 左侧：图标 */}
            <div className="flex-shrink-0">
              {tool.iconUrl ? (
                <div className="w-16 h-16 relative rounded">
                  <Image
                    src={tool.iconUrl}
                    alt={`${tool.name} 图标`}
                    fill
                    sizes="64px"
                    className="rounded object-cover"
                  />
                </div>
              ) : (
                <div className="w-16 h-16 bg-gray-200 flex items-center justify-center rounded">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-8 h-8 text-gray-400"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.158 0a.225.225 0 0 1 .225.225v.008a.225.225 0 0 1-.225.225H12.9a.225.225 0 0 1-.225-.225V8.475a.225.225 0 0 1 .225-.225h.008Z"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* 右侧：文本内容 */}
            <div className="flex-grow min-w-0">
              <h3
                className="text-base font-semibold text-gray-800 line-clamp-1"
                title={tool.name}
              >
                {tool.name}
              </h3>
              <p
                className="text-sm text-gray-600 line-clamp-2 mt-1"
                title={tool.description}
              >
                {tool.description}
              </p>
            </div>
          </div>
        </div>

        {/* 底部：标签和分类 */}
        <div className="px-4 pb-3 pt-2 bg-gray-50 border-t border-gray-100">
          <div className="flex justify-between items-center">
            {tool.category && (
              <Badge variant="secondary" className="text-xs">
                {tool.category.name}
              </Badge>
            )}
            <div className="flex flex-wrap gap-1">
              {tool.tags?.slice(0, 2).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}

async function searchTools(
  query: string,
  page: number = 1,
  pageSize: number = PAGINATION.SEARCH_PAGE_SIZE
): Promise<SearchResult> {
  if (!query) return { tools: [], totalCount: 0 };

  try {
    const skip = (page - 1) * pageSize;

    const searchCondition = {
      OR: [
        { name: { contains: query, mode: "insensitive" as const } },
        { description: { contains: query, mode: "insensitive" as const } },
        { tags: { has: query } }, // This is case-sensitive for array elements.
      ],
    };

    // 并行查询总数和分页数据
    const [totalCount, tools] = await Promise.all([
      prisma.tool.count({ where: searchCondition }),
      prisma.tool.findMany({
        where: searchCondition,
        include: {
          category: true,
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: pageSize,
      }),
    ]);

    return {
      tools: tools as EnrichedTool[],
      totalCount,
    };
  } catch (error) {
    console.error("Error searching tools:", error);
    return { tools: [], totalCount: 0 };
  }
}

type SearchPageProps = {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
};

/**
 * 搜索分页导航组件
 */
function SearchPaginationNav({
  currentPage,
  totalPages,
  query,
  pageSize,
}: {
  currentPage: number;
  totalPages: number;
  query: string;
  pageSize: number;
}) {
  if (totalPages <= 1) return null;

  const getPageUrl = (page: number) => {
    return `${ROUTES.SEARCH}${buildPaginationQuery({
      q: query,
      page: page > 1 ? page : undefined,
      pageSize: pageSize !== PAGINATION.SEARCH_PAGE_SIZE ? pageSize : undefined,
    })}`;
  };

  const pageNumbers = generatePageNumbers(currentPage, totalPages);

  return (
    <Pagination className="mt-8">
      <PaginationContent>
        {/* 上一页 */}
        <PaginationItem>
          {currentPage > 1 ? (
            <PaginationPrevious href={getPageUrl(currentPage - 1)} />
          ) : (
            <PaginationPrevious
              href="#"
              className="pointer-events-none opacity-50"
            />
          )}
        </PaginationItem>

        {/* 页码 */}
        {pageNumbers.map((page, index) => {
          if (page === "ellipsis") {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }

          const isActive = page === currentPage;

          return (
            <PaginationItem key={page}>
              <PaginationLink href={getPageUrl(page)} isActive={isActive}>
                {page}
              </PaginationLink>
            </PaginationItem>
          );
        })}

        {/* 下一页 */}
        <PaginationItem>
          {currentPage < totalPages ? (
            <PaginationNext href={getPageUrl(currentPage + 1)} />
          ) : (
            <PaginationNext
              href="#"
              className="pointer-events-none opacity-50"
            />
          )}
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}

export async function generateMetadata({
  searchParams,
}: SearchPageProps): Promise<Metadata> {
  // The searchParams object itself needs to be awaited as per Next.js 13+ app router conventions
  // when used in generateMetadata or other Server Components if they are dynamic.
  // However, in this specific case, the error message "searchParams should be awaited before using its properties"
  // implies that `searchParams` itself behaves like a promise or has a mechanism that requires awaiting
  // for its properties to be ready, especially when dealing with dynamic route segments or search parameters.
  // For generateMetadata, Next.js handles the promise-like nature of searchParams.
  // The primary fix is to ensure that when `searchParams.q` is accessed, `searchParams` has been "resolved".
  // In many typical Next.js `generateMetadata` scenarios, `searchParams` can be directly accessed.
  // The error "searchParams should be awaited" usually means that the *act* of accessing its properties *before*
  // Next.js has had a chance to fully process them (which it does implicitly when you `await` something *derived* from it,
  // or if the function itself is async and Next.js awaits it) is the issue.
  // Let's directly use await on searchParams as suggested by the error's spirit,
  // even if TypeScript doesn't immediately show `searchParams` as a `Promise<ActualType>`.
  // Next.js's runtime handles this.

  const resolvedSearchParams = await searchParams; // Await the searchParams object directly
  const query =
    typeof resolvedSearchParams.q === "string" ? resolvedSearchParams.q : "";
  if (!query) {
    return {
      title: "搜索 - AI 工具导航",
    };
  }
  return {
    title: `搜索 "${query}" - AI 工具导航`,
    description: `查看与 "${query}" 相关的 AI 工具搜索结果。`,
  };
}

export default async function SearchPage({
  searchParams: searchParamsPromise,
}: SearchPageProps) {
  const searchParams = await searchParamsPromise;
  const {
    page,
    pageSize,
    search: query,
  } = parsePaginationParams(searchParams, PAGINATION.SEARCH_PAGE_SIZE);

  const { tools, totalCount } = await searchTools(query, page, pageSize);
  const totalPages = Math.ceil(totalCount / pageSize);

  const breadcrumbItems = [
    { label: "首页", href: "/" },
    { label: "搜索结果" },
    ...(query ? [{ label: `"${query}"` }] : []),
  ];

  return (
    <div>
      <Breadcrumbs items={breadcrumbItems} />
      <h1 className="text-3xl font-bold mb-6">
        {query ? `搜索结果: "${query}"` : "请输入搜索词"}
      </h1>

      {query && (
        <p className="mb-6 text-gray-600">
          找到了 {totalCount} 个与 "{query}" 相关的工具
          {totalPages > 1 && (
            <>
              ，当前显示第 {page} 页，共 {totalPages} 页
            </>
          )}
          。
        </p>
      )}

      {!query && (
        <p className="text-gray-600">请输入关键词开始搜索 AI 工具。</p>
      )}

      {query && totalCount === 0 && (
        <p className="text-gray-600">
          抱歉，没有找到与 "{query}" 相关的工具。请尝试其他关键词。
        </p>
      )}

      {tools.length > 0 && (
        <>
          <div className={RESPONSIVE_GRID.TOOL_CARDS}>
            {tools.map((tool) => (
              <div key={tool.id} className="h-full">
                <SearchToolCard tool={tool} />
              </div>
            ))}
          </div>

          {/* 分页控制栏 */}
          <SearchPaginationControls
            currentPage={page}
            currentPageSize={pageSize}
            totalCount={totalCount}
            query={query}
          />

          {/* 分页导航 */}
          <SearchPaginationNav
            currentPage={page}
            totalPages={totalPages}
            query={query}
            pageSize={pageSize}
          />
        </>
      )}
    </div>
  );
}
