import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import redis from "@/lib/redis";

// 缓存键
const CACHE_KEY = "sitemap:categories:xml";
const CACHE_TTL = 3600; // 1小时

/**
 * 生成分类页面的sitemap XML
 */
export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";

    // 尝试从缓存获取
    const cached = await redis?.get(CACHE_KEY);
    if (cached) {
      return new NextResponse(cached, {
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600, s-maxage=3600",
        },
      });
    }

    // 获取所有分类
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        slug: true,
        updatedAt: true,
        _count: {
          select: {
            tools: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc",
      },
    });

    // 计算分类优先级
    function calculateCategoryPriority(toolCount: number): string {
      let priority = 0.5;
      if (toolCount > 50) {
        priority = 0.8;
      } else if (toolCount > 20) {
        priority = 0.7;
      } else if (toolCount > 5) {
        priority = 0.6;
      }
      return priority.toFixed(1);
    }

    // 生成XML
    const urls = categories
      .map((category) => {
        const url = `${baseUrl}/category/${category.slug || category.id}`;
        const lastmod = category.updatedAt.toISOString();
        const priority = calculateCategoryPriority(category._count?.tools || 0);

        return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
      })
      .join("\n");

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;

    // 缓存结果
    if (redis) {
      await redis.setex(CACHE_KEY, CACHE_TTL, sitemap);
    }

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating categories sitemap:", error);

    // 返回空的sitemap
    const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new NextResponse(emptySitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=300",
      },
    });
  }
}
