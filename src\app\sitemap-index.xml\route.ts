import { NextResponse } from "next/server";
import prisma from "@/lib/prisma";

/**
 * 生成sitemap索引文件
 * 当sitemap条目过多时，将其分割为多个文件
 */
export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
    const now = new Date().toISOString();

    // 获取数据统计
    const [categoriesCount, toolsCount] = await Promise.all([
      prisma.category.count(),
      prisma.tool.count(),
    ]);

    // 计算总条目数（静态页面 + 分类 + 工具 + 标签）
    const staticPages = 2;
    const estimatedTagsCount = 20; // 热门标签数量
    const totalEntries = staticPages + categoriesCount + toolsCount + estimatedTagsCount;

    // 如果条目数少于50,000，使用单个sitemap文件
    if (totalEntries < 50000) {
      const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap.xml</loc>
    <lastmod>${now}</lastmod>
  </sitemap>
</sitemapindex>`;

      return new NextResponse(sitemapIndex, {
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600, s-maxage=3600",
        },
      });
    }

    // 如果条目数超过50,000，分割为多个sitemap文件
    const sitemaps = [];
    
    // 主sitemap（静态页面）
    sitemaps.push({
      loc: `${baseUrl}/sitemap-main.xml`,
      lastmod: now,
    });

    // 分类sitemap
    if (categoriesCount > 0) {
      sitemaps.push({
        loc: `${baseUrl}/sitemap-categories.xml`,
        lastmod: now,
      });
    }

    // 工具sitemap（可能需要分割为多个文件）
    const toolsPerSitemap = 45000; // 每个sitemap文件的工具数量
    const toolSitemapCount = Math.ceil(toolsCount / toolsPerSitemap);

    for (let i = 0; i < toolSitemapCount; i++) {
      sitemaps.push({
        loc: `${baseUrl}/sitemap-tools-${i + 1}.xml`,
        lastmod: now,
      });
    }

    // 标签sitemap
    sitemaps.push({
      loc: `${baseUrl}/sitemap-tags.xml`,
      lastmod: now,
    });

    const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps
  .map(
    (sitemap) => `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`
  )
  .join("\n")}
</sitemapindex>`;

    return new NextResponse(sitemapIndex, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating sitemap index:", error);
    
    // 降级到单个sitemap
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
    const now = new Date().toISOString();
    
    const fallbackIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap.xml</loc>
    <lastmod>${now}</lastmod>
  </sitemap>
</sitemapindex>`;

    return new NextResponse(fallbackIndex, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=300", // 较短的缓存时间
      },
    });
  }
}
