import { NextResponse } from "next/server";

/**
 * 生成主要静态页面的sitemap XML
 */
export async function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
    const now = new Date().toISOString();

    // 静态页面列表
    const staticPages = [
      {
        url: baseUrl,
        lastmod: now,
        changefreq: "daily",
        priority: "1.0",
      },
      {
        url: `${baseUrl}/search`,
        lastmod: now,
        changefreq: "weekly",
        priority: "0.8",
      },
    ];

    // 生成XML
    const urls = staticPages
      .map((page) => {
        return `  <url>
    <loc>${page.url}</loc>
    <lastmod>${page.lastmod}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
      })
      .join("\n");

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=86400, s-maxage=86400", // 24小时缓存
      },
    });
  } catch (error) {
    console.error("Error generating main sitemap:", error);

    // 返回基本的sitemap
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";
    const now = new Date().toISOString();

    const fallbackSitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${now}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;

    return new NextResponse(fallbackSitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=300",
      },
    });
  }
}
