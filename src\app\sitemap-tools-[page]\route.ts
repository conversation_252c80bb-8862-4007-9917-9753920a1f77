import { NextRequest, NextResponse } from "next/server";
import prisma from "@/lib/prisma";
import redis from "@/lib/redis";

// 每页工具数量
const TOOLS_PER_PAGE = 45000;
const CACHE_TTL = 3600; // 1小时

/**
 * 生成工具页面的分页sitemap XML
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ page: string }> }
) {
  try {
    const resolvedParams = await params;
    const page = parseInt(resolvedParams.page) || 1;
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro";

    // 缓存键
    const cacheKey = `sitemap:tools:xml:page:${page}`;

    // 尝试从缓存获取
    const cached = await redis?.get(cacheKey);
    if (cached) {
      return new NextResponse(cached, {
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600, s-maxage=3600",
        },
      });
    }

    // 计算分页参数
    const skip = (page - 1) * TOOLS_PER_PAGE;
    const take = TOOLS_PER_PAGE;

    // 获取工具数据
    const tools = await prisma.tool.findMany({
      select: {
        id: true,
        updatedAt: true,
        views: true,
        isFeatured: true,
        lastViewedAt: true,
      },
      orderBy: {
        updatedAt: "desc",
      },
      skip,
      take,
    });

    // 如果没有工具，返回空sitemap
    if (tools.length === 0) {
      const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

      return new NextResponse(emptySitemap, {
        headers: {
          "Content-Type": "application/xml",
          "Cache-Control": "public, max-age=3600, s-maxage=3600",
        },
      });
    }

    // 计算工具优先级
    function calculateToolPriority(tool: {
      views: number;
      isFeatured: boolean;
      lastViewedAt: Date | null;
    }): string {
      let priority = 0.6;

      if (tool.isFeatured) {
        priority += 0.2;
      }

      if (tool.views > 1000) {
        priority += 0.1;
      } else if (tool.views > 100) {
        priority += 0.05;
      }

      if (tool.lastViewedAt) {
        const daysSinceLastView =
          (Date.now() - tool.lastViewedAt.getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceLastView < 7) {
          priority += 0.1;
        } else if (daysSinceLastView < 30) {
          priority += 0.05;
        }
      }

      return Math.min(priority, 1.0).toFixed(1);
    }

    // 生成XML
    const urls = tools
      .map((tool) => {
        const url = `${baseUrl}/tool/${tool.id}`;
        const lastmod = tool.updatedAt.toISOString();
        const priority = calculateToolPriority(tool);

        return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>daily</changefreq>
    <priority>${priority}</priority>
  </url>`;
      })
      .join("\n");

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;

    // 缓存结果
    if (redis) {
      await redis.setex(cacheKey, CACHE_TTL, sitemap);
    }

    return new NextResponse(sitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=3600, s-maxage=3600",
      },
    });
  } catch (error) {
    console.error("Error generating tools sitemap:", error);

    // 返回空的sitemap
    const emptySitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
</urlset>`;

    return new NextResponse(emptySitemap, {
      headers: {
        "Content-Type": "application/xml",
        "Cache-Control": "public, max-age=300, s-maxage=300",
      },
    });
  }
}
