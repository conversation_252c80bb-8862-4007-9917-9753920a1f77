import { Metadata, ResolvingMetadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { notFound } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { Suspense } from "react";
import { FormattedDate } from "@/components/ui/FormattedDate";
import { ExternalLink, Globe } from "lucide-react";
import Breadcrumbs from "@/components/Breadcrumbs";
import ViewTracker from "@/components/ViewTracker";
import { getToolById } from "@/lib/data/tools"; // 导入新的缓存函数

type PageParams = { toolId: string };
type SearchParams = Record<string, string | string[] | undefined>;

type Props = {
  params: Promise<PageParams>;
  searchParams?: Promise<SearchParams>;
};

// The old getToolDetails function is removed

export async function generateMetadata(
  { params, searchParams }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);
  const toolId = resolvedParams.toolId;
  try {
    const tool = await getToolById(toolId); // 使用新的缓存函数

    if (!tool) {
      return {
        title: "工具未找到 - AI 工具导航",
      };
    }

    const siteName = "AI 工具导航";
    const categoryName = tool.category?.name || "工具";
    const title = `${tool.name} - ${categoryName} | ${siteName}`;
    const description = tool.description
      ? tool.description.substring(0, 160)
      : `探索 ${tool.name} - ${siteName} 上的优秀AI工具。`;

    // Ensure NEXTAUTH_URL is defined and iconUrl is absolute for OpenGraph/Twitter
    const siteUrl = process.env.NEXTAUTH_URL || "";
    const absoluteIconUrl = tool.iconUrl
      ? tool.iconUrl.startsWith("http")
        ? tool.iconUrl
        : `${siteUrl}${tool.iconUrl.startsWith("/") ? "" : "/"}${tool.iconUrl}`
      : "";

    const images = absoluteIconUrl
      ? [{ url: absoluteIconUrl, width: 200, height: 200, alt: tool.name }]
      : [];
    const keywords = tool.tags.join(", ");

    return {
      title: title,
      description: description,
      openGraph: {
        title: title,
        description: description,
        url: `${siteUrl}/tool/${tool.id}`,
        siteName: siteName,
        images: images,
        type: "article",
        locale: "zh_CN",
      },
      twitter: {
        card: "summary_large_image",
        title: title,
        description: description,
        images: images,
      },
      keywords: keywords,
    };
  } catch (error) {
    console.error("Error in generateMetadata for tool after fetching:", error);
    return {
      title: "错误 - AI 工具导航",
      description: "生成工具元数据时发生错误。",
    };
  }
}

export default async function ToolDetailPage({ params, searchParams }: Props) {
  const [resolvedParams, resolvedSearchParams] = await Promise.all([
    params,
    searchParams,
  ]);
  const tool = await getToolById(resolvedParams.toolId);

  if (!tool) {
    notFound();
  }

  // tool.tags 是 String[]
  const toolTags = tool.tags;

  const breadcrumbItems = [
    { label: "首页", href: "/" },
    ...(tool.category
      ? [
          {
            label: tool.category.name,
            href: `/#${tool.category.slug || tool.category.id}`,
          },
        ]
      : []),
    { label: tool.name }, // Current page, no href
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 浏览次数跟踪组件 */}
      <ViewTracker toolId={tool.id} toolName={tool.name} />

      <Breadcrumbs items={breadcrumbItems} />
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="md:flex">
          {tool.iconUrl && (
            <div className="md:w-1/3 p-6 flex justify-center items-center bg-slate-50">
              <Image
                src={tool.iconUrl}
                alt={`${tool.name} 图标`}
                width={128}
                height={128}
                className="rounded-lg object-contain"
              />
            </div>
          )}
          <div className={`p-6 ${tool.iconUrl ? "md:w-2/3" : "w-full"}`}>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {tool.name}
            </h1>
            <div className="flex items-center space-x-2 mb-4">
              <Suspense fallback={null}>
                {tool.isFeatured && (
                  <Badge
                    variant="default"
                    className="bg-yellow-400 text-yellow-900"
                  >
                    精选
                  </Badge>
                )}
                {tool.isFree && (
                  <Badge
                    variant="secondary"
                    className="bg-green-400 text-green-900"
                  >
                    免费
                  </Badge>
                )}
              </Suspense>
              {tool.category && ( // 确保 category 存在再渲染
                <Link
                  href={
                    tool.category.slug
                      ? `/category/${tool.category.slug}`
                      : `/#${tool.category.id}`
                  }
                  className="text-sm text-indigo-600 hover:text-indigo-800"
                >
                  <Badge variant="outline">{tool.category.name}</Badge>
                </Link>
              )}
            </div>
            <p className="text-gray-600 text-base mb-6">
              {tool.description || "暂无详细描述。"}
            </p>

            {toolTags && toolTags.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-700 mb-2">
                  标签:
                </h3>
                <div className="flex flex-wrap gap-2">
                  {toolTags.map(
                    (
                      tag,
                      index // tag 是 string
                    ) => (
                      <Badge key={index} variant="outline" className="text-sm">
                        {tag}
                      </Badge>
                    )
                  )}
                </div>
              </div>
            )}

            <div className="space-y-4">
              <Link
                href={tool.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center justify-center w-full md:w-auto px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <ExternalLink className="mr-2 h-5 w-5" />
                访问工具
              </Link>
              {tool.website && (
                <Link
                  href={tool.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center justify-center w-full md:w-auto px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ml-0 md:ml-3 mt-3 md:mt-0"
                >
                  <Globe className="mr-2 h-5 w-5" />
                  官方网站
                </Link>
              )}
            </div>
          </div>
        </div>
        <div className="border-t border-gray-200 px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
            <p>
              创建时间: <FormattedDate date={tool.createdAt} />
            </p>
            <p>
              更新时间: <FormattedDate date={tool.updatedAt} />
            </p>
            <p>浏览次数: {tool.views || 0}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
