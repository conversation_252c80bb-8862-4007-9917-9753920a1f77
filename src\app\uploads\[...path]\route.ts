import { NextRequest, NextResponse } from "next/server";
import { readFile, stat } from "fs/promises";
import { join } from "path";
import { constants } from "fs";

/**
 * 静态文件服务 API 路由
 * 用于在生产环境中提供上传文件的访问
 * 路径: /uploads/[...path]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // 获取参数
    const resolvedParams = await params;
    // 获取文件路径
    const filePath = resolvedParams.path.join("/");
    const fullPath = join(process.cwd(), "public", "uploads", filePath);

    console.log(`[STATIC] 请求文件: ${filePath}`);
    console.log(`[STATIC] 完整路径: ${fullPath}`);

    // 安全检查：防止路径遍历攻击
    if (filePath.includes("..") || filePath.includes("\\")) {
      console.log(`[STATIC] 安全检查失败: 非法路径 ${filePath}`);
      return new NextResponse("Forbidden", { status: 403 });
    }

    // 检查文件是否存在
    try {
      const stats = await stat(fullPath);
      if (!stats.isFile()) {
        console.log(`[STATIC] 路径不是文件: ${fullPath}`);
        return new NextResponse("Not Found", { status: 404 });
      }
    } catch (error) {
      console.log(`[STATIC] 文件不存在: ${fullPath}`, error);
      return new NextResponse("Not Found", { status: 404 });
    }

    // 读取文件
    try {
      const fileBuffer = await readFile(fullPath);

      // 根据文件扩展名设置 Content-Type
      const ext = filePath.split(".").pop()?.toLowerCase();
      let contentType = "application/octet-stream";

      switch (ext) {
        case "png":
          contentType = "image/png";
          break;
        case "jpg":
        case "jpeg":
          contentType = "image/jpeg";
          break;
        case "webp":
          contentType = "image/webp";
          break;
        case "gif":
          contentType = "image/gif";
          break;
        case "svg":
          contentType = "image/svg+xml";
          break;
        default:
          contentType = "application/octet-stream";
      }

      console.log(`[STATIC] 成功提供文件: ${filePath}, 类型: ${contentType}`);

      // 返回文件内容
      return new NextResponse(fileBuffer, {
        status: 200,
        headers: {
          "Content-Type": contentType,
          "Cache-Control": "public, max-age=31536000, immutable", // 1年缓存
          "Content-Length": fileBuffer.length.toString(),
        },
      });
    } catch (readError) {
      console.error(`[STATIC] 读取文件失败: ${fullPath}`, readError);
      return new NextResponse("Internal Server Error", { status: 500 });
    }
  } catch (error) {
    console.error(`[STATIC] 处理请求失败:`, error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
