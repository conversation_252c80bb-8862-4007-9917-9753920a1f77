import Link from "next/link";
import { ChevronRight } from "lucide-react";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export default function Breadcrumbs({ items }: BreadcrumbsProps) {
  return (
    <nav aria-label="breadcrumb" className="mb-6">
      <ol className="flex items-center space-x-1 text-sm text-gray-500">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {item.href ? (
              <Link
                href={item.href}
                className="hover:text-blue-600 hover:underline"
                scroll={!item.href.startsWith('#')} // 禁用 Next.js 对锚点的默认滚动行为
              >
                {item.label}
              </Link>
            ) : (
              <span className="font-semibold text-gray-700">{item.label}</span>
            )}
            {index < items.length - 1 && (
              <ChevronRight size={16} className="mx-1 text-gray-400" />
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
