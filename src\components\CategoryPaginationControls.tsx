"use client";

import { useRouter } from "next/navigation";
import { PaginationControls } from "@/components/ui/page-size-selector";
import { buildPaginationQuery, adjustPageOnSizeChange } from "@/lib/utils/pagination.utils";
import { PAGINATION, ROUTES } from "@/lib/constants/app.constants";

interface CategoryPaginationControlsProps {
  /** 当前页码 */
  currentPage: number;
  /** 当前每页大小 */
  currentPageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 分类slug */
  categorySlug: string;
}

/**
 * 分类页面分页控制组件
 * 
 * @description 客户端组件，处理分页大小改变时的页面跳转
 */
export function CategoryPaginationControls({
  currentPage,
  currentPageSize,
  totalCount,
  categorySlug,
}: CategoryPaginationControlsProps) {
  const router = useRouter();

  const handlePageSizeChange = (newPageSize: number) => {
    // 计算调整后的页码，保持用户在相似的位置
    const adjustedPage = adjustPageOnSizeChange(
      currentPage,
      currentPageSize,
      newPageSize,
      totalCount
    );

    // 构建新的查询参数
    const newQuery = buildPaginationQuery({
      page: adjustedPage > 1 ? adjustedPage : undefined,
      pageSize: newPageSize !== PAGINATION.CATEGORY_PAGE_SIZE ? newPageSize : undefined,
    });

    // 跳转到新的URL
    router.push(`${ROUTES.CATEGORY}/${categorySlug}${newQuery}`);
  };

  return (
    <PaginationControls
      currentPage={currentPage}
      currentPageSize={currentPageSize}
      totalCount={totalCount}
      onPageSizeChange={handlePageSizeChange}
    />
  );
}
