"use client";

import { signIn, useSession } from "next-auth/react";
import Link from "next/link";
import SearchInput from "./SearchInput";
import { Button } from "@/components/ui/button";
import UserMenu from "@/components/UserMenu";
import { useIsClient } from "@/hooks/useIsClient";

export default function Header() {
  const { data: session, status } = useSession();
  const isClient = useIsClient();

  return (
    <header className="bg-white text-gray-700 sticky top-0 z-50 border-b border-gray-200">
      <nav className="container mx-auto px-6 py-3 flex justify-between items-center">
        <Link
          href="/"
          className="text-xl font-bold text-gray-800 hover:text-gray-600"
        >
          AI 工具导航
        </Link>
        <div className="flex items-center space-x-4">
          <SearchInput />

          {!isClient || status === "loading" ? (
            <div className="flex items-center space-x-2 p-2">
              <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse" />
            </div>
          ) : status === "authenticated" && session?.user ? (
            <UserMenu session={session} />
          ) : status === "unauthenticated" ? (
            <Button
              onClick={() => signIn("logto")}
              variant="ghost"
              size="sm"
              className="text-gray-700 hover:bg-gray-100"
            >
              登录
            </Button>
          ) : null}
        </div>
      </nav>
    </header>
  );
}
