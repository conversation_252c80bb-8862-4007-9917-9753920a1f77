"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { useState } from "react";
import { useRouter } from "next/navigation";

interface LargeSearchInputProps {
  placeholder?: string;
  className?: string;
}

export function LargeSearchInput({
  placeholder = "搜索...",
  className,
}: LargeSearchInputProps) {
  const [query, setQuery] = useState("");
  const router = useRouter();

  const handleSearch = () => {
    const trimmedQuery = query.trim();
    if (trimmedQuery === "") return;
    router.push(`/search?q=${encodeURIComponent(trimmedQuery)}`);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className={cn("relative flex w-full items-center", className)}>
      <Input
        type="search"
        value={query}
        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
          setQuery(e.target.value)
        }
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="h-14 flex-grow rounded-l-xl rounded-r-none border-2 border-transparent bg-secondary/50 px-4 py-2 pl-4 pr-4 text-lg shadow-lg transition-all duration-300 ease-in-out focus:border-blue-500 focus:bg-background focus:shadow-2xl focus:outline-none focus:ring-0"
      />
      <button
        type="button"
        onClick={handleSearch}
        className="flex h-14 items-center justify-center rounded-l-none rounded-r-xl border-2 border-transparent border-l-0 bg-secondary/50 px-4 py-2 text-lg shadow-lg transition-all duration-300 ease-in-out hover:bg-secondary/70 focus:border-blue-500 focus:bg-background focus:shadow-2xl focus:outline-none focus:ring-0"
        aria-label="搜索"
      >
        <Search className="h-5 w-5 text-muted-foreground group-hover:text-foreground" />
      </button>
    </div>
  );
}
