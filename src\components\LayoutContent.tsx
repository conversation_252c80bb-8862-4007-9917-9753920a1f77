"use client";

import Header from "@/components/Header";
import Footer from "@/components/Footer";
import type { ReactNode } from "react";

export default function LayoutContent({
  children,
  className,
  sideMenuComponent,
}: {
  children: ReactNode;
  className?: string;
  sideMenuComponent?: ReactNode;
}) {
  return (
    <div className="flex flex-1 h-full">
      {/* 渲染侧边菜单组件 */}
      {sideMenuComponent}
      <div className="flex flex-col flex-1">
        <Header />
        <main
          className={`flex-1 p-4 overflow-y-auto bg-background text-foreground ${
            className || ""
          }`}
        >
          {children}
        </main>
        <Footer />
      </div>
    </div>
  );
}
