"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import ToolCardHorizontal from "@/components/ui/ToolCardHorizontal";
import {
  Pagin<PERSON>,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Star } from "lucide-react";
import { PAGINATION, MESSAGES, CSS_CLASSES } from "@/lib/constants/app.constants";
import { RESPONSIVE_GRID } from "@/lib/utils/responsive.utils";
import { transformToExtendedTool } from "@/lib/utils/data-transform.utils";
import type { ToolWithCategory } from "@/lib/data/tools";

interface RecommendedToolsProps {
  initialTools: ToolWithCategory[];
  initialTotalCount: number;
  initialPage?: number;
}

interface RecommendedToolsResponse {
  tools: ToolWithCategory[];
  totalCount: number;
}

/**
 * 获取推荐工具数据
 * @param page - 页码
 * @param pageSize - 每页大小
 * @returns 推荐工具数据
 */
async function fetchRecommendedTools(
  page: number,
  pageSize: number = PAGINATION.RECOMMENDED_PAGE_SIZE
): Promise<RecommendedToolsResponse> {
  try {
    const response = await fetch(
      `/api/tools/recommended?page=${page}&pageSize=${pageSize}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching recommended tools:", error);
    throw error;
  }
}

/**
 * 生成分页链接数组
 * @param currentPage - 当前页码
 * @param totalPages - 总页数
 * @returns 分页链接数组
 */
function generatePaginationItems(currentPage: number, totalPages: number): (number | "ellipsis")[] {
  const items: (number | "ellipsis")[] = [];
  const maxVisiblePages = 5;

  if (totalPages <= maxVisiblePages) {
    // 如果总页数小于等于最大可见页数，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      items.push(i);
    }
  } else {
    // 总是显示第一页
    items.push(1);

    if (currentPage > 3) {
      items.push("ellipsis");
    }

    // 显示当前页附近的页码
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);

    for (let i = start; i <= end; i++) {
      items.push(i);
    }

    if (currentPage < totalPages - 2) {
      items.push("ellipsis");
    }

    // 总是显示最后一页
    if (totalPages > 1) {
      items.push(totalPages);
    }
  }

  return items;
}

/**
 * 推荐工具展示组件
 * @param props - 组件属性
 * @returns 推荐工具展示JSX元素
 */
export default function RecommendedTools({
  initialTools,
  initialTotalCount,
  initialPage = 1,
}: RecommendedToolsProps) {
  const [tools, setTools] = useState<ToolWithCategory[]>(initialTools);
  const [totalCount, setTotalCount] = useState(initialTotalCount);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const totalPages = Math.ceil(totalCount / PAGINATION.RECOMMENDED_PAGE_SIZE);

  /**
   * 处理页码变化
   * @param page - 新页码
   */
  const handlePageChange = async (page: number) => {
    if (page === currentPage || page < 1 || page > totalPages) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await fetchRecommendedTools(page);
      setTools(data.tools);
      setTotalCount(data.totalCount);
      setCurrentPage(page);
    } catch (error) {
      console.error("Error changing page:", error);
      setError("加载推荐工具失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  // 如果没有推荐工具，不显示该区域
  if (initialTotalCount === 0) {
    return null;
  }

  return (
    <div className="mb-12">
      {/* 标题区域 */}
      <div className="flex items-center gap-3 mb-6">
        <div className="flex items-center gap-2">
          <Star className="h-6 w-6 text-yellow-500 fill-current" />
          <h2 className="text-2xl font-bold">推荐工具</h2>
        </div>
        <Badge variant="outline">{totalCount} 个推荐工具</Badge>
      </div>

      {/* 错误提示 */}
      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>加载失败</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* 工具列表 */}
      <div className={`${RESPONSIVE_GRID.TOOL_CARDS} ${loading ? "opacity-50" : ""}`}>
        {tools.map((tool) => {
          const toolData = transformToExtendedTool(tool);
          return (
            <div key={tool.id} className="h-full">
              <ToolCardHorizontal tool={toolData} />
            </div>
          );
        })}
      </div>

      {/* 分页控件 */}
      {totalPages > 1 && (
        <div className="mt-8 flex justify-center">
          <Pagination>
            <PaginationContent>
              {/* 上一页 */}
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(currentPage - 1);
                  }}
                  className={
                    currentPage === 1 || loading
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                >
                  上一页
                </PaginationPrevious>
              </PaginationItem>

              {/* 页码 */}
              {generatePaginationItems(currentPage, totalPages).map((item, index) => (
                <PaginationItem key={index}>
                  {item === "ellipsis" ? (
                    <PaginationEllipsis />
                  ) : (
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        handlePageChange(item);
                      }}
                      isActive={item === currentPage}
                      className={loading ? "pointer-events-none opacity-50" : "cursor-pointer"}
                    >
                      {item}
                    </PaginationLink>
                  )}
                </PaginationItem>
              ))}

              {/* 下一页 */}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    handlePageChange(currentPage + 1);
                  }}
                  className={
                    currentPage === totalPages || loading
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                >
                  下一页
                </PaginationNext>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
