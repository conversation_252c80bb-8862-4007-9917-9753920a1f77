"use client";

import LayoutContent from "@/components/LayoutContent";
import SideMenu from "@/components/SideMenu";
import ScrollToAnchorWrapper from "@/components/ScrollToAnchorWrapper";
import { Toaster } from "@/components/ui/sonner";
import { usePathname } from "next/navigation";
import type { ReactNode } from "react";
import type { MenuItem } from "@/types/global";

/**
 * 客户端组件：负责条件渲染（使用客户端路由判断路径）
 */
export default function RootLayoutClient({
  children,
  menuItems,
}: {
  children: ReactNode;
  menuItems: MenuItem[];
}) {
  const pathname = usePathname();
  const isAdminPage = pathname.startsWith("/admin");

  if (isAdminPage) {
    return (
      <>
        {children}
        <Toaster position="top-center" />
      </>
    );
  }

  return (
    <LayoutContent sideMenuComponent={<SideMenu items={menuItems} />}>
      {children}
      <ScrollToAnchorWrapper />
      <Toaster position="top-center" />
    </LayoutContent>
  );
}
