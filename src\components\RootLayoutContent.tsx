import { getMenuItems } from "@/lib/menu-utils";
import RootLayoutClient from "@/components/RootLayoutClient";
import type { ReactNode } from "react";

/**
 * 服务器组件：负责获取菜单数据并传递给客户端组件
 */
export default async function RootLayoutContent({
  children,
}: {
  children: ReactNode;
}) {
  // 在服务器端获取菜单数据
  const menuItems = await getMenuItems();

  return <RootLayoutClient menuItems={menuItems}>{children}</RootLayoutClient>;
}
