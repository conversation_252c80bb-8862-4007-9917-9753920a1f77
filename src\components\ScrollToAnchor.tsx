"use client";

import { useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

/**
 * 当 URL 中包含锚点时，自动滚动到对应的元素位置
 * 这个组件应该放在页面内容的最后，确保所有内容都已加载完成
 */
export function ScrollToAnchor() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    // 当路径或查询参数变化时，检查是否需要滚动到锚点
    const scrollToAnchor = () => {
      // 获取 URL 中的哈希部分（不包括 # 符号）
      const hash = window.location.hash.substring(1);

      if (hash) {
        // 使用多次尝试确保元素已完全加载和渲染
        let attempts = 0;
        const maxAttempts = 15; // 增加最大尝试次数
        let timeoutId: NodeJS.Timeout;

        const tryScroll = () => {
          const element = document.getElementById(hash);
          if (element) {
            // 确保元素可见并且已经渲染完成
            const rect = element.getBoundingClientRect();
            if (rect.height > 0) {
              element.scrollIntoView({
                behavior: "smooth",
                block: "start",
              });
              return;
            }
          }

          if (attempts < maxAttempts) {
            attempts++;
            timeoutId = setTimeout(tryScroll, 200); // 每200ms尝试一次
          }
        };

        // 首次尝试延迟500ms，给页面更多时间加载
        timeoutId = setTimeout(tryScroll, 500);

        // 清理函数
        return () => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }
        };
      }
    };

    const cleanup = scrollToAnchor();
    return cleanup;
  }, [pathname, searchParams]);

  return null;
}
