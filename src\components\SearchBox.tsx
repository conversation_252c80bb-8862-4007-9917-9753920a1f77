'use client';

import dynamic from 'next/dynamic';
import { Suspense } from 'react';

interface SearchBoxProps {
  placeholder?: string;
  className?: string;
}

// 动态导入 LargeSearchInput 组件，禁用 SSR
const DynamicSearchInput = dynamic<SearchBoxProps>(
  () => import('@/components/LargeSearchInput').then(mod => mod.LargeSearchInput),
  { 
    ssr: false,
    loading: () => (
      <div className="h-14 w-full bg-gray-100 rounded-xl animate-pulse"></div>
    )
  }
);

export default function SearchBox({ placeholder, className }: SearchBoxProps) {
  return (
    <div className={className}>
      <Suspense fallback={<div className="h-14 w-full bg-gray-100 rounded-xl animate-pulse"></div>}>
        <DynamicSearchInput 
          placeholder={placeholder} 
          className="w-full"
        />
      </Suspense>
    </div>
  );
}
