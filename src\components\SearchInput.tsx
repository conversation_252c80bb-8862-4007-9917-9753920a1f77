"use client";

import React, { useState, FormEvent, useEffect } from "react";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import { Search } from "lucide-react";
import { useIsClient } from "@/hooks/useIsClient";

function SearchInputContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const [searchTerm, setSearchTerm] = useState("");
  const isClient = useIsClient();

  useEffect(() => {
    if (!isClient) return;

    const query = searchParams.get("q");
    if (query) {
      setSearchTerm(query);
    } else {
      setSearchTerm("");
    }
  }, [searchParams, pathname, isClient]);

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex items-center">
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder="搜索 AI 工具..."
        className="h-10 px-3 py-2 rounded-l-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm text-gray-800 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:placeholder-gray-400 dark:focus:ring-blue-500 dark:focus:border-blue-500"
      />
      <button
        type="submit"
        className="h-10 -ml-px px-4 py-2 rounded-r-md border border-gray-300 bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm dark:bg-blue-600 dark:hover:bg-blue-700 dark:border-gray-600 dark:focus:ring-blue-500 dark:focus:border-blue-500"
      >
        <Search size={18} />
      </button>
    </form>
  );
}

// 直接导出组件，使用 useIsClient 来避免水合错误
export default function SearchInput() {
  return <SearchInputContent />;
}
