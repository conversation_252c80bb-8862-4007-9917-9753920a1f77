"use client";

import { useRouter } from "next/navigation";
import { PaginationControls } from "@/components/ui/page-size-selector";
import { buildPaginationQuery, adjustPageOnSizeChange } from "@/lib/utils/pagination.utils";
import { PAGINATION, ROUTES } from "@/lib/constants/app.constants";

interface SearchPaginationControlsProps {
  /** 当前页码 */
  currentPage: number;
  /** 当前每页大小 */
  currentPageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 搜索查询 */
  query: string;
}

/**
 * 搜索页面分页控制组件
 * 
 * @description 客户端组件，处理分页大小改变时的页面跳转
 */
export function SearchPaginationControls({
  currentPage,
  currentPageSize,
  totalCount,
  query,
}: SearchPaginationControlsProps) {
  const router = useRouter();

  const handlePageSizeChange = (newPageSize: number) => {
    // 计算调整后的页码，保持用户在相似的位置
    const adjustedPage = adjustPageOnSizeChange(
      currentPage,
      currentPageSize,
      newPageSize,
      totalCount
    );

    // 构建新的查询参数
    const newQuery = buildPaginationQuery({
      q: query,
      page: adjustedPage > 1 ? adjustedPage : undefined,
      pageSize: newPageSize !== PAGINATION.SEARCH_PAGE_SIZE ? newPageSize : undefined,
    });

    // 跳转到新的URL
    router.push(`${ROUTES.SEARCH}${newQuery}`);
  };

  return (
    <PaginationControls
      currentPage={currentPage}
      currentPageSize={currentPageSize}
      totalCount={totalCount}
      onPageSizeChange={handlePageSizeChange}
    />
  );
}
