"use client";
import React, { useState } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ChevronDown, ChevronRight } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";
import type { MenuItem } from "@/types/global";

/**
 * @interface SingleMenuItemProps
 * @property {MenuItem} item - 当前菜单项的数据。
 * @property {number} level - 当前菜单项的层级 (0 表示顶级)。
 */
interface SingleMenuItemProps {
  item: MenuItem;
  level: number;
}

/**
 * 渲染单个菜单项，支持点击展开/折叠子菜单。
 * @param {SingleMenuItemProps} props - 组件属性。
 * @returns {JSX.Element} 单个菜单项的 JSX 元素。
 */
const SingleMenuItem: React.FC<SingleMenuItemProps> = ({ item, level }) => {
  const [isOpen, setIsOpen] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const pathname = usePathname();
  const router = useRouter();

  // 根据层级计算左内边距
  const paddingLeft = `${1 + level * 1}rem`; // 级别0: 1rem, 级别1: 2rem, 以此类推。 (1rem ~ pl-4)

  // 处理点击事件，滚动到对应分类或跳转到首页
  const handleClick = (e: React.MouseEvent, href?: string) => {
    if (!href || !href.startsWith("#")) return;

    e.preventDefault();
    const targetId = href.substring(1);

    // 如果当前在首页，直接滚动到目标元素
    if (pathname === "/") {
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
        // 更新 URL 哈希，但不触发页面跳转
        window.history.pushState(null, "", href);
      }
    } else {
      // 如果不在首页，跳转到首页并带上锚点
      router.push(`/${href}`);
    }
  };

  if (hasChildren) {
    return (
      <Collapsible open={isOpen} onOpenChange={setIsOpen} className="w-full">
        <CollapsibleTrigger
          className={cn(
            "flex items-center justify-between w-full py-2 pr-4 text-left text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 group"
          )}
          style={{ paddingLeft }} // 应用动态内边距
        >
          <span className="flex items-center">
            {item.icon ? (
              <span className="mr-2 h-4 w-4">{item.icon}</span>
            ) : null}
            {item.label}
          </span>
          {isOpen ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform group-hover:text-accent-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 text-muted-foreground transition-transform group-hover:text-accent-foreground" />
          )}
        </CollapsibleTrigger>
        <CollapsibleContent className="overflow-hidden data-[state=open]:animate-collapsible-down data-[state=closed]:animate-collapsible-up">
          <div className="py-1">
            {item.children?.map((child) => (
              <SingleMenuItem key={child.id} item={child} level={level + 1} />
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    );
  }

  // 如果是分类链接（以#开头的href），使用button并处理滚动
  if (item.href?.startsWith("#")) {
    return (
      <button
        onClick={(e) => handleClick(e, item.href)}
        className={cn(
          "w-full text-left py-2 pr-4 text-sm rounded-md hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1"
        )}
        style={{ paddingLeft }}
      >
        <span className="flex items-center">
          {item.icon ? <span className="mr-2 h-4 w-4">{item.icon}</span> : null}
          {item.label}
        </span>
      </button>
    );
  }

  // 普通链接保持原样
  return (
    <Link
      href={item.href || "#"}
      className={cn(
        "block py-2 pr-4 text-sm rounded-md hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-1"
      )}
      style={{ paddingLeft }}
    >
      <span className="flex items-center">
        {item.icon ? <span className="mr-2 h-4 w-4">{item.icon}</span> : null}
        {item.label}
      </span>
    </Link>
  );
};

/**
 * @interface SideMenuProps
 * @property {MenuItem[]} items - 构成菜单的条目数组。
 * @property {string} [className] - (可选) 应用于导航根元素的额外 CSS 类名。
 */
export interface SideMenuProps {
  items: MenuItem[];
  className?: string;
}

/**
 * 网站左侧多级菜单组件。
 * 此组件接收菜单项数据并渲染一个可导航的、支持多层级的侧边栏菜单。
 *
 * @param {SideMenuProps} props - 组件的属性。
 * @returns {JSX.Element | null} 左侧菜单组件的 JSX 元素，或在没有菜单项时返回 null。
 * @example
 * const sampleMenuItems = [
 *   { id: 'home', label: '首页', href: '/' },
 *   {
 *     id: 'products',
 *     label: '产品中心',
 *     icon: <PackageIcon />, // 假设 PackageIcon 是一个 React 组件
 *     children: [
 *       { id: 'product-a', label: '产品A', href: '/products/a' },
 *       { id: 'product-b', label: '产品B', href: '/products/b' },
 *     ],
 *   },
 *   { id: 'about', label: '关于我们', href: '/about' },
 * ];
 * <SideMenu items={sampleMenuItems} className="my-custom-menu" />
 */
const SideMenu: React.FC<SideMenuProps> = ({ items, className }) => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <nav
      className={cn(
        "flex flex-col w-72 space-y-1 p-2 bg-background border-r sticky top-0 h-screen overflow-y-auto", // 宽度从 w-64 调整为 w-72, 添加了 sticky 定位
        className
      )}
      aria-label="Main Navigation"
    >
      <Link href="/" className="block">
        <h2 className="p-4 text-2xl font-bold hover:text-primary transition-colors cursor-pointer text-center">
          AI 工具导航
        </h2>
      </Link>
      {/* 添加标题 */}
      {items.map((item) => (
        <SingleMenuItem key={item.id} item={item} level={0} />
      ))}
    </nav>
  );
};

export default SideMenu;
