"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type User = {
  name?: string | null;
  email?: string | null;
  image?: string | null;
};

interface UserDropdownProps {
  session: {
    user?: User | null;
  } | null;
}

export default function UserDropdown({ session }: UserDropdownProps) {
  return (
    <div className="flex items-center space-x-2 p-2 cursor-pointer rounded-md hover:bg-gray-100">
      <Avatar className="h-8 w-8">
        {session?.user?.image && (
          <AvatarImage
            src={session.user.image}
            alt={session?.user?.name || session?.user?.email || "User"}
          />
        )}
        <AvatarFallback className="bg-gray-200 text-gray-700">
          {session?.user?.name
            ? session.user.name.charAt(0).toUpperCase()
            : session?.user?.email
            ? session.user.email.charAt(0).toUpperCase()
            : "U"}
        </AvatarFallback>
      </Avatar>
      <span className="text-sm hidden sm:inline text-gray-700">
        {session?.user?.name || session?.user?.email}
      </span>
    </div>
  );
}
