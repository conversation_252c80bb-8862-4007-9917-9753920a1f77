"use client";

import { signOut } from "next-auth/react";
import Link from "next/link";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { isAdmin } from "@/lib/auth";
import UserDropdown from "@/components/UserDropdown";
import type { Session } from "next-auth";

interface UserMenuProps {
  session: Session;
}

/**
 * 用户菜单组件 - 包含完整的下拉菜单逻辑
 * 此组件被动态导入以避免SSR水合错误
 */
export default function UserMenu({ session }: UserMenuProps) {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <button className="outline-none">
          <UserDropdown session={{ user: session.user }} />
        </button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          align="end"
          className="w-48 bg-white border border-gray-200 rounded-md shadow-lg py-1 z-50"
          sideOffset={5}
        >
          {isAdmin(session) && (
            <DropdownMenu.Item asChild>
              <Link
                href="/admin"
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 outline-none"
              >
                后台管理
              </Link>
            </DropdownMenu.Item>
          )}
          <DropdownMenu.Item
            onSelect={() => signOut()}
            className="block px-4 py-2 text-sm text-red-600 hover:bg-red-50 cursor-pointer outline-none"
          >
            注销
          </DropdownMenu.Item>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
