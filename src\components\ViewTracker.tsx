"use client";

import { useEffect, useRef } from "react";

interface ViewTrackerProps {
  /** 工具ID */
  toolId: string;
  /** 工具名称，用于分析追踪 */
  toolName?: string;
}

/**
 * 浏览次数跟踪组件
 * 在组件挂载时自动增加工具的浏览次数
 */
export default function ViewTracker({ toolId, toolName }: ViewTrackerProps) {
  const hasTracked = useRef(false);

  useEffect(() => {
    // 防止重复调用
    if (hasTracked.current) {
      return;
    }

    const incrementViews = async () => {
      try {
        const response = await fetch(`/api/tools/${toolId}/view`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (process.env.NODE_ENV === "development") {
            console.log(`Tool ${toolId} view tracked.`, data);
          }

          // 可选：触发Google Analytics事件
          if (typeof window !== "undefined" && window.gtag && toolName) {
            window.gtag("event", "tool_view", {
              event_category: "engagement",
              event_label: `${toolId}:${toolName}`,
            });
          }
        } else {
          if (process.env.NODE_ENV === "development") {
            console.error("Failed to track view:", response.statusText);
          }
        }
      } catch (error) {
        if (process.env.NODE_ENV === "development") {
          console.error("Error tracking view:", error);
        }
      }
    };

    // 延迟执行，确保页面已完全加载
    const timer = setTimeout(() => {
      incrementViews();
      hasTracked.current = true;
    }, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [toolId, toolName]);

  // 这个组件不渲染任何内容
  return null;
}

// 类型声明
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
