"use client";

import * as React from "react";
import { ChevronDownIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface AdminPaginationControlsProps {
  /** 当前页码 */
  currentPage: number;
  /** 当前每页大小 */
  currentPageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 总页数 */
  totalPages: number;
  /** 是否有下一页 */
  hasNext: boolean;
  /** 是否有上一页 */
  hasPrevious: boolean;
  /** 页面大小改变回调 */
  onPageSizeChange: (pageSize: number) => void;
  /** 页码改变回调 */
  onPageChange: (page: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

// 后台管理页面的分页大小选项
const ADMIN_PAGE_SIZE_OPTIONS = [10, 12, 20, 50, 100] as const;
const ADMIN_PAGE_SIZE_LABELS = {
  10: "10条/页",
  12: "12条/页",
  20: "20条/页", 
  50: "50条/页",
  100: "100条/页",
} as const;

/**
 * 后台管理分页控制组件
 * 
 * @description 包含分页大小选择器、分页信息和分页导航的完整控制栏
 */
export function AdminPaginationControls({
  currentPage,
  currentPageSize,
  totalCount,
  totalPages,
  hasNext,
  hasPrevious,
  onPageSizeChange,
  onPageChange,
  disabled = false,
  className,
}: AdminPaginationControlsProps) {
  // 过滤掉大于总记录数的选项（当总记录数较少时）
  const availableOptions = ADMIN_PAGE_SIZE_OPTIONS.filter(
    (size) => size <= Math.max(totalCount, currentPageSize)
  );

  // 如果没有数据，不显示分页控制
  if (totalCount === 0) {
    return null;
  }

  const startIndex = (currentPage - 1) * currentPageSize + 1;
  const endIndex = Math.min(currentPage * currentPageSize, totalCount);

  return (
    <div className={cn(
      "flex flex-col sm:flex-row items-center justify-between gap-4 py-4",
      className
    )}>
      {/* 左侧：分页大小选择器 */}
      <div className="flex items-center gap-2">
        {availableOptions.length > 1 && (
          <>
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              每页显示
            </span>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={disabled}
                  className="h-8 gap-1 text-sm min-w-[80px]"
                >
                  <span>{currentPageSize}</span>
                  <ChevronDownIcon className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              
              <DropdownMenuContent align="start" className="min-w-[120px]">
                {availableOptions.map((size) => (
                  <DropdownMenuItem
                    key={size}
                    onClick={() => onPageSizeChange(size)}
                    className={cn(
                      "cursor-pointer",
                      size === currentPageSize && "bg-accent"
                    )}
                  >
                    <span className="flex-1">
                      {ADMIN_PAGE_SIZE_LABELS[size as keyof typeof ADMIN_PAGE_SIZE_LABELS]}
                    </span>
                    {size === currentPageSize && (
                      <span className="text-xs text-muted-foreground ml-2">✓</span>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
            
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              条记录
            </span>
          </>
        )}
      </div>

      {/* 中间：分页信息 */}
      <div className="text-sm text-muted-foreground">
        显示第 <span className="font-medium text-foreground">{startIndex}</span> - <span className="font-medium text-foreground">{endIndex}</span> 项，
        共 <span className="font-medium text-foreground">{totalCount}</span> 条记录
        {totalPages > 1 && (
          <>
            ，第 <span className="font-medium text-foreground">{currentPage}</span> / <span className="font-medium text-foreground">{totalPages}</span> 页
          </>
        )}
      </div>

      {/* 右侧：分页导航 */}
      {totalPages > 1 && (
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={!hasPrevious || disabled}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">上一页</span>
          </Button>
          
          <div className="text-sm text-muted-foreground min-w-[100px] text-center">
            第 {currentPage} / {totalPages} 页
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={!hasNext || disabled}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">下一页</span>
          </Button>
        </div>
      )}
    </div>
  );
}

/**
 * 简化版分页信息显示组件
 * 
 * @description 仅显示分页信息，不包含控制器
 */
interface AdminPaginationInfoProps {
  /** 当前页码 */
  currentPage: number;
  /** 每页大小 */
  pageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 总页数 */
  totalPages: number;
  /** 自定义类名 */
  className?: string;
}

export function AdminPaginationInfo({
  currentPage,
  pageSize,
  totalCount,
  totalPages,
  className,
}: AdminPaginationInfoProps) {
  if (totalCount === 0) {
    return (
      <div className={cn("text-center text-sm text-muted-foreground py-4", className)}>
        暂无数据
      </div>
    );
  }

  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);

  return (
    <div className={cn("text-center text-sm text-muted-foreground py-2", className)}>
      显示第 <span className="font-medium text-foreground">{startIndex}</span> - <span className="font-medium text-foreground">{endIndex}</span> 项，
      共 <span className="font-medium text-foreground">{totalCount}</span> 条记录
      {totalPages > 1 && (
        <>
          ，第 <span className="font-medium text-foreground">{currentPage}</span> / <span className="font-medium text-foreground">{totalPages}</span> 页
        </>
      )}
    </div>
  );
}
