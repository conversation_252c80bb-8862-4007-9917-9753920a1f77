"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { AdminPaginationControls } from "./AdminPaginationControls";

export interface Column<T> {
  key: keyof T;
  title: string;
  render?: (record: T) => React.ReactNode;
}

export interface PaginationData {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface AdminTableProps<T> {
  columns: Column<T>[];
  data: T[];
  className?: string;
  pagination?: PaginationData;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  loading?: boolean;
}

export function AdminTable<T extends Record<string, any>>({
  columns,
  data,
  className,
  pagination,
  onPageChange,
  onPageSizeChange,
  loading = false,
}: AdminTableProps<T>) {
  return (
    <div className="space-y-4">
      <div className="rounded-md border">
        <Table className={className}>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={String(column.key)}>{column.title}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((record, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={String(column.key)}>
                    {column.render
                      ? column.render(record)
                      : String(record[column.key])}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <AdminPaginationControls
          currentPage={pagination.page}
          currentPageSize={pagination.pageSize}
          totalCount={pagination.total}
          totalPages={pagination.totalPages}
          hasNext={pagination.hasNext}
          hasPrevious={pagination.hasPrevious}
          onPageChange={(page) => onPageChange?.(page)}
          onPageSizeChange={(pageSize) => onPageSizeChange?.(pageSize)}
          disabled={loading}
        />
      )}
    </div>
  );
}
