"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export interface CategoryOption {
  id: string;
  name: string;
}

interface CategorySearchSelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  categories: CategoryOption[];
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * 支持搜索的分类选择组件
 * 使用 Combobox 模式实现模糊搜索功能
 */
export function CategorySearchSelect({
  value,
  onValueChange,
  categories,
  placeholder = "选择分类",
  required = false,
  disabled = false,
  className,
}: CategorySearchSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");

  // 根据搜索值过滤分类，支持中文搜索
  const filteredCategories = React.useMemo(() => {
    if (!searchValue.trim()) return categories;

    const searchTerm = searchValue.toLowerCase().trim();
    return categories.filter((category) => {
      const categoryName = category.name.toLowerCase();
      // 支持拼音首字母和完整匹配
      return categoryName.includes(searchTerm);
    });
  }, [categories, searchValue]);

  // 获取当前选中的分类
  const selectedCategory = React.useMemo(() => {
    return categories.find((category) => category.id === value);
  }, [categories, value]);

  // 处理选择
  const handleSelect = React.useCallback(
    (categoryId: string) => {
      onValueChange?.(categoryId);
      setOpen(false);
      setSearchValue(""); // 清空搜索
    },
    [onValueChange]
  );

  // 处理搜索输入变化
  const handleSearchChange = React.useCallback((search: string) => {
    setSearchValue(search);
  }, []);

  // 高亮搜索结果
  const highlightText = React.useCallback(
    (text: string, searchTerm: string) => {
      if (!searchTerm.trim()) return text;

      const regex = new RegExp(
        `(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")})`,
        "gi"
      );
      const parts = text.split(regex);

      return parts.map((part, index) =>
        regex.test(part) ? (
          <mark
            key={index}
            className="bg-yellow-200 text-yellow-900 rounded px-0.5"
          >
            {part}
          </mark>
        ) : (
          part
        )
      );
    },
    []
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between",
            !selectedCategory && "text-muted-foreground",
            className
          )}
          disabled={disabled}
        >
          {selectedCategory ? selectedCategory.name : placeholder}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-[var(--radix-popover-trigger-width)] p-0"
        align="start"
        sideOffset={4}
        onWheel={(e) => {
          // 阻止滚轮事件冒泡到父元素
          e.stopPropagation();
        }}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="搜索分类..."
            value={searchValue}
            onValueChange={handleSearchChange}
            className="h-9"
          />
          <CommandList
            className="max-h-[200px] overflow-y-auto scroll-smooth overscroll-contain"
            style={{
              // 确保滚动容器有正确的样式
              scrollBehavior: "smooth",
              WebkitOverflowScrolling: "touch",
            }}
            onWheel={(e) => {
              // 确保滚轮事件能够正常工作
              e.stopPropagation();
            }}
          >
            <CommandEmpty>未找到匹配的分类</CommandEmpty>
            <CommandGroup>
              {filteredCategories.map((category) => (
                <CommandItem
                  key={category.id}
                  value={category.name}
                  onSelect={() => handleSelect(category.id)}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === category.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="flex-1">
                    {highlightText(category.name, searchValue)}
                  </span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
