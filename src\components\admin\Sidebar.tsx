"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { LayoutDashboard, FolderTree, Wrench, Eye } from "lucide-react";

const navItems = [
  {
    title: "仪表盘",
    href: "/admin/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "分类管理",
    href: "/admin/categories",
    icon: FolderTree,
  },
  {
    title: "工具管理",
    href: "/admin/tools",
    icon: Wrench,
  },
  {
    title: "浏览统计",
    href: "/admin/tool-views",
    icon: Eye,
  },
];

export function Sidebar() {
  const pathname = usePathname();

  return (
    <nav className="h-screen w-64 border-r bg-gray-50/40">
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/admin" className="font-semibold text-gray-900">
          管理后台
        </Link>
      </div>
      <div className="px-3 py-4">
        {navItems.map((item) => {
          const isActive = pathname === item.href;
          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900",
                isActive ? "bg-gray-100 text-gray-900" : "hover:bg-gray-100"
              )}
            >
              <item.icon className="h-5 w-5" />
              {item.title}
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
