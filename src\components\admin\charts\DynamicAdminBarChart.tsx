"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import type { CategoryDistribution } from "@/lib/data/dashboard";

interface DynamicBarChartProps {
  data: CategoryDistribution[];
}

export default function DynamicAdminBarChart({ data }: DynamicBarChartProps) {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="name"
          angle={-45}
          textAnchor="end"
          height={70}
          interval={0}
          tick={{ fontSize: 12 }}
        />
        <YAxis />
        <Tooltip />
        <Legend />
        <Bar
          dataKey="count"
          name="工具数量"
          fill="var(--chart-1, #2563eb)" // Use CSS variable from shadcn/ui chart
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
