"use client";

import Link from "next/link";
import Image from "next/image";
import { Tool } from "@prisma/client";
import { Badge } from "./badge";

interface ToolCardHorizontalProps {
  tool: Tool & {
    category?: {
      id: string;
      name: string;
      slug: string;  // 现在 slug 是必填的
    } | null | undefined;
    // 添加其他可能的属性
    [key: string]: any;
  };
}

export default function ToolCardHorizontal({ tool }: ToolCardHorizontalProps) {
  return (
    <div className="h-full bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
      <Link href={`/tool/${tool.id}`} className="block h-full flex flex-col">
        <div className="p-4 flex-grow">
          <div className="flex items-start space-x-4">
            {/* 左侧：图标 */}
            <div className="flex-shrink-0">
              {tool.iconUrl ? (
                <div className="w-16 h-16 relative rounded">
                  <Image
                    src={tool.iconUrl}
                    alt={`${tool.name} 图标`}
                    fill
                    sizes="64px"
                    className="rounded object-cover"
                  />
                </div>
              ) : (
                <div className="w-16 h-16 bg-gray-200 flex items-center justify-center rounded">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth={1.5}
                    stroke="currentColor"
                    className="w-8 h-8 text-gray-400"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.158 0a.225.225 0 0 1 .225.225v.008a.225.225 0 0 1-.225.225H12.9a.225.225 0 0 1-.225-.225V8.475a.225.225 0 0 1 .225-.225h.008Z"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* 右侧：文本内容 */}
            <div className="flex-grow min-w-0">
              <h3
                className="text-base font-semibold text-gray-800 line-clamp-1"
                title={tool.name}
              >
                {tool.name}
              </h3>
              <p
                className="text-sm text-gray-600 line-clamp-2 mt-1"
                title={tool.description}
              >
                {tool.description}
              </p>
            </div>
          </div>
        </div>

        {/* 底部：标签和分类 */}
        {/* <div className="px-4 pb-3 pt-2 bg-gray-50 border-t border-gray-100">
          <div className="flex justify-between items-center">
            {tool.category && (
              <Badge variant="secondary" className="text-xs">
                {tool.category.name}
              </Badge>
            )}
            <div className="flex flex-wrap gap-1">
              {tool.tags?.slice(0, 2).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div> */}
      </Link>
    </div>
  );
}
