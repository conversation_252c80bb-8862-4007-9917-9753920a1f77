"use client";

import { useCallback, useState, useRef, useEffect, ChangeEvent } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Upload, X, Loader2 } from "lucide-react";
import { calculateClientFileHash } from "@/lib/utils/hash.utils";

// 从URL获取文件扩展名
function getFileExtensionFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const lastDotIndex = pathname.lastIndexOf(".");
    if (lastDotIndex === -1) return "";
    return pathname.substring(lastDotIndex).toLowerCase();
  } catch (e) {
    return "";
  }
}

interface ImageUploadProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  required?: boolean;
  className?: string;
  accept?: string;
  maxSizeMB?: number;
}

export function ImageUpload({
  value,
  onChange,
  label = "图片",
  // 不再使用 required 属性，改为在父组件中处理必填逻辑
  className = "",
  accept = "image/*",
  maxSizeMB = 8,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(value || null);

  // 当value变化时更新预览URL
  useEffect(() => {
    console.log("ImageUpload value changed:", value);
    if (value) {
      setPreviewUrl(value);
    } else {
      setPreviewUrl(null);
    }
  }, [value]);

  const handleFileChange = useCallback(
    async (e: ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      // 检查文件类型
      if (!file.type.startsWith("image/")) {
        toast.error("请上传图片文件");
        return;
      }

      // 检查文件大小
      if (file.size > maxSizeMB * 1024 * 1024) {
        toast.error(`图片大小不能超过 ${maxSizeMB}MB`);
        return;
      }

      try {
        setIsUploading(true);

        // 创建临时预览
        const preview = URL.createObjectURL(file);
        setPreviewUrl(preview);

        // 准备上传文件
        const formData = new FormData();
        formData.append("file", file);

        // 上传文件到服务器
        const response = await fetch("/api/upload", {
          method: "POST",
          body: formData,
        });

        const result = await response.json();

        // 清理预览 URL
        if (preview) {
          URL.revokeObjectURL(preview);
        }

        if (result.success && result.url) {
          // 使用服务器返回的URL
          const imageUrl = result.url;
          console.log("上传成功，图片URL:", imageUrl);

          // 显示统一的成功提示
          toast.success(result.message || "上传成功");

          // 确保传递的是字符串
          onChange(imageUrl);
          // 设置预览为服务器返回的URL
          setPreviewUrl(imageUrl);
        } else {
          toast.error(result?.message || "上传失败");
          // 如果上传失败，清除预览和URL
          setPreviewUrl(null);
          onChange("");
        }
      } catch (error) {
        console.error("上传图片失败:", error);
        toast.error("上传图片失败，请重试");
        setPreviewUrl(null);
        onChange("");
      } finally {
        setIsUploading(false);
        // 重置文件输入，允许重复选择同一文件
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    },
    [onChange, maxSizeMB]
  );

  const handleRemoveImage = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setPreviewUrl(null);
      onChange("");
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [onChange]
  );

  // 确保在编辑模式下正确显示预览
  useEffect(() => {
    console.log("Initial load value:", value);
    if (value) {
      setPreviewUrl(value);
    }
  }, [value]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const url = e.target.value.trim();
      console.log("URL input changed:", url);
      setPreviewUrl(url || null);
      // 触发onChange，传递URL
      onChange(url);
    },
    [onChange]
  );

  // 清理预览URL
  useEffect(() => {
    return () => {
      if (previewUrl && previewUrl.startsWith("blob:")) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  return (
    <div className={`space-y-2 ${className}`}>
      {label && <Label>{label}</Label>}

      <div className="flex flex-col space-y-2">
        {/* 图片预览 */}
        {previewUrl && (
          <div className="relative w-32 h-32 border rounded-md overflow-hidden">
            <img
              src={previewUrl}
              alt="预览"
              className="w-full h-full object-cover"
              onError={() => {
                // 如果图片加载失败，清除预览
                setPreviewUrl(null);
                onChange("");
                toast.error("图片加载失败，请检查URL或重新上传");
              }}
            />
            <button
              type="button"
              onClick={handleRemoveImage}
              className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
              aria-label="移除图片"
            >
              <X className="w-3 h-3" />
            </button>
          </div>
        )}

        {/* URL输入 */}
        <div className="flex space-x-2">
          <Input
            type="text"
            value={value}
            onChange={handleInputChange}
            placeholder="输入图片URL"
            className="flex-1"
          />
          <span className="text-sm text-gray-500 self-center">或</span>
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
            id="file-upload"
          />
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="whitespace-nowrap"
          >
            {isUploading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Upload className="w-4 h-4 mr-2" />
            )}
            {isUploading ? "处理中..." : "选择文件"}
          </Button>
        </div>
        <p className="text-xs text-gray-500">
          支持 JPG, PNG, WebP 格式，最大 {maxSizeMB}MB
        </p>
      </div>
    </div>
  );
}
