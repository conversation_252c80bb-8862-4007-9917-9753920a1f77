"use client";

import * as React from "react";
import { ChevronDownIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PAGINATION } from "@/lib/constants/app.constants";

interface PageSizeSelectorProps {
  /** 当前每页大小 */
  currentPageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 页面大小改变回调 */
  onPageSizeChange: (pageSize: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

/**
 * 分页大小选择器组件
 * 
 * @description 允许用户选择每页显示的记录数量
 * @example
 * ```tsx
 * <PageSizeSelector
 *   currentPageSize={50}
 *   totalCount={100}
 *   onPageSizeChange={(size) => console.log(size)}
 * />
 * ```
 */
export function PageSizeSelector({
  currentPageSize,
  totalCount,
  onPageSizeChange,
  disabled = false,
  className,
}: PageSizeSelectorProps) {
  // 过滤掉大于总记录数的选项（当总记录数较少时）
  const availableOptions = PAGINATION.PAGE_SIZE_OPTIONS.filter(
    (size) => size <= Math.max(totalCount, currentPageSize)
  );

  // 如果只有一个选项或总记录数为0，不显示选择器
  if (availableOptions.length <= 1 || totalCount === 0) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-sm text-muted-foreground whitespace-nowrap">
        每页显示
      </span>
      
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={disabled}
            className="h-8 gap-1 text-sm"
          >
            <span>{currentPageSize}</span>
            <ChevronDownIcon className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="start" className="min-w-[120px]">
          {availableOptions.map((size) => (
            <DropdownMenuItem
              key={size}
              onClick={() => onPageSizeChange(size)}
              className={cn(
                "cursor-pointer",
                size === currentPageSize && "bg-accent"
              )}
            >
              <span className="flex-1">
                {PAGINATION.PAGE_SIZE_LABELS[size as keyof typeof PAGINATION.PAGE_SIZE_LABELS]}
              </span>
              {size === currentPageSize && (
                <span className="text-xs text-muted-foreground ml-2">✓</span>
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
      
      <span className="text-sm text-muted-foreground whitespace-nowrap">
        条记录
      </span>
    </div>
  );
}

/**
 * 分页信息显示组件
 * 
 * @description 显示当前分页的详细信息，包括范围和总数
 */
interface PaginationInfoProps {
  /** 当前页码 */
  currentPage: number;
  /** 每页大小 */
  pageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 自定义类名 */
  className?: string;
}

export function PaginationInfo({
  currentPage,
  pageSize,
  totalCount,
  className,
}: PaginationInfoProps) {
  if (totalCount === 0) {
    return (
      <div className={cn("text-center text-sm text-muted-foreground", className)}>
        暂无数据
      </div>
    );
  }

  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);
  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <div className={cn("text-center text-sm text-muted-foreground", className)}>
      显示第 <span className="font-medium text-foreground">{startIndex}</span> - <span className="font-medium text-foreground">{endIndex}</span> 项，
      共 <span className="font-medium text-foreground">{totalCount}</span> 个工具
      {totalPages > 1 && (
        <>
          ，第 <span className="font-medium text-foreground">{currentPage}</span> / <span className="font-medium text-foreground">{totalPages}</span> 页
        </>
      )}
    </div>
  );
}

/**
 * 完整的分页控制栏组件
 * 
 * @description 包含分页大小选择器和分页信息的完整控制栏
 */
interface PaginationControlsProps {
  /** 当前页码 */
  currentPage: number;
  /** 当前每页大小 */
  currentPageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 页面大小改变回调 */
  onPageSizeChange: (pageSize: number) => void;
  /** 是否禁用 */
  disabled?: boolean;
  /** 自定义类名 */
  className?: string;
}

export function PaginationControls({
  currentPage,
  currentPageSize,
  totalCount,
  onPageSizeChange,
  disabled = false,
  className,
}: PaginationControlsProps) {
  return (
    <div className={cn(
      "flex flex-col sm:flex-row items-center justify-between gap-4 mt-4",
      className
    )}>
      {/* 分页大小选择器 */}
      <div className="flex items-center">
        <PageSizeSelector
          currentPageSize={currentPageSize}
          totalCount={totalCount}
          onPageSizeChange={onPageSizeChange}
          disabled={disabled}
        />
      </div>
      
      {/* 分页信息 */}
      <PaginationInfo
        currentPage={currentPage}
        pageSize={currentPageSize}
        totalCount={totalCount}
      />
    </div>
  );
}
