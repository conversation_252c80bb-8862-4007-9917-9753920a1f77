"use client";

import * as React from "react";
import * as Collapsible from "@radix-ui/react-collapsible";
import { cn } from "@/lib/utils";
import { ChevronRight, Edit3, Trash2, PlusCircle } from "lucide-react";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from "@dnd-kit/sortable";

// 类型定义
export interface TreeItem {
  id: string;
  label: string;
  icon?: string;
  children?: TreeItem[];
}

export interface TreeProps {
  items: TreeItem[];
  onOrderChange: (items: TreeItem[]) => void;
  onEdit?: (item: TreeItem) => void;
  onDelete?: (item: TreeItem) => void;
  onAddChild?: (item: TreeItem) => void;
}

// 工具函数：扁平化树结构
const flattenTree = (items: TreeItem[]): string[] => {
  return items.reduce<string[]>((acc, item) => {
    acc.push(item.id);
    if (item.children?.length) {
      acc.push(...flattenTree(item.children));
    }
    return acc;
  }, []);
};

// 工具函数：重新排序树结构
const reorderItems = (
  items: TreeItem[],
  sourceId: string,
  targetId: string
): TreeItem[] => {
  // 深拷贝原始数据
  const cloneItems = (items: TreeItem[]): TreeItem[] => {
    return items.map((item) => ({
      ...item,
      children: item.children ? cloneItems(item.children) : undefined,
    }));
  };

  const newItems = cloneItems(items);

  // 查找源项和目标项
  const findItemAndParent = (
    items: TreeItem[],
    id: string,
    parent: TreeItem | null = null
  ): {
    item: TreeItem;
    parent: TreeItem | null;
    siblings: TreeItem[];
  } | null => {
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.id === id) {
        return { item, parent, siblings: items };
      }
      if (item.children) {
        const result = findItemAndParent(item.children, id, item);
        if (result) return result;
      }
    }
    return null;
  };

  const sourceResult = findItemAndParent(newItems, sourceId);
  const targetResult = findItemAndParent(newItems, targetId);

  if (!sourceResult || !targetResult) return items;

  // 从原位置移除源项
  const sourceIndex = sourceResult.siblings.findIndex(
    (item) => item.id === sourceId
  );
  const [movedItem] = sourceResult.siblings.splice(sourceIndex, 1);

  // 插入到目标位置
  const targetIndex = targetResult.siblings.findIndex(
    (item) => item.id === targetId
  );

  // 如果目标在同一个父级下，需要调整插入位置
  if (
    sourceResult.parent?.id === targetResult.parent?.id &&
    sourceIndex < targetIndex
  ) {
    targetResult.siblings.splice(targetIndex, 0, movedItem);
  } else {
    targetResult.siblings.splice(targetIndex + 1, 0, movedItem);
  }

  return newItems;
};

// TreeItem 组件接口
interface TreeItemComponentProps {
  item: TreeItem;
  depth?: number;
  onEdit?: (item: TreeItem) => void;
  onDelete?: (item: TreeItem) => void;
  onAddChild?: (item: TreeItem) => void;
  defaultOpen?: boolean;
}

// TreeItem 组件
const TreeItemComponent = ({
  item,
  depth = 0,
  onEdit,
  onDelete,
  onAddChild,
  defaultOpen = false,
}: TreeItemComponentProps) => {
  const [isOpen, setIsOpen] = React.useState(defaultOpen);
  const hasChildren = Boolean(item.children?.length);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: item.id,
  });

  const [isClient, setIsClient] = React.useState(false);

  React.useEffect(() => {
    setIsClient(true);
  }, []);

  const style: React.CSSProperties = React.useMemo(
    () => ({
      transform: transform
        ? `translate3d(${transform.x}px, ${transform.y}px, 0)`
        : undefined,
      transition: isClient ? transition : "none",
      opacity: isDragging ? 0.5 : 1,
    }),
    [transform, transition, isDragging, isClient]
  );

  // 在服务器端渲染时返回 null 或加载状态
  if (!isClient) {
    return null;
  }

  return (
    <div ref={setNodeRef} style={style}>
      <Collapsible.Root
        open={isOpen}
        onOpenChange={setIsOpen}
        suppressHydrationWarning
      >
        <div
          className={cn(
            "group flex items-center py-2 px-2 rounded-lg hover:bg-accent justify-between cursor-pointer",
            depth > 0 && "ml-6"
          )}
          onClick={() => setIsOpen(!isOpen)}
        >
          <div
            className="flex items-center gap-2"
            {...attributes}
            {...listeners}
          >
            {hasChildren && (
              <Collapsible.Trigger asChild>
                <ChevronRight
                  className={cn(
                    "h-4 w-4 shrink-0 transition-transform duration-200",
                    isOpen && "transform rotate-90"
                  )}
                />
              </Collapsible.Trigger>
            )}
            {item.icon && (
              <img
                src={item.icon}
                alt=""
                width={16}
                height={16}
                loading="eager"
                decoding="sync"
                fetchPriority="high"
                className="w-4 h-4 mr-2 flex-shrink-0"
              />
            )}
            <span className="text-sm">{item.label}</span>
          </div>
          <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            {onAddChild && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onAddChild(item);
                }}
                className="p-1 hover:bg-accent-foreground/10 rounded text-primary"
                title="添加子分类"
              >
                <PlusCircle className="h-4 w-4" />
              </button>
            )}
            {onEdit && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(item);
                }}
                className="p-1 hover:bg-accent-foreground/10 rounded"
                title="编辑分类"
              >
                <Edit3 className="h-4 w-4" />
              </button>
            )}
            {onDelete && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(item);
                }}
                className="p-1 hover:bg-accent-foreground/10 rounded text-destructive"
                title="删除分类"
              >
                <Trash2 className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
        {hasChildren && (
          <Collapsible.Content>
            <TreeBranch
              key={`branch-${item.id}`}
              items={item.children!}
              depth={depth + 1}
              defaultOpen={defaultOpen}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          </Collapsible.Content>
        )}
      </Collapsible.Root>
    </div>
  );
};

// TreeBranch 组件
interface TreeBranchProps {
  items: TreeItem[];
  depth?: number;
  onEdit?: (item: TreeItem) => void;
  onDelete?: (item: TreeItem) => void;
  onAddChild?: (item: TreeItem) => void;
  defaultOpen?: boolean;
}

const TreeBranch = ({
  items,
  depth = 0,
  onEdit,
  onDelete,
  onAddChild,
  defaultOpen,
}: TreeBranchProps) => {
  return (
    <SortableContext
      items={flattenTree(items)}
      strategy={verticalListSortingStrategy}
    >
      {items.map((item) => (
        <TreeItemComponent
          key={item.id}
          item={item}
          depth={depth}
          onEdit={onEdit}
          onDelete={onDelete}
          onAddChild={onAddChild}
          defaultOpen={defaultOpen}
        />
      ))}
    </SortableContext>
  );
};

// Tree 根组件
export function Tree({
  items,
  onOrderChange,
  onEdit,
  onDelete,
  onAddChild,
}: TreeProps) {
  const [activeId, setActiveId] = React.useState<string | null>(null);

  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));

  const handleDragStart = React.useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  }, []);

  const handleDragEnd = React.useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (over && active.id !== over.id) {
        const newItems = reorderItems(
          items,
          active.id as string,
          over.id as string
        );
        onOrderChange(newItems);
      }

      setActiveId(null);
    },
    [items, onOrderChange]
  );

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <TreeBranch
        items={items}
        onEdit={onEdit}
        onDelete={onDelete}
        onAddChild={onAddChild}
        defaultOpen={false}
      />
      <DragOverlay dropAnimation={null}>
        {activeId && (
          <div
            className="py-2 px-4 bg-background rounded-lg border shadow-lg"
            style={{
              cursor: "grabbing",
              opacity: 0.8,
              transform: "scale(1.02)",
            }}
          >
            {items.find((item) => item.id === activeId)?.label}
          </div>
        )}
      </DragOverlay>
    </DndContext>
  );
}
