/**
 * 网站分析和性能监控工具
 */

// Google Analytics 配置
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

// 页面浏览事件
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID, {
      page_path: url,
    });
  }
};

// 自定义事件
export const event = ({
  action,
  category,
  label,
  value,
}: {
  action: string;
  category: string;
  label?: string;
  value?: number;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// 性能指标监控
export const reportWebVitals = (metric: any) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      event_category: 'Web Vitals',
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      event_label: metric.id,
      non_interaction: true,
    });
  }
};

// 搜索事件追踪
export const trackSearch = (searchTerm: string, resultCount: number) => {
  event({
    action: 'search',
    category: 'engagement',
    label: searchTerm,
    value: resultCount,
  });
};

// 工具点击事件追踪
export const trackToolClick = (toolId: string, toolName: string) => {
  event({
    action: 'tool_click',
    category: 'engagement',
    label: `${toolId}:${toolName}`,
  });
};

// 分类浏览事件追踪
export const trackCategoryView = (categoryId: string, categoryName: string) => {
  event({
    action: 'category_view',
    category: 'navigation',
    label: `${categoryId}:${categoryName}`,
  });
};

// 类型声明
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
