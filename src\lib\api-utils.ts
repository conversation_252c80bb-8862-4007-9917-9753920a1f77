import { NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { AuthError } from "@/lib/auth";
import { ROLES, API_CODES, getApiMessage } from "./enums";

/**
 * 统一的 API 响应类型
 * @template T 响应数据的类型
 */
export type ApiResponse<T = unknown> = {
  /** 请求是否成功 */
  success: boolean;
  /** 业务状态码 */
  code: string;
  /** 提示信息 */
  message?: string;
  /** 响应数据 */
  data?: T;
  /** 其他扩展字段 */
  [key: string]: unknown;
};

/**
 * 成功的响应
 * @param data 响应数据
 * @param message 成功提示信息，可选，不传则使用默认消息
 * @param code 业务状态码，默认为 'SUCCESS'
 */
export function successResponse<T>(
  data: T,
  message?: string,
  code: keyof typeof API_CODES = "SUCCESS"
): NextResponse<ApiResponse<T>> {
  const responseCode = API_CODES[code] || API_CODES.SUCCESS;
  const responseMessage = message || responseCode.message;

  return NextResponse.json({
    success: true,
    code: responseCode.code,
    message: responseMessage,
    data,
  });
}

/**
 * 错误的响应
 * @param error 错误对象或错误信息
 * @param code 业务状态码，默认为 'INTERNAL_ERROR'
 * @param message 自定义错误信息，不传则使用默认消息
 */
export function errorResponse(
  error: Error | AuthError | string,
  code: keyof typeof API_CODES = API_CODES.INTERNAL_ERROR.code,
  message?: string
): NextResponse<ApiResponse> {
  const errorObj = typeof error === "string" ? new Error(error) : error;
  const errorCode = errorObj instanceof AuthError ? errorObj.code : code;

  // 获取错误消息
  let errorMessage: string;
  if (message) {
    errorMessage = message;
  } else if (errorObj instanceof AuthError) {
    errorMessage = errorObj.message;
  } else if (typeof error === "string") {
    errorMessage = error;
  } else {
    errorMessage = getApiMessage(code);
  }

  const responseCode =
    API_CODES[errorCode as keyof typeof API_CODES] || API_CODES.INTERNAL_ERROR;

  return NextResponse.json({
    success: false,
    code: responseCode.code,
    message: errorMessage,
  });
}

/**
 * 参数错误响应
 * @param message 自定义错误信息，可选
 */
export function badRequestResponse(
  message?: string
): NextResponse<ApiResponse> {
  return errorResponse(
    message || API_CODES.BAD_REQUEST.message,
    API_CODES.BAD_REQUEST.code,
    message
  );
}

/**
 * 未授权响应
 * @param message 自定义错误信息，可选
 */
export function unauthorizedResponse(
  message?: string
): NextResponse<ApiResponse> {
  return errorResponse(
    message || API_CODES.UNAUTHORIZED.message,
    API_CODES.UNAUTHORIZED.code,
    message
  );
}

/**
 * 权限不足响应
 * @param message 自定义错误信息，可选
 */
export function forbiddenResponse(message?: string): NextResponse<ApiResponse> {
  return errorResponse(
    message || API_CODES.FORBIDDEN.message,
    API_CODES.FORBIDDEN.code,
    message
  );
}

/**
 * 资源不存在响应
 * @param message 自定义错误信息，可选
 */
export function notFoundResponse(message?: string): NextResponse<ApiResponse> {
  return errorResponse(
    message || API_CODES.NOT_FOUND.message,
    API_CODES.NOT_FOUND.code,
    message
  );
}

/**
 * 验证错误响应
 * @param message 自定义错误信息，可选
 */
export function validationErrorResponse(
  message?: string
): NextResponse<ApiResponse> {
  return errorResponse(
    message || API_CODES.VALIDATION_ERROR.message,
    "VALIDATION_ERROR" as keyof typeof API_CODES,
    message
  );
}

// 验证管理员权限的中间件函数
export async function validateAdminPermission() {
  const session = await auth();

  if (!session?.user) {
    throw new AuthError(
      API_CODES.INVALID_TOKEN.message,
      API_CODES.INVALID_TOKEN.code
    );
  }

  // 使用 ROLES.ADMIN 进行比较
  if (!session.user.roles?.includes(ROLES.ADMIN)) {
    console.warn(
      `Permission denied for user ${
        session.user.email || session.user.id
      }. Roles: ${session.user.roles?.join(", ")}. Expected: ${ROLES.ADMIN}`
    );
    throw new AuthError(
      API_CODES.PERMISSION_DENIED.message,
      API_CODES.PERMISSION_DENIED.code
    );
  }
}
