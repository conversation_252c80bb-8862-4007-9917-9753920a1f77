import NextAuth, { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";
import Logto from "next-auth/providers/logto";
import { ROLES, API_CODES } from "./enums";

// 自定义错误类型
export class AuthError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = "AuthError";
  }
}

export class InvalidTokenError extends AuthError {
  constructor(message = "无效的令牌") {
    super(message, API_CODES.INVALID_TOKEN.code);
  }
}

export class PermissionDeniedError extends AuthError {
  constructor(message = "权限不足") {
    super(message, API_CODES.PERMISSION_DENIED.code);
  }
}

export class TooManyRequestsError extends AuthError {
  constructor(message = "请求过于频繁，请稍后再试") {
    super(message, "TOO_MANY_REQUESTS");
  }
}

// 扩展 Session 类型
declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      email?: string | null;
      name?: string | null;
      image?: string | null;
      roles: string[];
    } & DefaultSession["user"];
    accessToken?: string;
  }
}

// 扩展 JWT 类型
declare module "next-auth/jwt" {
  interface JWT {
    userId?: string;
    roles?: string[];
    accessToken?: string;
  }
}

/**
 * 从 token 中解析角色信息
 * @param idToken - ID令牌数据
 * @returns 解析后的角色信息
 * @throws {InvalidTokenError} 当令牌无效或解析失败时抛出
 */
const parseTokenClaims = (
  idToken: Record<string, unknown>
): { roles: string[] } => {
  try {
    if (!idToken) {
      throw new InvalidTokenError("令牌数据为空");
    }

    const roles = Array.isArray(idToken?.roles) ? idToken.roles : [];

    if (!roles.length) {
      console.warn("令牌缺少角色信息", { roles });
    }

    return { roles };
  } catch (error) {
    console.error("令牌解析失败", { error, idToken });
    throw new InvalidTokenError("令牌解析失败");
  }
};

/**
 * 检查用户是否为管理员
 * @param session 用户会话信息
 * @returns boolean 是否具有管理员权限
 */
export function isAdmin(session?: import("next-auth").Session | null): boolean {
  if (!session?.user?.roles) return false;
  return session.user.roles.includes(ROLES.ADMIN);
}

export const { handlers, signIn, signOut, auth } = NextAuth({
  providers: [
    Logto({
      // 根据需要配置其他选项
      authorization: {
        params: {
          scope: "openid profile email roles",
        },
      },
    }),
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      console.info("JWT回调开始", { account: account?.provider });

      if (account?.id_token) {
        try {
          // 初始登录时保存必要信息到 token
          token.accessToken = account.access_token;
          token.userId = profile?.sub as string;

          // 从 id_token 中解析角色信息
          const { roles } = parseTokenClaims(
            JSON.parse(
              Buffer.from(account.id_token.split(".")[1], "base64").toString()
            )
          );

          token.roles = roles;

          console.info("Token更新成功", {
            userId: token.userId,
            roles,
          });
        } catch (error) {
          console.error("JWT处理失败", { error });
          if (error instanceof AuthError) {
            throw error;
          }
          token.roles = [];
        }
      }
      return token as JWT;
    },
    async session({ session, token }) {
      console.info("Session回调开始", { userId: token.userId });

      try {
        if (session.user) {
          // 从 token 同步信息到 session
          session.user.id = token.userId || "";
          session.user.roles = token.roles || [];

          session.accessToken = token.accessToken;

          console.info("Session更新成功", {
            userId: session.user.id,
            roles: session.user.roles,
          });
        }
      } catch (error) {
        console.error("Session处理失败", { error });
        if (error instanceof AuthError) {
          throw error;
        }
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error", // 添加错误页面
  },
  events: {
    signIn({ user, account, isNewUser }) {
      console.info("用户登录成功", {
        name: user.name,
        email: user.email,
        provider: account?.provider,
        isNewUser,
        timestamp: new Date().toISOString(),
      });
    },
    signOut(message) {
      // NextAuth.js v5 中 signOut 事件的参数可能包含 session 或 token
      // 由于类型限制，我们简化日志记录
      console.info("用户退出登录", {
        timestamp: new Date().toISOString(),
        hasSession: "session" in message && !!message.session,
        hasToken: "token" in message && !!message.token,
      });
    },
    async session({ session }) {
      console.debug("Session更新", {
        user: session?.user?.name,
        timestamp: new Date().toISOString(),
      });
    },
    createUser({ user }) {
      console.info("新用户创建", {
        name: user.name,
        email: user.email,
        timestamp: new Date().toISOString(),
      });
    },
  },
  useSecureCookies: process.env.NODE_ENV === "production",
  secret: process.env.AUTH_SECRET,
});
