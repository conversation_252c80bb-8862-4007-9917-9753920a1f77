/**
 * 应用程序常量配置
 */

/** 分页相关常量 */
export const PAGINATION = {
  /** 默认页码 */
  DEFAULT_PAGE: 1,
  /** 默认每页大小 */
  DEFAULT_PAGE_SIZE: 10,
  /** 最大每页大小 */
  MAX_PAGE_SIZE: 100,
  /** 首页工具显示数量 */
  HOME_PAGE_SIZE: 30,
  /** 首页最大工具数量 */
  HOME_MAX_TOOLS: 100,
  /** 首页分类工具显示阈值 */
  HOME_CATEGORY_THRESHOLD: 30,
  /** 首页每个分类显示的工具数量 */
  HOME_CATEGORY_TOOLS_COUNT: 30,
  /** 分类页面每页大小 */
  CATEGORY_PAGE_SIZE: 50,
  /** 搜索页面每页大小 */
  SEARCH_PAGE_SIZE: 50,
  /** 推荐工具每页大小 */
  RECOMMENDED_PAGE_SIZE: 10,
  /** 可选的分页大小选项 */
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100] as const,
  /** 分页大小选择器标签 */
  PAGE_SIZE_LABELS: {
    10: "10条/页",
    20: "20条/页",
    50: "50条/页",
    100: "100条/页",
  } as const,
} as const;

/** 数据库查询相关常量 */
export const DATABASE = {
  /** 查询模式 */
  QUERY_MODE: {
    /** 不区分大小写 */
    INSENSITIVE: "insensitive",
  },
  /** 排序方式 */
  ORDER_BY: {
    /** 按创建时间降序 */
    CREATED_DESC: "desc",
    /** 按创建时间升序 */
    CREATED_ASC: "asc",
  },
} as const;

/** 缓存相关常量 */
export const CACHE = {
  /** 仪表盘数据刷新间隔（毫秒） */
  DASHBOARD_REFRESH_INTERVAL: 5 * 60 * 1000, // 5分钟
} as const;

/** 路由相关常量 */
export const ROUTES = {
  /** 首页 */
  HOME: "/",
  /** 管理后台 */
  ADMIN: "/admin",
  /** 管理后台仪表盘 */
  ADMIN_DASHBOARD: "/admin/dashboard",
  /** 登录页面 */
  SIGNIN: "/api/auth/signin",
  /** 搜索页面 */
  SEARCH: "/search",
  /** 分类页面 */
  CATEGORY: "/category",
} as const;

/** 环境变量相关常量 */
export const ENV = {
  /** 生产环境 */
  PRODUCTION: "production",
  /** 开发环境 */
  DEVELOPMENT: "development",
  /** 构建阶段 */
  BUILD_PHASE: "phase-production-build",
} as const;

/** 消息常量 */
export const MESSAGES = {
  /** 加载相关 */
  LOADING: {
    /** 正在加载工具列表 */
    TOOLS: "正在加载工具列表，请稍候...",
    /** 正在加载仪表盘 */
    DASHBOARD: "正在加载仪表盘...",
    /** 图表加载中 */
    CHART: "图表加载中...",
    /** 正在刷新数据 */
    REFRESHING: "正在刷新数据...",
  },
  /** 错误相关 */
  ERROR: {
    /** 加载失败 */
    LOAD_FAILED: "加载失败",
    /** 无法加载工具列表 */
    TOOLS_LOAD_FAILED: "无法加载工具列表。请稍后重试或联系管理员。",
    /** 获取仪表盘数据失败 */
    DASHBOARD_LOAD_FAILED: "加载仪表盘数据失败。请稍后重试或联系管理员。",
    /** 未知错误 */
    UNKNOWN: "发生未知错误",
  },
  /** 空状态相关 */
  EMPTY: {
    /** 暂无工具 */
    NO_TOOLS: "暂无工具",
    /** 当前没有可用的工具 */
    NO_TOOLS_AVAILABLE: "当前没有可用的工具，请稍后再试。",
    /** 暂无图表数据 */
    NO_CHART_DATA: "暂无图表数据。",
    /** 没有可显示的仪表盘数据 */
    NO_DASHBOARD_DATA: "没有可显示的仪表盘数据。",
  },
  /** 成功相关 */
  SUCCESS: {
    /** 操作成功 */
    OPERATION_SUCCESS: "操作成功",
  },
} as const;

/** 分类相关常量 */
export const CATEGORY = {
  /** 未分类标识 */
  UNCATEGORIZED: "uncategorized",
  /** 未分类名称 */
  UNCATEGORIZED_NAME: "未分类",
} as const;

/** 时间相关常量 */
export const TIME = {
  /** 最近工具统计天数 */
  RECENT_TOOLS_DAYS: 30,
} as const;

/** CSS类名常量 */
export const CSS_CLASSES = {
  /** 滚动偏移 */
  SCROLL_OFFSET: "scroll-mt-20",
  /** 动画脉冲 */
  ANIMATE_PULSE: "animate-pulse",
  /** 网格布局 */
  GRID: {
    /** 单列 */
    SINGLE: "grid-cols-1",
    /** 中等屏幕双列 */
    MD_DOUBLE: "md:grid-cols-2",
    /** 大屏幕三列 */
    LG_TRIPLE: "lg:grid-cols-3",
    /** 大屏幕四列 */
    LG_QUAD: "lg:grid-cols-4",
  },
  /** 响应式容器 */
  CONTAINER: {
    /** 基础容器 */
    BASE: "container mx-auto",
    /** 带内边距的容器 */
    WITH_PADDING: "container mx-auto px-4 sm:px-6 lg:px-8",
  },
  /** 响应式间距 */
  SPACING: {
    /** 页面垂直间距 */
    PAGE_Y: "py-6 sm:py-8 lg:py-12",
    /** 卡片间距 */
    CARD_GAP: "gap-4 sm:gap-6",
    /** 按钮间距 */
    BUTTON_GAP: "gap-2 sm:gap-3",
  },
} as const;

/** 性能优化常量 */
export const PERFORMANCE = {
  /** 图片懒加载阈值 */
  IMAGE_LAZY_THRESHOLD: "200px",
  /** 虚拟滚动项目高度 */
  VIRTUAL_ITEM_HEIGHT: 120,
  /** 分页加载阈值 */
  PAGINATION_THRESHOLD: 50,
} as const;

/** 管理后台相关常量 */
export const ADMIN = {
  /** 默认分页大小 */
  DEFAULT_PAGE_SIZE: 12,
} as const;
