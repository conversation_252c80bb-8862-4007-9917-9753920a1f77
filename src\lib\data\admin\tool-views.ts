import { prisma } from "@/lib/prisma";
import { Prisma } from "@prisma/client";
import { DATABASE, ADMIN } from "@/lib/constants/app.constants";
import { unstable_noStore as noStore } from "next/cache";

interface GetToolViewsParams {
  page?: number;
  pageSize?: number;
  search?: string;
}

export async function getToolViews({
  page = 1,
  pageSize = ADMIN.DEFAULT_PAGE_SIZE,
  search,
}: GetToolViewsParams) {
  noStore(); // Opt out of caching for this function

  try {
    const skip = (page - 1) * pageSize;
    const take = pageSize;

    const where: Prisma.ToolWhereInput = {};
    if (search) {
      where.name = {
        contains: search,
        mode: DATABASE.QUERY_MODE.INSENSITIVE,
      };
    }

    const tools = await prisma.tool.findMany({
      where,
      select: { id: true },
    });

    const toolIds = tools.map((tool) => tool.id);

    if (search && toolIds.length === 0) {
      return {
        data: [],
        pagination: {
          page,
          pageSize,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrevious: false,
        },
      };
    }

    const viewAggregations = await prisma.toolView.groupBy({
      by: ["toolId"],
      where: {
        toolId: {
          in: toolIds.length > 0 ? toolIds : undefined,
        },
      },
      _count: {
        id: true,
      },
      _max: {
        viewedAt: true,
      },
      orderBy: {
        _max: {
          viewedAt: "desc",
        },
      },
      skip,
      take,
    });

    const totalResult = await prisma.toolView.groupBy({
      by: ["toolId"],
      where: {
        toolId: {
          in: toolIds.length > 0 ? toolIds : undefined,
        },
      },
    });
    const total = totalResult.length;

    const aggregatedToolIds = viewAggregations.map((agg) => agg.toolId);
    const toolDetails = await prisma.tool.findMany({
      where: {
        id: {
          in: aggregatedToolIds,
        },
      },
    });

    const toolMap = new Map(toolDetails.map((tool) => [tool.id, tool]));

    const data = viewAggregations
      .map((agg) => ({
        tool: toolMap.get(agg.toolId),
        viewCount: agg._count.id,
        lastViewed: agg._max.viewedAt,
      }))
      .filter(
        (
          item
        ): item is {
          tool: NonNullable<typeof item.tool>;
          viewCount: number;
          lastViewed: Date | null;
        } => item.tool != null
      );

    const totalPages = Math.ceil(total / pageSize);

    return {
      data,
      pagination: {
        page,
        pageSize,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrevious: page > 1,
      },
    };
  } catch (error) {
    console.error("[DATA_FETCH_ERROR] getToolViews:", error);
    // In case of an error, return a default empty state
    return {
      data: [],
      pagination: {
        page,
        pageSize,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrevious: false,
      },
    };
  }
}
