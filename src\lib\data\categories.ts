import { cache } from "react";
import prisma from "@/lib/prisma";
import "server-only";
import { ENV } from "@/lib/constants/app.constants";

/**
 * Fetches a single category by its slug.
 * This function is cached to prevent multiple database queries for the same category
 * within a single request-response lifecycle.
 * @param slug The slug of the category to fetch.
 * @returns The category object or null if not found.
 * @throws Error if prisma query fails.
 */
export const getCategoryBySlug = cache(async (slug: string) => {
  console.log(`[Cache Check] Attempting to fetch category by slug: ${slug}`);
  try {
    const category = await prisma.category.findUnique({
      where: { slug },
    });
    if (category) {
      console.log(
        `[Cache Check] Fetched category by slug: ${slug} successfully (from DB or cache).`
      );
    } else {
      console.log(`[Cache Check] Category with slug: ${slug} not found.`);
    }
    return category;
  } catch (error) {
    console.error(`Failed to fetch category by slug ${slug}:`, error);
    throw new Error(`Database error while fetching category slug ${slug}.`);
  }
});

/**
 * @deprecated Use getCategoryBySlug instead
 */
export const getCategoryById = cache(async (categoryId: string) => {
  return getCategoryBySlug(categoryId);
});

/**
 * Fetches a single category by its slug, including its associated tools.
 * This function is cached.
 * @param slug The slug of the category to fetch.
 * @returns The category object with tools or null if not found.
 * @throws Error if prisma query fails.
 */
export const getCategoryWithToolsBySlug = cache(async (slug: string) => {
  console.log(
    `[Cache Check] Attempting to fetch category with tools by slug: ${slug}`
  );
  try {
    const category = await prisma.category.findUnique({
      where: { slug },
      include: {
        tools: true,
      },
    });
    if (category) {
      console.log(
        `[Cache Check] Fetched category with tools by slug: ${slug} successfully (from DB or cache).`
      );
    } else {
      console.log(`[Cache Check] Category with slug: ${slug} not found.`);
    }
    return category;
  } catch (error) {
    console.error(`Failed to fetch category ${slug} with tools:`, error);
    throw new Error(
      `Database error while fetching category slug ${slug} with tools.`
    );
  }
});

/**
 * @deprecated Use getCategoryWithToolsBySlug instead
 */
export const getCategoryWithToolsById = cache(async (categoryId: string) => {
  return getCategoryWithToolsBySlug(categoryId);
});

/**
 * @typedef {object} CategoryListItem
 * @property {string} id
 * @property {string} name
 * @property {string} slug
 * @property {string | null} [parentId]
 * @property {string} [description]
 * @property {string} [icon]
 * @property {Date} createdAt - Prisma typically returns Date objects
 * @property {CategoryListItem[]} [children] - For potential tree structures
 */

export interface CategoryListItem {
  id: string;
  name: string;
  slug: string;
  parentId?: string | null;
  description?: string | null;
  icon?: string | null;
  order: number;
  createdAt: Date;
}

/**
 * Fetches all categories, ordered by creation date.
 * This function is cached.
 * @returns {Promise<CategoryListItem[]>} A list of all categories.
 * @throws Error if prisma query fails.
 */
export const getAllCategories = cache(async (): Promise<CategoryListItem[]> => {
  // 在构建时返回空数组，避免数据库连接错误
  if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
    console.log("Skipping categories fetch during build phase");
    return [];
  }

  console.log(
    `[Cache Check] Attempting to fetch all categories at ${new Date().toISOString()}`
  );

  try {
    const categories = await prisma.category.findMany({
      orderBy: [
        {
          order: "asc",
        },
        {
          createdAt: "desc",
        },
      ],
    });
    console.log(
      `[Cache Check] Fetched all categories successfully at ${new Date().toISOString()}`
    );
    return categories.map((category) => ({
      ...category,
      slug: category.slug ?? "",
      createdAt: new Date(category.createdAt),
      description: category.description ?? undefined,
      icon: category.icon ?? undefined,
      parentId: category.parentId ?? undefined,
    }));
  } catch (error) {
    console.error("Failed to fetch all categories:", error);
    // 在生产构建时返回空数组而不是抛出错误
    if (process.env.NODE_ENV === "production") {
      return [];
    }
    throw new Error("Database error while fetching all categories.");
  }
});
