import { cache } from "react";
import prisma from "@/lib/prisma"; // 使用已配置的 Prisma 实例
import "server-only";
import { TIME, DATABASE } from "@/lib/constants/app.constants";

/**
 * 仪表盘统计数据接口
 */
export interface DashboardStats {
  /** 分类总数 */
  categoriesCount: number;
  /** 工具总数 */
  toolsCount: number;
  /** 最近新增工具数 */
  recentToolsCount: number;
  /** 总访问量 */
  totalViews: number;
}

/**
 * 分类分布数据接口
 */
export interface CategoryDistribution {
  /** 分类名称 */
  name: string;
  /** 工具数量 */
  count: number;
}

/**
 * 仪表盘数据接口
 */
export interface DashboardData {
  /** 统计数据 */
  stats: DashboardStats;
  /** 分类分布数据 */
  distribution: CategoryDistribution[];
}

/**
 * Fetches dashboard statistics and tool distribution data from the database.
 * This function is cached using React `cache` to prevent redundant database queries
 * within the same request lifecycle when called multiple times on the server.
 *
 * @returns {Promise<DashboardData | null>} The dashboard data or null if an error occurs.
 * @throws Error if prisma query fails at a deeper level.
 */
export const getDashboardData = cache(
  async (): Promise<DashboardData | null> => {
    console.log(
      `[Cache Check] Attempting to fetch dashboard data at ${new Date().toISOString()}`
    );
    try {
      const now = new Date();
      const lastMonth = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() - TIME.RECENT_TOOLS_DAYS
      );

      const [
        categoriesCount,
        toolsCount,
        recentToolsCount,
        totalViewsData,
        categoryWithTools,
      ] = await Promise.all([
        prisma.category.count(),
        prisma.tool.count(),
        prisma.tool.count({
          where: {
            createdAt: {
              gte: lastMonth,
            },
          },
        }),
        prisma.tool.aggregate({
          _sum: {
            views: true,
          },
        }),
        prisma.category.findMany({
          include: {
            _count: {
              select: { tools: true },
            },
          },
          orderBy: {
            tools: {
              _count: DATABASE.ORDER_BY.CREATED_DESC, // 按工具数量排序
            },
          },
        }),
      ]);

      const distribution = categoryWithTools.map((category) => ({
        name: category.name,
        count: category._count.tools,
      }));

      const dashboardData: DashboardData = {
        stats: {
          categoriesCount,
          toolsCount,
          recentToolsCount,
          totalViews: totalViewsData._sum.views || 0,
        },
        distribution,
      };

      console.log(
        `[Cache Check] Fetched dashboard data successfully at ${new Date().toISOString()}`,
        `Stats: ${JSON.stringify(dashboardData.stats)}`
      );

      return dashboardData;
    } catch (error) {
      console.error("Failed to fetch dashboard data directly:", error);
      // 返回 null 让调用者处理错误情况
      return null;
    }
  }
);
