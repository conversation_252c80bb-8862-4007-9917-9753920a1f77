import { cache } from "react";
import prisma from "@/lib/prisma";
import "server-only";
import { Prisma } from "@prisma/client";
import { DATABASE, ENV, PAGINATION } from "@/lib/constants/app.constants";

/**
 * Fetches a single tool by its ID, including its associated category.
 * This function is cached to prevent multiple database queries for the same tool
 * within a single request-response lifecycle.
 * @param toolId The ID of the tool to fetch.
 * @returns The tool object with its category or null if not found.
 * @throws Error if prisma query fails.
 */
export const getToolById = cache(async (toolId: string) => {
  console.log(`[Cache Check] Attempting to fetch tool by ID: ${toolId}`);
  try {
    const tool = await prisma.tool.findUnique({
      where: { id: toolId },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
    if (tool) {
      console.log(
        `[Cache Check] Fetched tool by ID: ${toolId} successfully (from DB or cache).`
      );
    } else {
      console.log(`[Cache Check] Tool with ID: ${toolId} not found.`);
    }
    return tool;
  } catch (error) {
    console.error(`Failed to fetch tool by ID ${toolId}:`, error);
    throw new Error(`Database error while fetching tool ID ${toolId}.`);
  }
});

/**
 * @typedef {object} ToolWithCategory
 * @property {string} id
 * @property {string} name
 * @property {string} description
 * @property {string} url
 * @property {string | null} [iconUrl]
 * @property {string | null} [website]
 * @property {string[]} tags
 * @property {boolean} isFree
 * @property {boolean} isFeatured
 * @property {string} categoryId
 * @property {Date} createdAt
 * @property {{id: string, name: string} | null} category - The associated category
 */

export interface ToolWithCategory {
  id: string;
  name: string;
  description: string;
  url: string;
  iconUrl?: string | null;
  website?: string | null;
  tags: string[];
  isFree: boolean;
  isFeatured: boolean;
  categoryId: string;
  createdAt: Date;
  category: {
    id: string;
    name: string;
    slug: string;
  } | null;
}

interface GetPaginatedToolsParams {
  page?: number;
  pageSize?: number;
  search?: string;
  categoryId?: string;
  isFeatured?: boolean;
}

interface PaginatedToolsResponse {
  tools: ToolWithCategory[];
  totalCount: number;
}

/**
 * 构建工具查询条件
 * @param params - 查询参数
 * @returns Prisma查询条件对象
 */
function buildToolsWhereCondition(params: {
  search?: string;
  categoryId?: string;
  isFeatured?: boolean;
}): Prisma.ToolWhereInput {
  const { search, categoryId, isFeatured } = params;
  const where: Prisma.ToolWhereInput = {};

  if (search) {
    where.OR = [
      { name: { contains: search, mode: DATABASE.QUERY_MODE.INSENSITIVE } },
      {
        description: {
          contains: search,
          mode: DATABASE.QUERY_MODE.INSENSITIVE,
        },
      },
      { tags: { hasSome: [search] } },
    ];
  }

  if (categoryId) {
    where.categoryId = categoryId;
  }

  if (isFeatured !== undefined) {
    where.isFeatured = isFeatured;
  }

  // 确保categoryId不为null
  if (where.categoryId) {
    if (typeof where.categoryId === "string") {
      where.categoryId = { equals: where.categoryId, not: null };
    } else {
      (where.categoryId as Prisma.StringNullableFilter).not = null;
    }
  } else {
    where.categoryId = { not: null };
  }

  return where;
}

/**
 * 转换工具数据格式
 * @param toolsData - 原始工具数据
 * @returns 格式化后的工具数据
 */
function transformToolsData(
  toolsData: Array<{
    id: string;
    name: string;
    description: string;
    url: string;
    iconUrl: string | null;
    website: string | null;
    tags: string[];
    isFree: boolean;
    isFeatured: boolean;
    categoryId: string | null;
    createdAt: Date;
    category: {
      id: string;
      name: string;
      slug: string;
    } | null;
  }>
): ToolWithCategory[] {
  const validTools = toolsData.filter((tool) => {
    if (tool.category && !tool.category.slug) {
      console.warn(
        `Category ${tool.category.id} for tool ${tool.id} is missing required slug`
      );
    }
    return true;
  });

  return validTools.map((tool) => ({
    ...tool,
    categoryId: tool.categoryId as string,
    createdAt: new Date(tool.createdAt),
    category: tool.category
      ? {
          id: tool.category.id,
          name: tool.category.name,
          slug: tool.category.slug || "",
        }
      : null,
  }));
}

/**
 * Fetches tools with their associated category names, supporting pagination and filtering.
 * This function is cached.
 * @param params - Parameters for pagination and filtering.
 * @returns {Promise<PaginatedToolsResponse>} A list of tools with their category info and total count.
 * @throws Error if prisma query fails.
 */
export const getPaginatedToolsWithCategory = cache(
  async (
    params: GetPaginatedToolsParams = {}
  ): Promise<PaginatedToolsResponse> => {
    // 在构建时返回空数组，避免数据库连接错误
    if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
      console.log(
        "Skipping database fetch during build phase for getPaginatedToolsWithCategory"
      );
      return { tools: [], totalCount: 0 };
    }

    console.log(
      `[Cache Check] Attempting to fetch tools with categories (paginated) at ${new Date().toISOString()}`,
      params
    );

    const {
      page = PAGINATION.DEFAULT_PAGE,
      pageSize = PAGINATION.DEFAULT_PAGE_SIZE,
      search,
      categoryId,
      isFeatured,
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const where = buildToolsWhereCondition({ search, categoryId, isFeatured });

    try {
      const [toolsData, totalCount] = await prisma.$transaction([
        prisma.tool.findMany({
          where,
          include: {
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: {
            createdAt: DATABASE.ORDER_BY.CREATED_DESC,
          },
          skip,
          take,
        }),
        prisma.tool.count({ where }),
      ]);

      console.log(
        `[Cache Check] Fetched tools with categories (paginated) successfully at ${new Date().toISOString()}`
      );

      return {
        tools: transformToolsData(toolsData),
        totalCount,
      };
    } catch (error) {
      console.error(
        "Failed to fetch tools with categories (paginated):",
        error
      );
      if (process.env.NODE_ENV === ENV.PRODUCTION) {
        return { tools: [], totalCount: 0 };
      }
      throw new Error(
        "Database error while fetching tools with categories (paginated)."
      );
    }
  }
);

/**
 * 根据分类slug获取分类信息
 * @param slug - 分类slug
 * @returns 分类信息或null
 */
export const getCategoryBySlug = cache(async (slug: string) => {
  if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
    console.log(
      "Skipping database fetch during build phase for getCategoryBySlug"
    );
    return null;
  }

  try {
    const category = await prisma.category.findUnique({
      where: { slug },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
      },
    });

    return category;
  } catch (error) {
    console.error(`Failed to fetch category by slug ${slug}:`, error);
    return null;
  }
});

/**
 * 获取分类下的工具总数
 * @param categoryId - 分类ID
 * @returns 工具总数
 */
export const getToolsCountByCategory = cache(
  async (categoryId: string): Promise<number> => {
    if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
      return 0;
    }

    try {
      const count = await prisma.tool.count({
        where: {
          categoryId: { equals: categoryId, not: null },
        },
      });

      return count;
    } catch (error) {
      console.error(`Failed to count tools for category ${categoryId}:`, error);
      return 0;
    }
  }
);

/**
 * 按分类获取工具列表，每个分类获取指定数量的工具
 * @param categoriesWithLimit - 分类ID和每个分类要获取的工具数量的映射
 * @returns 按分类分组的工具列表
 */
export const getToolsByCategories = cache(
  async (
    categoriesWithLimit: Array<{ categoryId: string; limit: number }>
  ): Promise<Record<string, ToolWithCategory[]>> => {
    // 在构建时返回空对象，避免数据库连接错误
    if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
      console.log(
        "Skipping database fetch during build phase for getToolsByCategories"
      );
      return {};
    }

    console.log(
      `[Cache Check] Attempting to fetch tools by categories at ${new Date().toISOString()}`,
      categoriesWithLimit
    );

    try {
      const results: Record<string, ToolWithCategory[]> = {};

      // 并行获取每个分类的工具
      const promises = categoriesWithLimit.map(
        async ({ categoryId, limit }) => {
          const toolsData = await prisma.tool.findMany({
            where: { categoryId },
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
            orderBy: {
              createdAt: DATABASE.ORDER_BY.CREATED_DESC,
            },
            take: limit,
          });

          return { categoryId, tools: transformToolsData(toolsData) };
        }
      );

      const categoryResults = await Promise.all(promises);

      // 将结果组织成按分类ID索引的对象
      categoryResults.forEach(({ categoryId, tools }) => {
        results[categoryId] = tools;
      });

      console.log(
        `[Cache Check] Fetched tools by categories successfully at ${new Date().toISOString()}`
      );

      return results;
    } catch (error) {
      console.error("Failed to fetch tools by categories:", error);
      if (process.env.NODE_ENV === ENV.PRODUCTION) {
        return {};
      }
      throw new Error("Database error while fetching tools by categories.");
    }
  }
);

/**
 * 获取推荐工具列表（分页）
 * @param params - 分页参数
 * @returns 推荐工具列表和总数
 */
export const getPaginatedRecommendedTools = cache(
  async (
    params: { page?: number; pageSize?: number } = {}
  ): Promise<PaginatedToolsResponse> => {
    // 在构建时返回空数组，避免数据库连接错误
    if (process.env.NEXT_PHASE === ENV.BUILD_PHASE) {
      console.log(
        "Skipping database fetch during build phase for getPaginatedRecommendedTools"
      );
      return { tools: [], totalCount: 0 };
    }

    console.log(
      `[Cache Check] Attempting to fetch recommended tools (paginated) at ${new Date().toISOString()}`,
      params
    );

    const {
      page = PAGINATION.DEFAULT_PAGE,
      pageSize = PAGINATION.RECOMMENDED_PAGE_SIZE,
    } = params;

    const skip = (page - 1) * pageSize;
    const take = pageSize;
    const where: Prisma.ToolWhereInput = {
      isFeatured: true,
      categoryId: { not: null },
    };

    try {
      const [toolsData, totalCount] = await prisma.$transaction([
        prisma.tool.findMany({
          where,
          include: {
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
          orderBy: {
            createdAt: DATABASE.ORDER_BY.CREATED_DESC,
          },
          skip,
          take,
        }),
        prisma.tool.count({ where }),
      ]);

      console.log(
        `[Cache Check] Fetched recommended tools (paginated) successfully at ${new Date().toISOString()}`
      );

      return {
        tools: transformToolsData(toolsData),
        totalCount,
      };
    } catch (error) {
      console.error("Failed to fetch recommended tools (paginated):", error);
      if (process.env.NODE_ENV === ENV.PRODUCTION) {
        return { tools: [], totalCount: 0 };
      }
      throw new Error(
        "Database error while fetching recommended tools (paginated)."
      );
    }
  }
);
