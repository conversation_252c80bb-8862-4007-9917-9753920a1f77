/**
 * API 状态码常量
 */

// 成功
const SUCCESS = {
  code: 'SUCCESS',
  message: '操作成功',
} as const;

// 通用错误
const INTERNAL_ERROR = {
  code: 'INTERNAL_ERROR',
  message: '服务器内部错误',
} as const;

// 认证授权相关
const UNAUTHORIZED = {
  code: 'UNAUTHORIZED',
  message: '未授权访问',
} as const;

const FORBIDDEN = {
  code: 'FORBIDDEN',
  message: '权限不足',
} as const;

const INVALID_TOKEN = {
  code: 'INVALID_TOKEN',
  message: '无效的令牌',
} as const;

const PERMISSION_DENIED = {
  code: 'PERMISSION_DENIED',
  message: '没有操作权限',
} as const;

// 请求参数错误
const BAD_REQUEST = {
  code: 'BAD_REQUEST',
  message: '请求参数错误',
} as const;

const VALIDATION_ERROR = {
  code: 'VALIDATION_ERROR',
  message: '参数验证失败',
} as const;

// 资源相关
const NOT_FOUND = {
  code: 'NOT_FOUND',
  message: '资源不存在',
} as const;

const RESOURCE_EXISTS = {
  code: 'RESOURCE_EXISTS',
  message: '资源已存在',
} as const;

// 业务相关
const OPERATION_FAILED = {
  code: 'OPERATION_FAILED',
  message: '操作失败',
} as const;

// 导出所有状态码
export const API_CODES = {
  SUCCESS,
  INTERNAL_ERROR,
  UNAUTHORIZED,
  FORBIDDEN,
  INVALID_TOKEN,
  PERMISSION_DENIED,
  BAD_REQUEST,
  VALIDATION_ERROR,
  NOT_FOUND,
  RESOURCE_EXISTS,
  OPERATION_FAILED,
} as const;

/** API 状态码类型 */
export type ApiCode = keyof typeof API_CODES;

/**
 * 获取状态码对应的消息
 * @param code 状态码
 * @param customMessage 自定义消息，可选
 * @returns 返回状态码对应的消息或自定义消息
 */
export function getApiMessage(code: ApiCode, customMessage?: string): string {
  return customMessage || API_CODES[code]?.message || '未知错误';
}
