import prisma from "@/lib/prisma";
import type { Category } from "@prisma/client";
import type { MenuItem } from "@/types/global";

// Define a more specific type for categories with hierarchy
export interface CategoryWithHierarchy extends Category {
  /**
   * Icon identifier (e.g., SVG file name)
   */
  icon: string | null;
  /**
   * Child categories in the hierarchy
   */
  children: CategoryWithHierarchy[];
}

/**
 * Transforms a category and its children into a MenuItem structure.
 *
 * @param {CategoryWithHierarchy} category - The category to transform.
 * @returns {MenuItem} The transformed menu item.
 */
function transformCategoryToMenuItem(
  category: CategoryWithHierarchy
): MenuItem {
  if (!category.slug) {
    throw new Error(`Category ${category.id} is missing a slug`);
  }

  const menuItem: MenuItem = {
    id: category.slug, // 使用 slug 作为 id
    label: category.name,
    icon: category.icon || undefined, // Convert null to undefined
  };

  // 使用分类的 slug 作为锚点，用于页面内滚动
  menuItem.href = `#${category.slug}`;

  if (category.children && category.children.length > 0) {
    menuItem.children = category.children.map(transformCategoryToMenuItem);
  }

  return menuItem;
}

/**
 * Builds a hierarchical structure of categories from a flat list.
 *
 * @param {Category[]} categories - A flat list of categories from Prisma.
 * @returns {CategoryWithHierarchy[]} A hierarchical list of categories.
 */
function buildCategoryHierarchy(
  categories: Category[]
): CategoryWithHierarchy[] {
  const categoryMap = new Map<string, CategoryWithHierarchy>();
  const rootCategories: CategoryWithHierarchy[] = [];

  categories.forEach((category) => {
    categoryMap.set(category.id, {
      ...category,
      children: [],
    } as CategoryWithHierarchy);
  });

  categories.forEach((category) => {
    const currentCategoryNode = categoryMap.get(category.id)!;
    if (category.parentId && categoryMap.has(category.parentId)) {
      const parentNode = categoryMap.get(category.parentId)!;
      parentNode.children.push(currentCategoryNode);
    } else {
      rootCategories.push(currentCategoryNode);
    }
  });

  return rootCategories;
}

/**
 * Fetches all categories from the database, builds a hierarchical structure,
 * and transforms them into an array of MenuItems.
 *
 * @returns {Promise<MenuItem[]>} A promise that resolves to an array of menu items.
 * @throws Will throw an error if fetching or processing categories fails.
 */
export async function getMenuItems(): Promise<MenuItem[]> {
  try {
    const categories = await prisma.category.findMany({
      orderBy: [
        {
          order: "asc",
        },
        {
          name: "asc",
        },
      ],
    });

    const hierarchicalCategories = buildCategoryHierarchy(categories);
    return hierarchicalCategories.map(transformCategoryToMenuItem);
  } catch (error) {
    console.error("Failed to fetch and process menu items:", error);
    // Return empty array instead of crashing the UI
    return [];
  }
}
