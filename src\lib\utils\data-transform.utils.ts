/**
 * 数据转换工具函数
 */

import type { Tool } from "@prisma/client";
import type { ToolWithCategory } from "@/lib/data/tools";
import { CATEGORY } from "@/lib/constants/app.constants";

/**
 * 扩展的工具类型，包含所有必要的属性
 */
export type ExtendedTool = Tool & {
  category: { id: string; name: string; slug: string } | null;
  viewCount: number;
};

/**
 * 将工具按分类分组
 * @param tools - 工具列表
 * @returns 按分类分组的工具对象
 */
export function groupToolsByCategory(
  tools: ToolWithCategory[]
): Record<string, ToolWithCategory[]> {
  return tools.reduce<Record<string, ToolWithCategory[]>>((acc, tool) => {
    const categoryKey =
      tool.category?.slug || tool.category?.id || CATEGORY.UNCATEGORIZED;
    if (!acc[categoryKey]) {
      acc[categoryKey] = [];
    }
    acc[categoryKey].push(tool);
    return acc;
  }, {});
}

/**
 * 分类信息接口
 */
export interface CategoryInfo {
  id: string;
  name: string;
  slug: string;
  order: number;
}

/**
 * 将工具按分类分组并按分类排序排列
 * @param tools - 工具列表
 * @param categories - 分类列表（包含排序信息）
 * @returns 按分类排序的工具分组数组
 */
export function groupAndSortToolsByCategory(
  tools: ToolWithCategory[],
  categories: CategoryInfo[]
): Array<{
  categoryKey: string;
  categoryInfo: CategoryInfo | null;
  tools: ToolWithCategory[];
}> {
  // 先按分类分组
  const toolsByCategory = groupToolsByCategory(tools);

  // 创建分类映射，便于查找
  const categoryMap = new Map<string, CategoryInfo>();
  categories.forEach((category) => {
    categoryMap.set(category.slug, category);
    categoryMap.set(category.id, category);
  });

  // 转换为数组并添加分类信息
  const categoryGroups = Object.entries(toolsByCategory).map(
    ([categoryKey, categoryTools]) => {
      const categoryInfo = categoryMap.get(categoryKey) || null;
      return {
        categoryKey,
        categoryInfo,
        tools: categoryTools,
      };
    }
  );

  // 按分类的 order 字段排序
  categoryGroups.sort((a, b) => {
    const orderA = a.categoryInfo?.order ?? 999999; // 未分类的放在最后
    const orderB = b.categoryInfo?.order ?? 999999;
    return orderA - orderB;
  });

  return categoryGroups;
}

/**
 * 转换工具数据为扩展格式
 * @param tool - 原始工具数据
 * @returns 扩展格式的工具数据
 */
export function transformToExtendedTool(tool: ToolWithCategory): ExtendedTool {
  return {
    id: tool.id,
    name: tool.name,
    description: tool.description,
    url: tool.url,
    iconUrl: tool.iconUrl ?? null,
    website: tool.website ?? null,
    tags: tool.tags,
    isFree: tool.isFree,
    isFeatured: tool.isFeatured,
    views: 0,
    viewCount: 0,
    createdAt: tool.createdAt,
    updatedAt: new Date(tool.createdAt),
    categoryId: tool.categoryId,
    lastViewedAt: null, // 添加缺少的 lastViewedAt 属性
    category: tool.category
      ? {
          id: tool.category.id,
          name: tool.category.name,
          slug: tool.category.slug || tool.category.id,
        }
      : null,
  };
}

/**
 * 验证工具数据的完整性
 * @param tool - 工具数据
 * @returns 是否有效
 */
export function validateToolData(tool: ToolWithCategory): boolean {
  if (!tool.id || !tool.name || !tool.description || !tool.url) {
    return false;
  }

  if (tool.category && !tool.category.slug) {
    console.warn(
      `Category ${tool.category.id} for tool ${tool.id} is missing required slug`
    );
  }

  return true;
}

/**
 * 格式化工具标签
 * @param tags - 原始标签数组
 * @returns 格式化后的标签数组
 */
export function formatToolTags(tags: string[]): string[] {
  return tags
    .filter((tag) => tag && tag.trim().length > 0)
    .map((tag) => tag.trim().toLowerCase())
    .filter((tag, index, array) => array.indexOf(tag) === index); // 去重
}

/**
 * 生成工具的SEO友好URL
 * @param tool - 工具数据
 * @returns SEO友好的URL
 */
export function generateToolSeoUrl(tool: ToolWithCategory): string {
  const baseUrl = `/tool/${tool.id}`;
  const slug = tool.name
    .toLowerCase()
    .replace(/[^a-z0-9\u4e00-\u9fa5]/g, "-")
    .replace(/-+/g, "-")
    .replace(/^-|-$/g, "");

  return slug ? `${baseUrl}/${slug}` : baseUrl;
}

/**
 * 计算工具的相关性评分
 * @param tool - 工具数据
 * @param searchTerm - 搜索词
 * @returns 相关性评分 (0-100)
 */
export function calculateToolRelevance(
  tool: ToolWithCategory,
  searchTerm: string
): number {
  if (!searchTerm) return 0;

  const term = searchTerm.toLowerCase();
  let score = 0;

  // 名称匹配 (权重: 40)
  if (tool.name.toLowerCase().includes(term)) {
    score += 40;
    if (tool.name.toLowerCase() === term) {
      score += 20; // 完全匹配额外加分
    }
  }

  // 描述匹配 (权重: 30)
  if (tool.description.toLowerCase().includes(term)) {
    score += 30;
  }

  // 标签匹配 (权重: 20)
  const matchingTags = tool.tags.filter((tag) =>
    tag.toLowerCase().includes(term)
  );
  if (matchingTags.length > 0) {
    score += 20;
  }

  // 分类匹配 (权重: 10)
  if (tool.category?.name.toLowerCase().includes(term)) {
    score += 10;
  }

  return Math.min(score, 100);
}

/**
 * 过滤和排序工具列表
 * @param tools - 工具列表
 * @param options - 过滤和排序选项
 * @returns 过滤和排序后的工具列表
 */
export function filterAndSortTools(
  tools: ToolWithCategory[],
  options: {
    searchTerm?: string;
    isFree?: boolean;
    isFeatured?: boolean;
    categoryId?: string;
    sortBy?: "relevance" | "name" | "created" | "featured";
    sortOrder?: "asc" | "desc";
  } = {}
): ToolWithCategory[] {
  let filteredTools = tools.filter((tool) => {
    // 基本验证
    if (!validateToolData(tool)) return false;

    // 免费筛选
    if (options.isFree !== undefined && tool.isFree !== options.isFree) {
      return false;
    }

    // 推荐筛选
    if (
      options.isFeatured !== undefined &&
      tool.isFeatured !== options.isFeatured
    ) {
      return false;
    }

    // 分类筛选
    if (options.categoryId && tool.categoryId !== options.categoryId) {
      return false;
    }

    // 搜索词筛选
    if (options.searchTerm) {
      const relevance = calculateToolRelevance(tool, options.searchTerm);
      return relevance > 0;
    }

    return true;
  });

  // 排序
  const sortBy = options.sortBy || "created";
  const sortOrder = options.sortOrder || "desc";

  filteredTools.sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "relevance":
        if (options.searchTerm) {
          const aRelevance = calculateToolRelevance(a, options.searchTerm);
          const bRelevance = calculateToolRelevance(b, options.searchTerm);
          comparison = bRelevance - aRelevance;
        }
        break;
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "created":
        comparison =
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        break;
      case "featured":
        comparison = (b.isFeatured ? 1 : 0) - (a.isFeatured ? 1 : 0);
        break;
    }

    return sortOrder === "desc" ? comparison : -comparison;
  });

  return filteredTools;
}
