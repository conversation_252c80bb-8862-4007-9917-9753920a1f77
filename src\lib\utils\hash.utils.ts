/**
 * 文件哈希计算工具
 * 确保前后端hash计算的一致性
 */

/**
 * 前端计算文件hash（使用js-sha256）
 * @param file - 文件对象
 * @returns Promise<string> - 16位短hash
 */
export async function calculateClientFileHash(file: File): Promise<string> {
  // 动态导入js-sha256以避免服务器端错误
  const { sha256 } = await import("js-sha256");

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        const arrayBuffer = e.target?.result as ArrayBuffer;
        const hash = sha256.create();
        hash.update(new Uint8Array(arrayBuffer));
        // 取前16个字符作为短哈希（64位）
        const shortHash = hash.hex().substring(0, 16);
        resolve(shortHash);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
}

/**
 * 服务器端计算文件hash（使用Node.js crypto）
 * @param buffer - 文件内容缓冲区
 * @returns string - 16位短hash
 */
export function calculateServerFileHash(buffer: Buffer): string {
  // 在服务器端使用Node.js crypto模块
  if (typeof window === "undefined") {
    const { createHash } = require("crypto");
    const hash = createHash("sha256");
    hash.update(buffer);
    // 取前16个字符作为短哈希（64位），与前端保持一致
    return hash.digest("hex").substring(0, 16);
  }
  throw new Error(
    "calculateServerFileHash should only be called on server side"
  );
}

/**
 * 验证hash格式是否正确
 * @param hash - 哈希值
 * @returns boolean - 是否为有效的16位十六进制字符串
 */
export function isValidHash(hash: string): boolean {
  return /^[a-f0-9]{16}$/i.test(hash);
}

/**
 * 从文件URL中提取hash
 * @param url - 文件URL，格式如 /uploads/1234567890abcdef.jpg
 * @returns string | null - 提取的hash或null
 */
export function extractHashFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url, "http://localhost");
    const pathname = urlObj.pathname;
    const filename = pathname.split("/").pop();
    if (!filename) return null;

    const dotIndex = filename.lastIndexOf(".");
    if (dotIndex === -1) return null;

    const hash = filename.substring(0, dotIndex);
    return isValidHash(hash) ? hash : null;
  } catch {
    return null;
  }
}
