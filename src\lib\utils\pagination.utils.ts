import { PAGINATION } from "@/lib/constants/app.constants";

/**
 * 分页工具函数
 */

/**
 * 验证并标准化页码
 * @param page - 页码字符串或数字
 * @param defaultPage - 默认页码
 * @returns 标准化的页码
 */
export function validatePage(
  page: string | number | undefined,
  defaultPage = PAGINATION.DEFAULT_PAGE
): number {
  if (!page) return defaultPage;

  const pageNum = typeof page === "string" ? parseInt(page, 10) : page;

  if (isNaN(pageNum) || pageNum < 1) {
    return defaultPage;
  }

  return pageNum;
}

/**
 * 验证并标准化每页大小
 * @param pageSize - 每页大小字符串或数字
 * @param defaultPageSize - 默认每页大小
 * @returns 标准化的每页大小
 */
export function validatePageSize(
  pageSize: string | number | undefined,
  defaultPageSize: number = PAGINATION.DEFAULT_PAGE_SIZE
): number {
  if (!pageSize) return defaultPageSize;

  const pageSizeNum =
    typeof pageSize === "string" ? parseInt(pageSize, 10) : pageSize;

  if (isNaN(pageSizeNum) || pageSizeNum < 1) {
    return defaultPageSize;
  }

  // 限制在允许的选项范围内
  const validOptions = PAGINATION.PAGE_SIZE_OPTIONS as readonly number[];
  if (!validOptions.includes(pageSizeNum)) {
    // 如果不在预设选项中，返回最接近的有效选项
    return validOptions.reduce((prev, curr) =>
      Math.abs(curr - pageSizeNum) < Math.abs(prev - pageSizeNum) ? curr : prev
    );
  }

  return Math.min(pageSizeNum, PAGINATION.MAX_PAGE_SIZE);
}

/**
 * 计算分页信息
 * @param totalCount - 总记录数
 * @param currentPage - 当前页码
 * @param pageSize - 每页大小
 * @returns 分页信息对象
 */
export function calculatePaginationInfo(
  totalCount: number,
  currentPage: number,
  pageSize: number
) {
  const totalPages = Math.ceil(totalCount / pageSize);
  const startIndex = (currentPage - 1) * pageSize + 1;
  const endIndex = Math.min(currentPage * pageSize, totalCount);
  const hasNext = currentPage < totalPages;
  const hasPrevious = currentPage > 1;

  return {
    totalPages,
    startIndex,
    endIndex,
    hasNext,
    hasPrevious,
    isFirstPage: currentPage === 1,
    isLastPage: currentPage === totalPages,
  };
}

/**
 * 生成URL查询参数
 * @param params - 参数对象
 * @returns URL查询字符串
 */
export function buildPaginationQuery(params: {
  page?: number;
  pageSize?: number;
  search?: string;
  [key: string]: any;
}): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      // 对于默认值，不添加到URL中以保持URL简洁
      if (key === "page" && value === PAGINATION.DEFAULT_PAGE) return;
      if (key === "pageSize" && value === PAGINATION.DEFAULT_PAGE_SIZE) return;

      searchParams.set(key, String(value));
    }
  });

  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

/**
 * 解析URL查询参数为分页参数
 * @param searchParams - URL搜索参数
 * @param defaultPageSize - 默认每页大小
 * @returns 分页参数对象
 */
export function parsePaginationParams(
  searchParams:
    | URLSearchParams
    | { [key: string]: string | string[] | undefined },
  defaultPageSize: number = PAGINATION.DEFAULT_PAGE_SIZE
) {
  const getParam = (key: string): string | undefined => {
    if (searchParams instanceof URLSearchParams) {
      return searchParams.get(key) || undefined;
    }
    const value = searchParams[key];
    return typeof value === "string" ? value : undefined;
  };

  const page = validatePage(getParam("page"));
  const pageSize = validatePageSize(getParam("pageSize"), defaultPageSize);
  const search = getParam("search") || getParam("q") || "";

  return {
    page,
    pageSize,
    search,
  };
}

/**
 * 当页面大小改变时，调整当前页码以保持用户在相似的位置
 * @param currentPage - 当前页码
 * @param oldPageSize - 旧的每页大小
 * @param newPageSize - 新的每页大小
 * @param totalCount - 总记录数
 * @returns 调整后的页码
 */
export function adjustPageOnSizeChange(
  currentPage: number,
  oldPageSize: number,
  newPageSize: number,
  totalCount: number
): number {
  // 计算当前显示的第一个项目的索引
  const currentFirstItemIndex = (currentPage - 1) * oldPageSize;

  // 计算在新页面大小下应该在哪一页
  const newPage = Math.floor(currentFirstItemIndex / newPageSize) + 1;

  // 确保新页码不超过总页数
  const maxPage = Math.ceil(totalCount / newPageSize);

  return Math.min(Math.max(1, newPage), maxPage);
}

/**
 * 生成页码数组（用于分页导航）
 * @param currentPage - 当前页码
 * @param totalPages - 总页数
 * @param maxVisiblePages - 最大可见页码数
 * @returns 页码数组，包含数字和"ellipsis"
 */
export function generatePageNumbers(
  currentPage: number,
  totalPages: number,
  maxVisiblePages = 7
): (number | "ellipsis")[] {
  const pages: (number | "ellipsis")[] = [];

  if (totalPages <= maxVisiblePages) {
    // 如果总页数小于等于最大可见页数，显示所有页码
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // 复杂的分页逻辑
    if (currentPage <= 4) {
      // 当前页在前面
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push("ellipsis");
      pages.push(totalPages);
    } else if (currentPage >= totalPages - 3) {
      // 当前页在后面
      pages.push(1);
      pages.push("ellipsis");
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      pages.push(1);
      pages.push("ellipsis");
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i);
      }
      pages.push("ellipsis");
      pages.push(totalPages);
    }
  }

  return pages;
}
