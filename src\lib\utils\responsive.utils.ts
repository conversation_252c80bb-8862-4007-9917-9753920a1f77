/**
 * 响应式设计工具函数
 */

/**
 * 断点常量
 */
export const BREAKPOINTS = {
  /** 小屏幕 (手机) */
  SM: 640,
  /** 中等屏幕 (平板) */
  MD: 768,
  /** 大屏幕 (桌面) */
  LG: 1024,
  /** 超大屏幕 */
  XL: 1280,
  /** 2XL屏幕 */
  '2XL': 1536,
} as const;

/**
 * 响应式网格类名
 */
export const RESPONSIVE_GRID = {
  /** 工具卡片网格 */
  TOOL_CARDS: 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6',
  /** 仪表盘卡片网格 */
  DASHBOARD_CARDS: 'grid gap-4 md:grid-cols-2 lg:grid-cols-4',
  /** 分类网格 */
  CATEGORIES: 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 sm:gap-4',
  /** 搜索结果网格 */
  SEARCH_RESULTS: 'grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6',
} as const;

/**
 * 响应式间距类名
 */
export const RESPONSIVE_SPACING = {
  /** 容器内边距 */
  CONTAINER_PADDING: 'px-4 sm:px-6 lg:px-8',
  /** 页面内边距 */
  PAGE_PADDING: 'py-6 sm:py-8 lg:py-12',
  /** 卡片内边距 */
  CARD_PADDING: 'p-4 sm:p-6',
  /** 按钮内边距 */
  BUTTON_PADDING: 'px-3 py-2 sm:px-4 sm:py-2',
} as const;

/**
 * 响应式文字大小类名
 */
export const RESPONSIVE_TEXT = {
  /** 主标题 */
  HERO_TITLE: 'text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold',
  /** 页面标题 */
  PAGE_TITLE: 'text-2xl sm:text-3xl md:text-4xl font-bold',
  /** 卡片标题 */
  CARD_TITLE: 'text-lg sm:text-xl font-semibold',
  /** 正文 */
  BODY: 'text-sm sm:text-base',
  /** 小文字 */
  SMALL: 'text-xs sm:text-sm',
} as const;

/**
 * 检测当前屏幕尺寸类型
 * @param width - 屏幕宽度
 * @returns 屏幕尺寸类型
 */
export function getScreenSize(width: number): keyof typeof BREAKPOINTS {
  if (width >= BREAKPOINTS['2XL']) return '2XL';
  if (width >= BREAKPOINTS.XL) return 'XL';
  if (width >= BREAKPOINTS.LG) return 'LG';
  if (width >= BREAKPOINTS.MD) return 'MD';
  if (width >= BREAKPOINTS.SM) return 'SM';
  return 'SM'; // 默认为最小尺寸
}

/**
 * 根据屏幕尺寸获取网格列数
 * @param screenSize - 屏幕尺寸
 * @param type - 网格类型
 * @returns 列数
 */
export function getGridColumns(
  screenSize: keyof typeof BREAKPOINTS,
  type: 'tools' | 'categories' | 'dashboard' = 'tools'
): number {
  const columnMap = {
    tools: {
      SM: 1,
      MD: 2,
      LG: 3,
      XL: 4,
      '2XL': 4,
    },
    categories: {
      SM: 2,
      MD: 3,
      LG: 4,
      XL: 6,
      '2XL': 6,
    },
    dashboard: {
      SM: 1,
      MD: 2,
      LG: 4,
      XL: 4,
      '2XL': 4,
    },
  };

  return columnMap[type][screenSize];
}

/**
 * 生成响应式类名
 * @param baseClass - 基础类名
 * @param responsiveClasses - 响应式类名映射
 * @returns 完整的响应式类名字符串
 */
export function generateResponsiveClass(
  baseClass: string,
  responsiveClasses: Partial<Record<keyof typeof BREAKPOINTS, string>>
): string {
  const classes = [baseClass];

  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {
    if (className) {
      const prefix = breakpoint === 'SM' ? '' : `${breakpoint.toLowerCase()}:`;
      classes.push(`${prefix}${className}`);
    }
  });

  return classes.join(' ');
}

/**
 * 获取响应式图片尺寸
 * @param screenSize - 屏幕尺寸
 * @param type - 图片类型
 * @returns 图片尺寸对象
 */
export function getResponsiveImageSize(
  screenSize: keyof typeof BREAKPOINTS,
  type: 'avatar' | 'card' | 'hero' = 'card'
): { width: number; height: number } {
  const sizeMap = {
    avatar: {
      SM: { width: 32, height: 32 },
      MD: { width: 40, height: 40 },
      LG: { width: 48, height: 48 },
      XL: { width: 56, height: 56 },
      '2XL': { width: 64, height: 64 },
    },
    card: {
      SM: { width: 200, height: 120 },
      MD: { width: 250, height: 150 },
      LG: { width: 300, height: 180 },
      XL: { width: 350, height: 210 },
      '2XL': { width: 400, height: 240 },
    },
    hero: {
      SM: { width: 300, height: 200 },
      MD: { width: 500, height: 300 },
      LG: { width: 700, height: 400 },
      XL: { width: 900, height: 500 },
      '2XL': { width: 1200, height: 600 },
    },
  };

  return sizeMap[type][screenSize];
}

/**
 * 检查是否为移动设备
 * @param width - 屏幕宽度
 * @returns 是否为移动设备
 */
export function isMobile(width: number): boolean {
  return width < BREAKPOINTS.MD;
}

/**
 * 检查是否为平板设备
 * @param width - 屏幕宽度
 * @returns 是否为平板设备
 */
export function isTablet(width: number): boolean {
  return width >= BREAKPOINTS.MD && width < BREAKPOINTS.LG;
}

/**
 * 检查是否为桌面设备
 * @param width - 屏幕宽度
 * @returns 是否为桌面设备
 */
export function isDesktop(width: number): boolean {
  return width >= BREAKPOINTS.LG;
}

/**
 * 获取响应式容器最大宽度
 * @param screenSize - 屏幕尺寸
 * @returns 容器最大宽度类名
 */
export function getContainerMaxWidth(screenSize: keyof typeof BREAKPOINTS): string {
  const maxWidthMap = {
    SM: 'max-w-full',
    MD: 'max-w-3xl',
    LG: 'max-w-5xl',
    XL: 'max-w-7xl',
    '2XL': 'max-w-7xl',
  };

  return maxWidthMap[screenSize];
}

/**
 * 生成响应式网格类名
 * @param columns - 各断点的列数配置
 * @returns 响应式网格类名
 */
export function generateResponsiveGrid(columns: {
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
}): string {
  const classes = ['grid'];

  if (columns.sm) classes.push(`grid-cols-${columns.sm}`);
  if (columns.md) classes.push(`md:grid-cols-${columns.md}`);
  if (columns.lg) classes.push(`lg:grid-cols-${columns.lg}`);
  if (columns.xl) classes.push(`xl:grid-cols-${columns.xl}`);
  if (columns['2xl']) classes.push(`2xl:grid-cols-${columns['2xl']}`);

  return classes.join(' ');
}
