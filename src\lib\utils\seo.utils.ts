/**
 * SEO优化工具函数
 */

import type { Metadata } from "next";
import type { ToolWithCategory } from "@/lib/data/tools";

/**
 * SEO配置接口
 */
export interface SeoConfig {
  /** 页面标题 */
  title: string;
  /** 页面描述 */
  description: string;
  /** 关键词 */
  keywords?: string[];
  /** 页面URL */
  url?: string;
  /** 图片URL */
  image?: string;
  /** 页面类型 */
  type?: "website" | "article";
  /** 作者 */
  author?: string;
  /** 发布时间 */
  publishedTime?: string;
  /** 修改时间 */
  modifiedTime?: string;
}

/**
 * 默认SEO配置
 */
export const DEFAULT_SEO: SeoConfig = {
  title: "AI导航 - 发现最佳AI工具和资源",
  description:
    "欢迎来到 AI 工具导航！发现最新、最热门的AI应用与技术，探索各类智能工具，提升您的工作效率和创造力。",
  keywords: [
    "AI工具",
    "人工智能",
    "AI导航",
    "AI资源",
    "AI应用",
    "机器学习",
    "深度学习",
    "ChatGPT",
    "Midjourney",
    "AI写作",
    "AI图像生成",
  ],
  url: process.env.NEXT_PUBLIC_SITE_URL || "https://ai-nav.hnch.pro",
  image: "/og-image.png", // 更新为实际存在的图片
  type: "website",
  author: "AI导航团队",
};

/**
 * 生成页面元数据
 * @param config - SEO配置
 * @returns Next.js Metadata对象
 */
export function generateSeoMetadata(config: Partial<SeoConfig>): Metadata {
  const seoConfig = { ...DEFAULT_SEO, ...config };

  return {
    title: seoConfig.title,
    description: seoConfig.description,
    keywords: seoConfig.keywords?.join(", "),
    authors: seoConfig.author ? [{ name: seoConfig.author }] : undefined,

    // Open Graph
    openGraph: {
      title: seoConfig.title,
      description: seoConfig.description,
      url: seoConfig.url,
      siteName: "AI导航",
      images: seoConfig.image
        ? [
            {
              url: seoConfig.image,
              width: 1200,
              height: 630,
              alt: seoConfig.title,
            },
          ]
        : undefined,
      locale: "zh_CN",
      type: seoConfig.type || "website",
      publishedTime: seoConfig.publishedTime,
      modifiedTime: seoConfig.modifiedTime,
    },

    // Twitter Card
    twitter: {
      card: "summary_large_image",
      title: seoConfig.title,
      description: seoConfig.description,
      images: seoConfig.image ? [seoConfig.image] : undefined,
      creator: "@ai_nav",
    },

    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },

    // 结构化数据
    other: {
      "application-name": "AI导航",
      "apple-mobile-web-app-title": "AI导航",
      "format-detection": "telephone=no",
    },
  };
}

/**
 * 为工具页面生成SEO元数据
 * @param tool - 工具数据
 * @returns Next.js Metadata对象
 */
export function generateToolSeoMetadata(tool: ToolWithCategory): Metadata {
  const title = `${tool.name} - AI工具导航`;
  const description =
    tool.description.length > 160
      ? `${tool.description.substring(0, 157)}...`
      : tool.description;

  const keywords = [
    tool.name,
    ...tool.tags,
    tool.category?.name || "",
    "AI工具",
    "人工智能",
  ].filter(Boolean);

  return generateSeoMetadata({
    title,
    description,
    keywords,
    url: `${DEFAULT_SEO.url}/tool/${tool.id}`,
    image: tool.iconUrl || DEFAULT_SEO.image,
    type: "article",
    publishedTime: tool.createdAt.toISOString(),
    modifiedTime: tool.createdAt.toISOString(),
  });
}

/**
 * 为分类页面生成SEO元数据
 * @param category - 分类数据
 * @param toolCount - 工具数量
 * @returns Next.js Metadata对象
 */
export function generateCategorySeoMetadata(
  category: { id: string; name: string; description?: string; slug: string },
  toolCount: number
): Metadata {
  const title = `${category.name} - AI工具分类 | AI导航`;
  const description =
    category.description ||
    `发现${category.name}相关的AI工具和资源，共${toolCount}个优质工具等你探索。`;

  return generateSeoMetadata({
    title,
    description,
    keywords: [category.name, "AI工具", "人工智能", "工具分类"],
    url: `${DEFAULT_SEO.url}/${category.slug}`,
    type: "website",
  });
}

/**
 * 为搜索页面生成SEO元数据
 * @param query - 搜索关键词
 * @param resultCount - 搜索结果数量
 * @returns Next.js Metadata对象
 */
export function generateSearchSeoMetadata(
  query: string,
  resultCount: number
): Metadata {
  const title = `搜索"${query}" - AI导航`;
  const description = `搜索"${query}"相关的AI工具，找到${resultCount}个相关结果。`;

  return generateSeoMetadata({
    title,
    description,
    keywords: [query, "搜索", "AI工具", "人工智能"],
    url: `${DEFAULT_SEO.url}/search?q=${encodeURIComponent(query)}`,
    type: "website",
  });
}

/**
 * 生成结构化数据 (JSON-LD)
 * @param type - 数据类型
 * @param data - 数据内容
 * @returns JSON-LD字符串
 */
export function generateStructuredData(
  type: "WebSite" | "SoftwareApplication" | "BreadcrumbList",
  data: Record<string, unknown>
): string {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": type,
    ...data,
  };

  return JSON.stringify(structuredData);
}

/**
 * 为网站生成结构化数据
 * @returns 网站结构化数据
 */
export function generateWebsiteStructuredData(): string {
  return generateStructuredData("WebSite", {
    name: "AI导航",
    description: DEFAULT_SEO.description,
    url: DEFAULT_SEO.url,
    potentialAction: {
      "@type": "SearchAction",
      target: {
        "@type": "EntryPoint",
        urlTemplate: `${DEFAULT_SEO.url}/search?q={search_term_string}`,
      },
      "query-input": "required name=search_term_string",
    },
  });
}

/**
 * 为工具生成结构化数据
 * @param tool - 工具数据
 * @returns 工具结构化数据
 */
export function generateToolStructuredData(tool: ToolWithCategory): string {
  return generateStructuredData("SoftwareApplication", {
    name: tool.name,
    description: tool.description,
    url: tool.url,
    applicationCategory: tool.category?.name || "AI工具",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: tool.isFree ? "0" : undefined,
      priceCurrency: "CNY",
      availability: "https://schema.org/InStock",
    },
    aggregateRating: tool.isFeatured
      ? {
          "@type": "AggregateRating",
          ratingValue: "4.5",
          ratingCount: "100",
        }
      : undefined,
  });
}

/**
 * 生成面包屑导航结构化数据
 * @param breadcrumbs - 面包屑数据
 * @returns 面包屑结构化数据
 */
export function generateBreadcrumbStructuredData(
  breadcrumbs: Array<{ name: string; url: string }>
): string {
  const itemListElement = breadcrumbs.map((item, index) => ({
    "@type": "ListItem",
    position: index + 1,
    name: item.name,
    item: item.url,
  }));

  return generateStructuredData("BreadcrumbList", {
    itemListElement,
  });
}

/**
 * 优化页面标题
 * @param title - 原始标题
 * @param maxLength - 最大长度
 * @returns 优化后的标题
 */
export function optimizeTitle(title: string, maxLength: number = 60): string {
  if (title.length <= maxLength) return title;

  // 尝试在单词边界截断
  const truncated = title.substring(0, maxLength - 3);
  const lastSpace = truncated.lastIndexOf(" ");

  if (lastSpace > maxLength * 0.8) {
    return truncated.substring(0, lastSpace) + "...";
  }

  return truncated + "...";
}

/**
 * 优化页面描述
 * @param description - 原始描述
 * @param maxLength - 最大长度
 * @returns 优化后的描述
 */
export function optimizeDescription(
  description: string,
  maxLength: number = 160
): string {
  if (description.length <= maxLength) return description;

  // 尝试在句子边界截断
  const truncated = description.substring(0, maxLength - 3);
  const lastPeriod = truncated.lastIndexOf("。");
  const lastComma = truncated.lastIndexOf("，");
  const lastBreak = Math.max(lastPeriod, lastComma);

  if (lastBreak > maxLength * 0.8) {
    return truncated.substring(0, lastBreak + 1);
  }

  return truncated + "...";
}
