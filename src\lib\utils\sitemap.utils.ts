import prisma from "@/lib/prisma";
import redis from "@/lib/redis";

// 缓存键常量
export const SITEMAP_CACHE_KEYS = {
  SITEMAP: "sitemap:main",
  CATEGORIES: "sitemap:categories",
  TOOLS: "sitemap:tools",
  TAGS: "sitemap:tags",
  LAST_MODIFIED: "sitemap:last_modified",
  STATS: "sitemap:stats",
} as const;

// 缓存过期时间（秒）
export const SITEMAP_CACHE_TTL = {
  SITEMAP: 3600, // 1小时
  DATA: 1800, // 30分钟
  STATS: 600, // 10分钟
} as const;

// sitemap配置
export const SITEMAP_CONFIG = {
  MAX_ENTRIES_PER_FILE: 50000,
  TOOLS_PER_PAGE: 45000,
  MIN_TAG_COUNT: 2,
  MAX_TAGS: 50,
} as const;

/**
 * 计算工具优先级
 */
export function calculateToolPriority(tool: {
  views: number;
  isFeatured: boolean;
  lastViewedAt: Date | null;
}): number {
  let priority = 0.6; // 基础优先级

  // 精选工具优先级更高
  if (tool.isFeatured) {
    priority += 0.2;
  }

  // 根据浏览量调整优先级
  if (tool.views > 1000) {
    priority += 0.1;
  } else if (tool.views > 100) {
    priority += 0.05;
  }

  // 最近浏览过的工具优先级更高
  if (tool.lastViewedAt) {
    const daysSinceLastView =
      (Date.now() - tool.lastViewedAt.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceLastView < 7) {
      priority += 0.1;
    } else if (daysSinceLastView < 30) {
      priority += 0.05;
    }
  }

  return Math.min(priority, 1.0);
}

/**
 * 计算分类优先级
 */
export function calculateCategoryPriority(toolCount: number): number {
  let priority = 0.5; // 基础优先级

  if (toolCount > 50) {
    priority = 0.8;
  } else if (toolCount > 20) {
    priority = 0.7;
  } else if (toolCount > 5) {
    priority = 0.6;
  }

  return priority;
}

/**
 * 计算标签优先级
 */
export function calculateTagPriority(count: number): number {
  let priority = 0.3; // 基础优先级

  if (count > 20) {
    priority = 0.6;
  } else if (count > 10) {
    priority = 0.5;
  } else if (count > 5) {
    priority = 0.4;
  }

  return priority;
}

/**
 * 获取sitemap统计信息
 */
export async function getSitemapStats() {
  try {
    // 尝试从缓存获取
    const cached = await redis?.get(SITEMAP_CACHE_KEYS.STATS);
    if (cached) {
      return JSON.parse(cached);
    }

    // 获取数据库统计
    const [categoriesCount, toolsCount, lastModified] = await Promise.all([
      prisma.category.count(),
      prisma.tool.count(),
      redis?.get(SITEMAP_CACHE_KEYS.LAST_MODIFIED),
    ]);

    // 获取缓存状态
    const cacheStatus = await Promise.all([
      redis?.exists(SITEMAP_CACHE_KEYS.SITEMAP),
      redis?.exists(SITEMAP_CACHE_KEYS.CATEGORIES),
      redis?.exists(SITEMAP_CACHE_KEYS.TOOLS),
      redis?.exists(SITEMAP_CACHE_KEYS.TAGS),
    ]);

    // 计算预估的sitemap条目数
    const staticPages = 2;
    const estimatedTagsCount = 20;
    const estimatedEntries = staticPages + categoriesCount + toolsCount + estimatedTagsCount;

    const stats = {
      database: {
        categories: categoriesCount,
        tools: toolsCount,
        estimatedSitemapEntries: estimatedEntries,
      },
      cache: {
        lastModified: lastModified ? new Date(lastModified) : null,
        status: {
          sitemap: cacheStatus[0] ? "cached" : "not_cached",
          categories: cacheStatus[1] ? "cached" : "not_cached",
          tools: cacheStatus[2] ? "cached" : "not_cached",
          tags: cacheStatus[3] ? "cached" : "not_cached",
        },
      },
      config: {
        maxEntriesPerFile: SITEMAP_CONFIG.MAX_ENTRIES_PER_FILE,
        needsSplitting: estimatedEntries > SITEMAP_CONFIG.MAX_ENTRIES_PER_FILE,
        estimatedFiles: Math.ceil(estimatedEntries / SITEMAP_CONFIG.MAX_ENTRIES_PER_FILE),
      },
    };

    // 缓存结果
    if (redis) {
      await redis.setex(
        SITEMAP_CACHE_KEYS.STATS,
        SITEMAP_CACHE_TTL.STATS,
        JSON.stringify(stats)
      );
    }

    return stats;
  } catch (error) {
    console.error("Error getting sitemap stats:", error);
    return null;
  }
}

/**
 * 清除所有sitemap缓存
 */
export async function clearSitemapCache(): Promise<number> {
  if (!redis) {
    return 0;
  }

  try {
    const deletedKeys = await Promise.all([
      redis.del(SITEMAP_CACHE_KEYS.SITEMAP),
      redis.del(SITEMAP_CACHE_KEYS.CATEGORIES),
      redis.del(SITEMAP_CACHE_KEYS.TOOLS),
      redis.del(SITEMAP_CACHE_KEYS.TAGS),
      redis.del(SITEMAP_CACHE_KEYS.LAST_MODIFIED),
      redis.del(SITEMAP_CACHE_KEYS.STATS),
    ]);

    return deletedKeys.reduce((sum, count) => sum + count, 0);
  } catch (error) {
    console.error("Error clearing sitemap cache:", error);
    return 0;
  }
}

/**
 * 检查sitemap是否需要更新
 */
export async function shouldUpdateSitemap(): Promise<boolean> {
  try {
    const lastModified = await redis?.get(SITEMAP_CACHE_KEYS.LAST_MODIFIED);
    if (!lastModified) {
      return true;
    }

    const lastModifiedDate = new Date(lastModified);
    const now = new Date();
    const hoursSinceUpdate = (now.getTime() - lastModifiedDate.getTime()) / (1000 * 60 * 60);

    // 如果超过1小时没有更新，则需要更新
    return hoursSinceUpdate > 1;
  } catch (error) {
    console.error("Error checking sitemap update status:", error);
    return true;
  }
}

/**
 * 生成XML格式的URL条目
 */
export function generateXmlUrl(
  url: string,
  lastmod: string,
  changefreq: string,
  priority: string
): string {
  return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
}

/**
 * 生成完整的sitemap XML
 */
export function generateSitemapXml(urls: string[]): string {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.join("\n")}
</urlset>`;
}

/**
 * 生成sitemap索引XML
 */
export function generateSitemapIndexXml(sitemaps: Array<{ loc: string; lastmod: string }>): string {
  const sitemapEntries = sitemaps
    .map(
      (sitemap) => `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`
    )
    .join("\n");

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`;
}
