// src/middleware.ts
import { auth } from "@/lib/auth";
import { ROLES } from "@/lib/enums";
import { NextResponse } from "next/server";

export default auth((req) => {
  const { pathname } = req.nextUrl;
  const { auth: session } = req;

  // 检查管理员路由访问权限
  if (pathname.startsWith("/admin")) {
    // 如果访问的是 /admin 根路径，则重定向到 /admin/dashboard
    if (pathname === "/admin") {
      return Response.redirect(new URL("/admin/dashboard", req.url));
    }

    // 用户未登录，重定向到登录页面
    if (!session) {
      return Response.redirect(new URL("/api/auth/signin", req.url));
    }

    // 检查用户是否具有所需的角色
    const hasRequiredRole = session.user.roles?.includes(ROLES.ADMIN);

    // 如果用户没有所需权限，重定向到首页
    if (!hasRequiredRole) {
      return Response.redirect(new URL("/", req.url));
    }
  }

  return NextResponse.next();
});

// 配置 matcher 来匹配所有路径（除了静态文件和 API 路由）
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - uploads (uploaded files)
     */
    "/((?!api|_next/static|_next/image|favicon.ico|uploads).*)",
  ],
};
