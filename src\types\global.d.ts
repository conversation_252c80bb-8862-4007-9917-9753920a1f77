// src/types/global.d.ts
import { PrismaClient } from "@prisma/client";

declare global {
  var prisma: PrismaClient | undefined;
}

/**
 * @interface MenuItem
 * @description Represents an item in the side menu.
 *
 * @property {string} id - Unique identifier for the menu item.
 * @property {string} label - Text displayed for the menu item.
 * @property {string} [href] - Optional link for navigation when the item is clicked.
 * @property {React.ReactNode} [icon] - Optional icon for the menu item.
 * @property {MenuItem[]} [children] - Optional array of child menu items for multi-level menus.
 */
export interface MenuItem {
  id: string;
  label: string;
  href?: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export {};
