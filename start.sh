#!/bin/sh

echo "🚀 启动 AI Nav 应用..."

# 确保上传目录存在并设置权限
echo "📁 设置上传目录权限..."
mkdir -p /app/public/uploads
chown -R nextjs:nodejs /app/public/uploads
chmod -R 755 /app/public/uploads

# 检查上传目录状态
echo "🔍 检查上传目录状态..."
ls -la /app/public/uploads/ || echo "上传目录为空"

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
npx prisma migrate deploy

# 后台运行数据初始化脚本（不阻塞服务启动）
echo "📊 启动后台数据初始化..."
if [ -f "/app/scripts/addData.ts" ] && [ -f "/app/data.json" ]; then
    echo "🔄 在后台运行数据初始化脚本..."
    (
        # 等待服务启动完成
        sleep 10
        echo "🔄 开始执行数据初始化..."
        npx tsx /app/scripts/addData.ts && echo "✅ 数据初始化完成" || echo "⚠️ 数据初始化失败"
    ) &
else
    echo "ℹ️ 跳过数据初始化（脚本或数据文件不存在）"
fi

echo "✅ 启动 Next.js 服务器..."
# 使用 Next.js standalone 服务器（文件已复制到根目录）
exec node server.js
